import { ThemedText } from "@/components/ThemedText"
import { StyleSheet, View, FlatList } from "react-native"
import { TipReceiver } from "./tipping-party"
import { memo, useState, useCallback, useEffect } from "react"
import BackButton from "@/components/navigation/BackButton"
import { Colors } from "@/constants/Colors"
import Avatar from "@/components/ui/avatar"
import { CheckBox } from "@/components/ui/checkbox"

type TEditTipReceivers = {
    tipReceivers: TipReceiver[]
    setTipReceivers: (tipReceivers: TipReceiver[]) => void
    closeEditTipReceivers: () => void
    showTipsCount: boolean
}

type TTipReceiverItem = {
    item: TipReceiver
    setTipReceiversArray: React.Dispatch<React.SetStateAction<TipReceiver[]>>
    showTipsCount: boolean
}

const AVATAR_SIZE = 42

const TipReceiverItem = memo(({ item, setTipReceiversArray, showTipsCount }: TTipReceiverItem) => {
    const pictureSrc = item.traderUser.twitterPicture;

    const onValueChange = (value: boolean) => {
        setTipReceiversArray(
            prev => prev.map(
                tipReceiver => tipReceiver.id === item.id ?
                    { ...tipReceiver, isChecked: value } :
                    tipReceiver
            ))
    }

    return (
        <View style={styles.itemContainer}>
            <View style={styles.leftContainer}>
                {
                    pictureSrc ? (
                        <Avatar size={AVATAR_SIZE} src={pictureSrc} />
                    ) : (
                        <Avatar src={require("@assets/images/sample-profile-pic.png")} size={AVATAR_SIZE} />
                    )
                }
            </View>
            <View style={styles.middleContainer}>
                <ThemedText type="defaultGrey">@{item.traderUser?.twitterHandle}</ThemedText>
                {
                    showTipsCount ? (
                        <ThemedText style={styles.amountText}>{item.amount} {item.amount === 1 ? "tip" : "tips"}</ThemedText>
                    ): null
                }
            </View>
            <View style={styles.rightContainer}>
                <CheckBox value={item.isChecked} onValueChange={onValueChange} />
            </View>
        </View>
    )
}, (prevProps, nextProps) => {
    return (
        JSON.stringify(prevProps.item) === JSON.stringify(nextProps.item)
    )
})

export const EditTipReceivers = memo(({ tipReceivers, setTipReceivers, closeEditTipReceivers, showTipsCount }: TEditTipReceivers) => {

    const [tipReceiversArray, setTipReceiversArray] = useState<TipReceiver[]>(tipReceivers);

    const renderTipReceiverItem = useCallback(({ item }: { item: TipReceiver }) => {
        return <TipReceiverItem item={item} setTipReceiversArray={setTipReceiversArray} showTipsCount={showTipsCount} />
    }, [showTipsCount])

    useEffect(() => {
       setTipReceivers(tipReceiversArray)
    }, [tipReceiversArray])

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <BackButton onPress={closeEditTipReceivers} />
                <ThemedText style={styles.title}>Edit Tip Receivers</ThemedText>
            </View>
            <FlatList
                data={tipReceiversArray}
                renderItem={renderTipReceiverItem}
                keyExtractor={(item) => item.traderUser.twitterHandle}
                style={styles.list}
                ListEmptyComponent={() => (
                    <View style={styles.emptyContainer}>
                        <ThemedText type="defaultGrey">No tip receivers found</ThemedText>
                    </View>
                )}
            />
        </View>
    )
}, (prevProps, nextProps) => {
    return (
        JSON.stringify(prevProps.tipReceivers) === JSON.stringify(nextProps.tipReceivers) &&
        prevProps.setTipReceivers === nextProps.setTipReceivers &&
        prevProps.closeEditTipReceivers === nextProps.closeEditTipReceivers &&
        prevProps.showTipsCount === nextProps.showTipsCount
    )
})


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.dark.background,
        padding: 20
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        fontFamily: "InterSemiBold",
        color: Colors.dark.offWhite,
        marginLeft: 10
    },
    list: {
        flex: 1,
        marginVertical: 10
    },
    itemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20
    },
    leftContainer: {
        width: '15%',
    },
    middleContainer: {
        width: '70%',
    },
    rightContainer: {
        width: '15%',
        alignItems: 'flex-end'
    },
    amountText: {
        fontSize: 12,
        fontFamily: "InterSemiBold",
        color: Colors.dark.offWhite,
    },
    emptyContainer: {
        justifyContent: 'center',
        alignItems: 'center'
    }
})
