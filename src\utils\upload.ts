import * as FileSystem from "expo-file-system";
import { v4 as uuidv4 } from "uuid";

import { env } from "@/env";
import { axios } from "@/lib/axios";
import { getOAuthAccessToken } from "./get-x-token";

type OnProgressChangeHandler = (progress: number) => void;

export type UploadableAsset = {
  uri: string;
  fileName: string;
  mimeType: string;
  width?: number;
  height?: number;
  fileSize: number;
};

interface Params {
  file: any;
  onProgressChange?: OnProgressChangeHandler;
}

export async function upload(params: Params) {
  try {
    params.onProgressChange?.(0);

    const res = await uploadFile(params);
    return res;
  } catch (error) {
    throw error;
  }
}

interface UploadPolicyType {
  url: string;
  enctype: string;
  key: string;
  "x-goog-date": string;
  "x-goog-credential": string;
  "x-goog-algorithm": string;
  policy: string;
  "x-goog-signature": string;
}

async function uploadFile({ file, onProgressChange }: Params) {
  try {
    onProgressChange?.(0);
    const searchParams = new URLSearchParams({
      fileType: file.mimeType,
      fileName: file.fileName,
    });

    const res = await axios.get<{ uploadPolicy: UploadPolicyType }>(
      `/uploads/getUploadPolicy?${searchParams.toString()}`,
    );

    if (res.status !== 200) {
      throw new Error("Failed to get upload policy");
    }
    const uploadPolicy = res.data.uploadPolicy;
    await uploadFileInner(file, uploadPolicy, onProgressChange);

    return {
      id: uuidv4(),
      name: uploadPolicy.key,
      url: `https://static.starsarena.com/${uploadPolicy.key}`,
      createdOn: new Date(),
      path: `/${uploadPolicy.key}`,
      previewUrl: `https://static.starsarena.com/${uploadPolicy.key}`,
      size: file.fileSize,
    };
  } catch (e) {
    onProgressChange?.(0);
    throw e;
  }
}

const uploadFileInner = async (
  file: any,
  uploadPolicy: UploadPolicyType,
  onProgressChange?: OnProgressChangeHandler,
) => {
  const promise = new Promise<boolean | null>((resolve, reject) => {
    const formData = new FormData();

    formData.append("key", uploadPolicy.key);
    formData.append("x-goog-date", uploadPolicy["x-goog-date"]);
    formData.append("x-goog-credential", uploadPolicy["x-goog-credential"]);
    formData.append("x-goog-algorithm", uploadPolicy["x-goog-algorithm"]);
    formData.append("policy", uploadPolicy["policy"]);
    formData.append("x-goog-signature", uploadPolicy["x-goog-signature"]);

    formData.append("Content-Type", file.mimeType);
    formData.append("file", {
      uri: file.uri,
      name: file.fileName,
      type: file.mimeType,
    } as any);
    
    const request = new XMLHttpRequest();
    request.open("POST", uploadPolicy.url, true);
    request.addEventListener("loadstart", () => {
      onProgressChange?.(0);
    });
    request.upload.addEventListener("progress", (e) => {
      if (e.lengthComputable) {
        // 2 decimal progress
        const progress = Math.round((e.loaded / e.total) * 10000) / 100;
        onProgressChange?.(progress);
      }
    });
    request.addEventListener("error", () => {
      reject(new Error("Error uploading file"));
    });    
    request.addEventListener("abort", () => {
      reject(new Error("File upload aborted"));
    });

    request.addEventListener("loadend", () => {
      resolve(true);
    });

    request.send(formData);
  });
  return promise;
};

export async function getFileFromUrl(url: string): Promise<UploadableAsset> {
  const fileName = url.split("/").pop() || "file.bin";
  const fileExtension = fileName.split(".").pop() || "bin";
  const mimeType = getMimeType(fileExtension);

  const localUri = FileSystem.cacheDirectory + fileName;
  const { uri } = await FileSystem.downloadAsync(url, localUri);

  const fileInfo = await FileSystem.getInfoAsync(uri, { size: true });
  if (!fileInfo.exists) {
    console.error("File does not exist:", uri);
    throw new Error("File does not exist");
  }

  return {
    uri,
    fileName: fileName,
    mimeType: mimeType,
    fileSize: fileInfo.size,
  };
}

function getMimeType(ext: string): string {
  const map: Record<string, string> = {
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
    mp4: "video/mp4",
    mov: "video/quicktime",
    pdf: "application/pdf",
    txt: "text/plain",
    bin: "application/octet-stream",
  };
  return map[ext.toLowerCase()] || "application/octet-stream";
}


export async function uploadFileToXAPI(
  file: any,
  onProgressChange?: OnProgressChangeHandler,
) {
  onProgressChange?.(0);

  const accessToken = await getOAuthAccessToken();

  const formData = new FormData();

  formData.append("file", {
    uri: file.uri,
    name: file.fileName,
    type: file.mimeType,
  } as any);

  formData.append("accessToken", accessToken ? accessToken : "");

  const res = await fetch(`${env.EXPO_PUBLIC_API_URL}/uploads/x-api-upload`, {
    method: "POST",
    body: formData,
  });
  const data = await res.json();

  if (data.success) {
    onProgressChange?.(100);
    await FileSystem.deleteAsync(file.uri, { idempotent: true });
  }

  return data;
}
