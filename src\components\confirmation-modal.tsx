import React from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  useWindowDimensions,
  Platform,
  StyleProp,
  ViewStyle,
} from "react-native";

function Button({
  variant = "secondary",
  style = {} as StyleProp<ViewStyle>,
  onPress,
  children,
}: {
  variant?: "secondary" | "outline" | "destructive";
  style?: StyleProp<ViewStyle>;
  onPress: () => void;
  children: React.ReactNode;
}) {
  let backgroundColor = "#333";
  let textColor = "#fff";

  if (variant === "outline") {
    backgroundColor = "transparent";
    textColor = "#fff";
  } else if (variant === "destructive") {
    backgroundColor = "red";
    textColor = "#fff";
  } else if (variant === "secondary") {
    backgroundColor = "#555";
    textColor = "#fff";
  }

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.btnContainer, { backgroundColor }, style]}
    >
      <Text style={[styles.btnText, { color: textColor }]}>{children}</Text>
    </TouchableOpacity>
  );
}

/**
 * ConfirmationModal
 *
 * @param {boolean} open - Whether the modal is open
 * @param {function} setOpen - Function to toggle modal visibility
 * @param {string} title - Title for the dialog
 * @param {string} confirmButtonLabel - Label for the confirm button
 * @param {JSX.Element | string} children - Content/description inside the dialog
 * @param {function} onConfirm - Callback when user presses the confirm button
 * @param {boolean} destructive - If true, confirm button is styled destructively
 */

interface ConfirmationModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
  confirmButtonLabel: string;
  children: React.ReactNode;
  onConfirm: () => void;
  destructive?: boolean;
}

export function ConfirmationModal({
  open,
  setOpen,
  title,
  confirmButtonLabel,
  children,
  onConfirm,
  destructive = false,
}: ConfirmationModalProps) {
  const { width } = useWindowDimensions();
  const isTablet = width >= 768; // approximate check

  return (
    <Modal
      visible={open}
      transparent
      animationType="fade"
      onRequestClose={() => setOpen(false)}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPressOut={() => setOpen(false)}
      >
        <View
          style={[
            styles.modalContainer,
            isTablet ? styles.modalContainerTablet : styles.modalContainerPhone,
          ]}
        >
          <ConfirmationModalContent
            title={title}
            confirmButtonLabel={confirmButtonLabel}
            onConfirm={onConfirm}
            onCancel={() => setOpen(false)}
            destructive={destructive}
          >
            {children}
          </ConfirmationModalContent>
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

interface ConfirmationModalContentProps {
  title: string;
  children: React.ReactNode;
  confirmButtonLabel: string;
  onConfirm: () => void;
  onCancel: () => void;
  destructive?: boolean;
}

function ConfirmationModalContent({
  title,
  children,
  confirmButtonLabel,
  onConfirm,
  onCancel,
  destructive,
}: ConfirmationModalContentProps) {
  return (
    <View>
      <View style={{ marginBottom: 16 }}>
        <Text style={styles.titleText}>{title}</Text>
        <Text style={styles.descText}>{children}</Text>
      </View>
      <View style={styles.btnRow}>
        <Button
          variant="outline"
          style={{ flex: 1, marginRight: 8 }}
          onPress={onCancel}
        >
          Cancel
        </Button>
        <Button
          variant={destructive ? "destructive" : "secondary"}
          style={{ flex: 1 }}
          onPress={onConfirm}
        >
          {confirmButtonLabel}
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.4)",
    // Press anywhere outside content to close
    justifyContent: "center",
  },
  modalContainer: {
    backgroundColor: "#222",
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 20,
  },
  // Centered for tablet
  modalContainerTablet: {
    alignSelf: "center",
    width: 400,
  },
  // "Drawer-like" or standard for phone
  modalContainerPhone: {
    // if you want a bottom "drawer" style, you can do:
    // position: 'absolute',
    // bottom: 0,
    // left: 0,
    // right: 0,
    // borderTopLeftRadius: 16,
    // borderTopRightRadius: 16,
  },
  titleText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  descText: {
    color: "#ccc",
    fontSize: 14,
  },
  btnRow: {
    flexDirection: "row",
    marginTop: 8,
  },
  btnContainer: {
    paddingVertical: 10,
    borderRadius: 4,
    alignItems: "center",
  },
  btnText: {
    fontSize: 14,
  },
});
