import React, { useEffect, useMemo } from "react";
import { Text, ActivityIndicator, StyleSheet, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolate,
} from "react-native-reanimated";

const getButtonStyles = (variant: string, size: string) => {
  const baseStyle = {
    flexDirection: 'row' as const,
    position: 'relative' as const,
    overflow: 'hidden' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderRadius: 25,
  };

  const variantStyles = {
    default: {},
    destructive: { backgroundColor: '#dc2626' },
    outline: { 
      borderWidth: 1, 
      borderColor: '#6b7280',
      backgroundColor: 'transparent' 
    },
    'outline-ghost': { 
      borderWidth: 1, 
      borderColor: '#3B3B3B', 
      backgroundColor: 'transparent' 
    },
    secondary: { backgroundColor: '#ffffff' },
    ghost: { backgroundColor: 'transparent' },
    link: { backgroundColor: 'transparent' },
    active: { backgroundColor: '#059669' },
  };

  const sizeStyles = {
    default: { paddingHorizontal: 16, paddingVertical: 12 },
    sm: { paddingHorizontal: 12, paddingVertical: 8, borderRadius: 6 },
    lg: { paddingHorizontal: 32, paddingVertical: 16, borderRadius: 6 },
    icon: { height: 40, width: 40 },
  };

  return {
    ...baseStyle,
    ...variantStyles[variant as keyof typeof variantStyles],
    ...sizeStyles[size as keyof typeof sizeStyles],
  };
};

const getTextStyles = (variant: string) => {
  const baseStyle = {
    fontSize: 14,
    fontWeight: '600' as const,
  };

  const variantStyles = {
    default: { color: '#ffffff' },
    destructive: { color: '#ffffff' },
    outline: { color: '#ffffff' },
    'outline-ghost': { color: '#ffffff' },
    secondary: { color: '#000000' },
    ghost: { color: '#ffffff' },
    link: { color: '#2563eb', textDecorationLine: 'underline' as const },
    active: { color: '#ffffff' },
  };

  return {
    ...baseStyle,
    ...variantStyles[variant as keyof typeof variantStyles],
  };
};

export interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'outline-ghost' | 'secondary' | 'ghost' | 'link' | 'active';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  onPress?: () => void;
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  style?: any;
  textStyle?: any;
}

export function Button({
  variant = 'default',
  size = 'default',
  onPress,
  disabled,
  loading,
  children,
  style,
  textStyle,
  ...props
}: ButtonProps) {
  const loadingAnimation = useSharedValue(loading ? 1 : 0);

  useEffect(() => {
    loadingAnimation.value = withTiming(loading ? 1 : 0, { duration: 300 });
  }, [loading]);

  const buttonStyle = useMemo(() => {
    const baseStyle = getButtonStyles(variant, size);
    const opacityStyle = disabled && !loading ? { opacity: 0.5 } : {};
    return [baseStyle, opacityStyle, style];
  }, [variant, size, loading, disabled, style]);

  const textStyle_computed = useMemo(() => {
    const baseTextStyle = getTextStyles(variant);
    return [baseTextStyle, textStyle];
  }, [variant, textStyle]);

  const loadingAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: loadingAnimation.value,
      transform: [
        {
          translateY: interpolate(
            loadingAnimation.value,
            [0, 1],
            [-20, 0]
          ),
        },
      ],
    };
  });

  const childrenAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: 1 - loadingAnimation.value,
      transform: [
        {
          translateY: interpolate(
            loadingAnimation.value,
            [0, 1],
            [0, 20]
          ),
        },
      ],
    };
  });

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      {...props}
    >
      {variant === "default" && (
        <LinearGradient
          colors={["#FF7817", "#D05700", "#DD3C09"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          locations={[0, 0, 0.5747]}
          style={StyleSheet.absoluteFillObject}
        />
      )}
      
      <Animated.View 
        style={[
          StyleSheet.absoluteFillObject,
          {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          },
          loadingAnimatedStyle
        ]}
      >
        <ActivityIndicator size="small" color="#ffffff" />
      </Animated.View>
      
      <Animated.View 
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 4,
          },
          childrenAnimatedStyle
        ]}
      >
        {typeof children === "string" ? (
          <Text style={textStyle_computed}>{children}</Text>
        ) : (
          children
        )}
      </Animated.View>
    </TouchableOpacity>
  );
}
