import { Svg, Path, SvgProps } from "react-native-svg";

export const WalletOutlineIcon = (props: SvgProps) => (
  <Svg viewBox="0 0 24 24" {...props}>
    <Path
      fill="currentColor"
      stroke="currentColor"
      strokeWidth={0.5}
      d="M17 8.616h2.194c.322 0 .645-.323.645-.71V7.1H5.08c-.323 0-.581-.194-.581-.516 0-.323.258-.58.58-.58H19.84v-.259c0-.387.161-1.71-.71-1.71H2.806a.716.716 0 0 0-.71.71v13.807c0 .387.323.71.71.71H19.13c.387 0 .71-.323.71-.71v-3.226c0-.387-.323-.71-.645-.71H17c-1.032 0-1.806-.774-1.806-1.806v-2.388c0-.967.774-1.806 1.806-1.806Zm3.806 0h.388c.967 0 1.806.839 1.806 1.806v2.388a1.803 1.803 0 0 1-1.806 1.806h-.387c.064.258.129.452.129.71v3.226c0 .967-.775 1.806-1.807 1.806H2.806C1.84 20.358 1 19.519 1 18.552V4.745c0-.968.839-1.806 1.806-1.806H19.13c1.032 0 1.806.838 1.806 1.806v3.161c0 .258-.064.516-.128.71Zm.388 1.097H17a.716.716 0 0 0-.71.71v2.387c0 .387.323.71.71.71h4.194c.387 0 .71-.323.71-.71v-2.388a.716.716 0 0 0-.71-.71Z"
    />
    <Path d="M18.226 10.23c.774 0 1.42.644 1.42 1.418 0 .775-.646 1.355-1.42 1.355-.774 0-1.355-.58-1.355-1.354 0-.775.58-1.42 1.355-1.42Z" />
  </Svg>
);
