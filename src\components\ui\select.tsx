import React, { useState, useCallback, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  TouchableWithoutFeedback,
  StyleProp,
  ViewStyle,
  TextStyle,
  ImageSourcePropType,
  Image,
  Pressable,
  Dimensions,
} from "react-native";
import { TabBarIcon } from "../navigation/TabBarIcon";
import { Colors } from "@/constants/Colors";
import { ThemedText } from "../ThemedText";
import { isAndroid } from "@/constants/Device";
import { CheckFilledIcon } from "../icons";

type OptionItem = {
  name: string;
  icon?: ImageSourcePropType; // Make icon optional
  native?: boolean;
};

interface DropDownProps {
  data: OptionItem[];
  onChange: (item: OptionItem) => void;
  placeholder: string;
  placeholderImage?: ImageSourcePropType;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  dropdownStyle?: StyleProp<ViewStyle>;
  activeBorderColor?: string;
  inactiveBorderColor?: string;
  label?: string;
  labelStyle?: StyleProp<TextStyle>;
  fullWidth?: boolean;
  defaultValue?: OptionItem;
  balance?: object;
  value?: string;
}

export default function Dropdown({
  data,
  onChange,
  placeholder,
  placeholderImage,
  style,
  textStyle,
  dropdownStyle,
  label,
  labelStyle,
  activeBorderColor = Colors.dark.brandOrange,
  inactiveBorderColor = Colors.dark.lightgrey,
  fullWidth = false,
  defaultValue,
  balance,
  value,
}: DropDownProps) {
  const [expanded, setExpanded] = useState(false);
  const [selectedValue, setSelectedValue] = useState<OptionItem | null>(
    defaultValue || null
  );
  const buttonRef = useRef<View>(null);
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
  });

  const toggleExpanded = useCallback(() => setExpanded(!expanded), [expanded]);

  useEffect(() => {
    if (value && data?.length > 0) {
      setSelectedValue(data.find((item) => item.name === value) || null);
    }
  }, [value, data]);

  const handleSelect = (item: OptionItem) => {
    setExpanded(false);
    onChange(item);
  };

  const measureDropdown = () => {
    if (buttonRef.current) {
      buttonRef.current.measure((_fx, _fy, width, height, px, py) => {
        setDropdownPosition({
          top: py + height + (isAndroid ? 0 : 3),
          left: fullWidth ? 0 : px,
          width: fullWidth ? Dimensions.get("window").width - 20 : width,
        });
      });
    }
  };

  useEffect(() => {
    if(dropdownPosition.width > 0 && dropdownPosition.top > 0 && dropdownPosition.left > 0) {
      toggleExpanded()
    }
  }, [dropdownPosition]) 

  return (
    <View>
      {label ? (
        <ThemedText style={[styles.label, labelStyle]}>{label}</ThemedText>
      ) : null}
      <Pressable
        ref={buttonRef}
        style={[
          styles.button,
          style,
          { borderColor: expanded ? activeBorderColor : inactiveBorderColor },
        ]}
        onPress={() => {
          measureDropdown()
        }}
      >
        <View style={styles.placeholderWrapper}>
          {selectedValue?.icon ? (
            <Image
              style={styles.placeholderImage}
              source={selectedValue.icon}
            />
          ) : placeholderImage ? (
            <Image style={styles.placeholderImage} source={placeholderImage} />
          ) : null}
          <Text style={[styles.text, textStyle]}>
            {selectedValue?.name || placeholder}
          </Text>
        </View>
        <TabBarIcon
          name="chevron-down-outline"
          size={24}
          color={expanded ? Colors.dark.brandOrange : Colors.dark.lightgrey}
        />
      </Pressable>
      {expanded && (
        <Modal visible={expanded} transparent animationType="fade">
          <TouchableWithoutFeedback onPress={() => setExpanded(false)}>
            <View style={styles.modalWrapper}>
              <View
                style={[
                  styles.dropdown,
                  dropdownStyle,
                  {
                    top: dropdownPosition.top,
                    left: dropdownPosition.left,
                    width: dropdownPosition.width,
                  },
                ]}
              >
                <FlatList
                  data={data}
                  keyExtractor={(item, index) => item.name || index.toString()}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      style={styles.option}
                      onPress={() => handleSelect(item)}
                    >
                      <View style={styles.coinContainer}>
                        {item.icon && (
                          <Image style={styles.coinImage} source={item.icon} />
                        )}
                        <Text
                          style={[
                            styles.dropdownItemColor,
                            styles.dropdownItem,
                          ]}
                        >
                          {item.name}
                        </Text>
                      </View>
                      {selectedValue?.name === item.name ? (
                        <CheckFilledIcon height={20} width={20} color={Colors.dark.offWhite} />
                      ) : balance ? (
                        <Text
                          style={[
                            styles.dropdownItemColor,
                            styles.dropdownItemQty,
                          ]}
                        >
                          {balance[item.name as keyof typeof balance]}
                        </Text>
                      ) : null}
                    </TouchableOpacity>
                  )}
                  ItemSeparatorComponent={() => (
                    <View style={styles.separator} />
                  )}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  button: {
    height: 50,
    justifyContent: "space-between",
    backgroundColor: "transparent",
    width: "100%",
    paddingHorizontal: 15,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  text: {
    fontSize: 15,
    fontFamily: "InterSemiBold",
    color: Colors.dark.white,
  },
  modalWrapper: {
    flex: 1,
    justifyContent: "flex-start",
    alignItems: "flex-start",
  },
  dropdown: {
    position: "absolute",
    backgroundColor: Colors.dark.darkGrey,
    borderRadius: 8,
    maxHeight: 250,
  },
  option: {
    padding: 15,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  separator: {
    height: 1,
  },
  dropdownItem: {},
  dropdownItemColor: {
    color: Colors.dark.white,
  },
  dropdownItemQty: {
    fontSize: 12,
  },
  coinContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  coinImage: {
    width: 30,
    aspectRatio: 1,
    borderRadius: 15,
  },
  placeholderImage: {
    width: 20, // Adjust width as needed
    height: 20, // Adjust height as needed
    borderRadius: 10, // Adjust border radius if you want rounded images
  },
  placeholderWrapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  label: {
    fontWeight: "300",
    marginBottom: 7,
    color: Colors.dark.grey,
    fontSize: 12,
  },
});
