import React from "react";
import { View, Pressable } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import {
  FlagOutlineIcon,
  BanOutlineIcon,
  CopyOutlineIcon,
  SettingsCircleOutlineIcon,
  ShareOutlineIcon,
} from "@/components/icons";
import { ShowRepostsIcon } from "@/components/icons/show-reposts-icon";
import { HideRepostsIcon } from "@/components/icons/hide-reposts-icon";

interface ProfileMenuBottomSheetProps {
  isMe: boolean;
  onCreatorFeeSettings?: () => void;
  onHideReposts?: () => void;
  onShareProfile?: () => void;
  onReport?: () => void;
  onBlock?: () => void;
  onCopyLink?: () => void;
  showReposts?: boolean;
  isBlocked?: boolean;
}

export const ProfileMenuBottomSheet: React.FC<ProfileMenuBottomSheetProps> = ({
  isMe,
  onCreatorFeeSettings,
  onHideReposts,
  onShareProfile,
  onReport,
  onBlock,
  onCopyLink,
  showReposts,
  isBlocked = false,
}) => {
  return (
    <View >
      {isMe ? (
        <>
          <MenuItem
            icon={<SettingsCircleOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Creator Fee Settings"
            onPress={onCreatorFeeSettings}
          />
          <MenuItem
            icon={showReposts ? <HideRepostsIcon width={20} height={20} /> : <ShowRepostsIcon width={20} height={20} />}
            label={showReposts ? "Hide Reposts" : "Show Reposts"}
            onPress={onHideReposts}
          />
          <MenuItem
            icon={<ShareOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Share profile"
            onPress={onShareProfile}
          />
        </>
      ) : isBlocked ? (
        <>
          <MenuItem
            icon={<FlagOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Report"
            onPress={onReport}
          />
          <MenuItem
            icon={<BanOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Unblock"
            onPress={onBlock}
          />
          <MenuItem
            icon={<CopyOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Copy link to profile"
            onPress={onCopyLink}
          />
        </>
      ) : (
        <>
          <MenuItem
            icon={showReposts ? <HideRepostsIcon width={20} height={20} /> : <ShowRepostsIcon width={20} height={20} />}
            label={showReposts ? "Hide Reposts" : "Show Reposts"}
            onPress={onHideReposts}
          />
          <MenuItem
            icon={<FlagOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Report"
            onPress={onReport}
          />
          <MenuItem
            icon={<BanOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Block"
            onPress={onBlock}
          />
          <MenuItem
            icon={<CopyOutlineIcon width={20} height={20} stroke="#B0B0B0" />}
            label="Copy link to profile"
            onPress={onCopyLink}
          />
        </>
      )}
    </View>
  );
};

const MenuItem = ({ icon, label, onPress }: { icon: React.ReactNode; label: string; onPress?: () => void }) => (
  <Pressable
    className="flex-row items-center py-3 px-2"
    onPress={onPress}
    android_ripple={{ color: '#222' }}
  >
    <View className="mr-3">{icon}</View>
    <ThemedText type="bold" className="text-base font-medium">{label}</ThemedText>
  </Pressable>
);

export default ProfileMenuBottomSheet; 

