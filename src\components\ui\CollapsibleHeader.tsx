import React, { createContext, useContext, ReactNode } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  SharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { StyleSheet } from 'react-native';

interface CollapsibleHeaderConfig {
  maxHeaderHeight: number;
  minHeaderHeight: number;
  maxTabBarHeight?: number;
  minTabBarHeight?: number;
  springConfig?: {
    damping?: number;
    stiffness?: number;
    mass?: number;
  };
  timingConfig?: {
    duration?: number;
  };
}

interface CollapsibleHeaderContextType {
  scrollY: SharedValue<number>;
  config: CollapsibleHeaderConfig;
}

const CollapsibleHeaderContext = createContext<CollapsibleHeaderContextType | null>(null);

export const useCollapsibleHeader = () => {
  const context = useContext(CollapsibleHeaderContext);
  if (!context) {
    throw new Error('useCollapsibleHeader must be used within CollapsibleHeaderProvider');
  }
  return context;
};

interface CollapsibleHeaderProviderProps {
  children: ReactNode;
  config: CollapsibleHeaderConfig;
}

export const CollapsibleHeaderProvider: React.FC<CollapsibleHeaderProviderProps> = ({
  children,
  config,
}) => {
  const scrollY = useSharedValue(0);

  return (
    <CollapsibleHeaderContext.Provider value={{ scrollY, config }}>
      {children}
    </CollapsibleHeaderContext.Provider>
  );
};

interface CollapsibleHeaderProps {
  children: ReactNode;
  style?: any;
  useSpring?: boolean;
}

export const CollapsibleHeader: React.FC<CollapsibleHeaderProps> = ({
  children,
  style,
  useSpring = true,
}) => {
  const { scrollY, config } = useCollapsibleHeader();
  const scrollRange = config.maxHeaderHeight - config.minHeaderHeight;

  const animatedStyle = useAnimatedStyle(() => {
    const height = interpolate(
      scrollY.value,
      [0, scrollRange],
      [config.maxHeaderHeight, config.minHeaderHeight],
      'clamp'
    );

    return {
      height: useSpring
        ? withSpring(height, config.springConfig || { damping: 15, stiffness: 150 })
        : withTiming(height, config.timingConfig || { duration: 200 }),
      overflow: 'hidden',
    };
  });

  return (
    <Animated.View style={[animatedStyle, style]}>
      {children}
    </Animated.View>
  );
};

interface CollapsibleTabBarProps {
  style?: any;
  useSpring?: boolean;
}

export const useCollapsibleTabBarStyle = ({
  style,
  useSpring = true,
}: CollapsibleTabBarProps = {}) => {
  const { scrollY, config } = useCollapsibleHeader();

  if (!config.maxTabBarHeight || !config.minTabBarHeight) {
    return style;
  }

  const scrollRange = config.maxHeaderHeight - config.minHeaderHeight;

  const animatedStyle = useAnimatedStyle(() => {
    const height = interpolate(
      scrollY.value,
      [0, scrollRange],
      [config.maxTabBarHeight!, config.minTabBarHeight!],
      'clamp'
    );

    return {
      height: useSpring
        ? withSpring(height, config.springConfig || { damping: 15, stiffness: 150 })
        : withTiming(height, config.timingConfig || { duration: 200 }),
    };
  });

  return [style, animatedStyle];
};

// Hook for scroll handler
export const useCollapsibleScrollHandler = () => {
  const { scrollY } = useCollapsibleHeader();

  return {
    onScroll: (event: any) => {
      'worklet';
      scrollY.value = event.contentOffset.y;
    },
  };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
