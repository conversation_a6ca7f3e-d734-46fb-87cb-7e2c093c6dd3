import { ThemedText } from "@/components/ThemedText";
import { But<PERSON> } from "@/components/ui/button";
import { Colors } from "@/constants/Colors";
import { memo } from "react";
import { View, LayoutChangeEvent, StyleSheet } from "react-native"

type TMessageRequestView = {
    chatMateName: string;
    handleDelete: () => void;
    handleAccept: () => void;
    onLayout: (event: LayoutChangeEvent) => void;
}

export const MessageRequestView = memo(({
    chatMateName,
    handleDelete,
    handleAccept,
    onLayout,
}: TMessageRequestView) => {
    return (
        <View onLayout={onLayout} style={styles.container}>
            <ThemedText style={styles.text}>
                Do you want to let {chatMateName} message you? They won't know you've seen their message until you accept.
            </ThemedText>
            <View style={styles.buttonContainer}>
                <Button
                    onPress={handleAccept}
                    style={styles.button}
                >
                    <ThemedText type="bold">Accept</ThemedText>
                </Button>
                <Button
                    onPress={handleDelete}
                    variant="outline"
                    style={styles.button}
                >
                    <ThemedText type="bold">Delete</ThemedText>
                </Button>
            </View>
        </View>
    )
}, (prevProps, nextProps) => {
    return (
        prevProps.chatMateName === nextProps.chatMateName
        && prevProps.handleDelete === nextProps.handleDelete
        && prevProps.handleAccept === nextProps.handleAccept
        && prevProps.onLayout === nextProps.onLayout
    )
})

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderTopWidth: 1,
        borderTopColor: Colors.dark.darkGrey,
        marginTop: 10
    },
    text: {
        lineHeight: 24
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 20,
    },
    button: {
        width: '48%',
    }
})