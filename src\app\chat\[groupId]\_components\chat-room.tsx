import { Activity<PERSON>ndi<PERSON><PERSON>, <PERSON>rollViewProps, SectionList, StyleSheet, TouchableOpacity, View } from "react-native";
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { ThemedText } from "@/components/ThemedText";
import { ChatMessagesResponse, ChatPinnedMessagesResponse, MessageType, GroupMessageTypeEnum } from "@/queries/types";
import { InfiniteData, useInfiniteQuery, useQueryClient } from "@tanstack/react-query";
import { getMessagesAfter, getMessagesAround, getMessagesBefore } from "@/api/client/chat";
import { useGroupStore, useGroup } from "../_contexts/group-context";
import { format } from "date-fns";
import { v4 as uuidv4 } from "uuid";
import { useUser } from "@/stores";
import { formatChatTimeDistance } from "@/utils";
import { Colors } from "@/constants/Colors";
import { useMessagingSocket } from "../hooks/useMessagingSocket";
import { Message } from "./message";
import { toast } from "@/components/toast";
import { usePinMessage } from "@/queries/chat-mutations";
import { ImagePreview } from "@/components/ui/image-preview";
import { ScrollProtectedSectionList } from "@/components/ui/scroll-protected-sectionlist";
import { ArrowDownFilledIcon } from "@/components/icons";
import { screenWidth } from "@/constants/Device";
import { Nullable } from "@/types/common";
import { MessagesSkaleton } from "./messages-skaleton";
import { useFocusEffect } from "expo-router";

const PADDING_HZT = 10;

interface TImagePreview {
    isVisible: boolean;
    uri: string;
    type: "image" | "video";
    channelTitle: string;
}

type QueryKeyType = (
    | string
    | {
        groupId: string;
        messageId?: string | null;
    }
)[];

type TRenderItem = {
    item: MessageType;
    index: number;
    section: { data: MessageType[] };
}

const ChatRoom = memo(({ footerHeight }: { footerHeight: Nullable<number> }) => {
    //states
    const [imagePreview, setImagePreview] = useState<TImagePreview | null>(null);
    const [showScrollToBottomIcon, setShowScrollToBottomIcon] = useState(false);
    const sectionListRef = useRef<SectionList>(null);
    const [isAtStart, setIsAtStart] = useState(false);

    const scrollRef = useRef<SectionList<any>>(null);
    /**
  * We want to call onEndReached and onStartReached only once, per content length.
  * We keep track of calls to these functions per content length, with following trackers.
  */
    const onStartReachedTracker = useRef<Record<number, boolean>>({});
    const onEndReachedTracker = useRef<Record<number, boolean>>({});

    //store
    const msgId = useGroupStore((state) => state.messageId);
    const pinnedMessagesCount = useGroupStore((state) => state.pinnedMessagesCount);
    const isLoadingOverlay = useGroupStore((state) => state.isLoadingOverlay);
    const setLoadingOverlay = useGroupStore((state) => state.actions.setLoadingOverlay);
    const setMessageId = useGroupStore((state) => state.actions.setMessageId);
    const resetMessageId = useGroupStore((state) => state.actions.resetMessageId);

    //contexts
    const { groupId, setMessageToReplyTo, amIOwner, groupName, refetchData } = useGroup();
    const { user } = useUser();
    const queryClient = useQueryClient();

    useFocusEffect(
        useCallback(() => {
            //every focus, refetch the group settings and chatmate, owner data
            refetchData?.();
        }, [refetchData])
    );

    const onScrollToBottomButtonClick = () => {
        if (sections.length > 0 && sections[0].data?.length > 0) {
            sectionListRef.current?.scrollToLocation({
                animated: true,
                itemIndex: 0,
                sectionIndex: 0,
            });
        }
    }

    //hooks
    useMessagingSocket(groupId, msgId, isAtStart, onScrollToBottomButtonClick);

    const resetTrackers = () => {
        onEndReachedTracker.current = {};
        onStartReachedTracker.current = {};
        resetMessageId();
        setLoadingOverlay(false);
    }

    useEffect(() => {
        return () => {
            resetTrackers();
        }
    }, [])

    const {
        data: messagesData,
        isLoading,
        isFetchingNextPage,
        hasNextPage,
        fetchNextPage,
        isFetchingPreviousPage,
        hasPreviousPage,
        fetchPreviousPage,
    } = useInfiniteQuery<
        ChatMessagesResponse,
        Error,
        InfiniteData<ChatMessagesResponse>,
        QueryKeyType,
        {
            timeFrom: number;
            messageId: string | null;
            initialLoad?: boolean;
        }
    >({
        queryKey: [
            "chat",
            "group-infinite-messages",
            { groupId: groupId || '', messageId: msgId },
        ],
        queryFn: async ({ pageParam, direction }) => {
            if (pageParam.messageId) {
                //if pinned message is clicked, get messages around the pinned message
                const messagesData = await getMessagesAround({
                    groupId: groupId || '',
                    messageId: pageParam.messageId,
                });
                messagesData.messages = messagesData.messages.reverse();
                return messagesData;
            }
            if (direction === "backward") {
                //direction is backward when fetchPreviousPage is called - when fetching newer messages
                const messagesData = await getMessagesAfter({
                    groupId: groupId || '',
                    timeFrom: pageParam.timeFrom,
                });
                messagesData.messages = messagesData.messages.reverse();
                return messagesData;
            }

            if (direction === "forward") {
                //direction is forward when fetchNextPage is called - when fetching older messages
                const messagesData = await getMessagesBefore({
                    groupId: groupId || '',
                    timeFrom: pageParam.timeFrom,
                });
                //data from api is in descending order of dates (newest date first). First index message is the latest message.
                //FROM cache POV : same order is maintained as the api response.
                //example: data is 5th feb, 4th feb, 3rd feb, 2nd feb, 1st feb
                return messagesData;
            }
        },
        initialPageParam: {
            timeFrom: 0,
            messageId: msgId,
            initialLoad: true,
        },
        getPreviousPageParam: (firstPage, pages, lastPageParam) => {
            //get newer messages
            if (firstPage.messages.length >= 40) {
                return {
                    timeFrom: +firstPage.messages[0].createdOn,
                    messageId: null,
                };
            }
            return undefined;
        },
        getNextPageParam: (lastPage, pages, lastPageParam) => {
            //get older messages
            //current data:  5th feb, 4th feb, 3rd feb, 2nd feb, 1st feb
            //when you fetch new page, new page data is concatenated after all the prev pages data.
            //After fetching new page data, data will be : 5th feb, 4th feb, 3rd feb, 2nd feb, 1st feb, 31st jan

            if (lastPage.messages.length >= 40) {
                return {
                    timeFrom: +lastPage.messages[lastPage.messages.length - 1].createdOn,
                    messageId: null,
                };
            }
            return undefined;
        },
        enabled: !!groupId
    });

    const { mutate: pin } = usePinMessage({
        onMutate: ({ groupId, messageId, isPinned }) => {
            const previousMessages = queryClient.getQueryData<InfiniteData<ChatMessagesResponse>>([
                "chat",
                "group-infinite-messages",
                { groupId: groupId, messageId: msgId },
            ]);
            const previousPinnedMessages = queryClient.getQueryData<ChatPinnedMessagesResponse>([
                "chat",
                "messages",
                "pinned",
                groupId,
            ]);

            queryClient.setQueryData(
                [
                    "chat",
                    "group-infinite-messages",
                    { groupId, messageId: msgId },
                ],
                (old: InfiniteData<ChatMessagesResponse>) => {
                    if (!old) return old;
                    return {
                        ...old,
                        pages: old.pages.map((page) => {
                            return {
                                ...page,
                                messages: page.messages.map((message) => {
                                    if (message.id === messageId) {
                                        return {
                                            ...message,
                                            isPinned,
                                        }
                                    }
                                    return message;
                                }
                                ),
                            };
                        }),
                    };
                },
            );

            let messageToUpdate: Nullable<MessageType> = null;
            if (previousMessages) {
                //for each page, check if the message is present. If message is present, return message item
                //when message is found, don't check other pages
                previousMessages.pages.forEach((page) => {
                    const message = page.messages.find((message) => message.id === messageId);
                    if (message) {
                        messageToUpdate = message;
                        return;
                    }
                });
            }

            queryClient.setQueryData(
                ["chat", "messages", "pinned", groupId],
                (old: ChatPinnedMessagesResponse) => {
                    if (!old) return old;

                    if (isPinned) {
                        if (messageToUpdate) {
                            return {
                                ...old,
                                messages: [...old.messages, { ...messageToUpdate, isPinned }].sort(
                                    (a, b) => {
                                        return a.createdOn > b.createdOn ? -1 : 1;
                                    },
                                ),
                            };
                        } else {
                            return {
                                ...old
                            }
                        }
                    } else if (!isPinned) {
                        return {
                            ...old,
                            messages: old.messages.filter((m) => m.id !== messageId),
                        };
                    }
                },
            );

            return { previousMessages, previousPinnedMessages };
        },
        onError: (err, variables, context) => {
            queryClient.setQueryData(
                [
                    "chat",
                    "group-infinite-messages",
                    { groupId: variables.groupId, messageId: null },
                ],
                context?.previousMessages,
            );
        },
    });

    const sections = useMemo(() => {
        const flatMessages =
            messagesData?.pages.map((page) => page.messages).flat() ?? [];
        const groupedByDate = flatMessages.reduce(
            (acc, message) => {
                const date = new Date(+message.createdOn);
                const formattedDate = format(date, "MM/dd/yyyy");

                if (!acc[formattedDate]) {
                    acc[formattedDate] = [];
                }

                acc[formattedDate].push(message);
                return acc;
            },
            {} as Record<string, MessageType[]>,
        );

        // Sort the dates in descending order (newest date first) because Object.keys returns the dates in ascending order
        const sortedDates = Object.keys(groupedByDate).sort((a, b) => a > b ? -1 : 1);
        return sortedDates.map((date) => ({
            id: uuidv4(),     // Generate a unique id for each section
            title: date,      // The formatted date for the section title
            data: groupedByDate[date],  // Messages for this section
            noDataText: '',   // Empty string for noDataText
        }));
    }, [messagesData]);

    //pinned message is clicked and we had to get new data from server
    const pinnedMsgClickSectionIndex = useMemo(() =>
        sections?.findIndex((section) => section.data.some((message) => message.id === msgId)),
        [sections, msgId]);

    //pinned message is clicked and we had to get new data from server
    const pinnedMsgClickItemIndex = useMemo(() =>
        sections?.[pinnedMsgClickSectionIndex]?.data?.findIndex((message) => message.id === msgId),
        [sections, pinnedMsgClickSectionIndex, msgId]);

    //scrollToIndex for "pinned message is clicked and we had to get new data from server"
    useEffect(() => {
        if (msgId && sections && sections.length > 0) {
            const wait = new Promise(resolve => setTimeout(resolve, 500));
            wait.then(() => {
                if (pinnedMsgClickItemIndex !== -1 && pinnedMsgClickSectionIndex !== -1) {
                    sectionListRef.current?.scrollToLocation({
                        animated: true,
                        itemIndex: pinnedMsgClickItemIndex,
                        sectionIndex: pinnedMsgClickSectionIndex,
                    });
                }
            });
        }
    }, [msgId, pinnedMsgClickItemIndex, pinnedMsgClickSectionIndex])


    const handleReplySelect = useCallback(
        (item: MessageType) => {
            setMessageToReplyTo?.(item);
        },
        [setMessageToReplyTo]
    );

    const handlePinMessage = useCallback((item: MessageType) => {
        if (!groupId) return;
        if (
            pinnedMessagesCount &&
            pinnedMessagesCount >= 3 &&
            Boolean(item.isPinned) === false
        ) {
            toast.danger("Please unpin one of your messages");
            return;
        }
        pin({
            groupId: groupId,
            messageId: item.id,
            isPinned: !item.isPinned,
        });
    }, [groupId, pinnedMessagesCount])

    const handleImagePress = useCallback((item: MessageType) => {
        setImagePreview?.({
            isVisible: true,
            uri: item.attachments?.[0]?.url || "",
            type: item.attachments?.[0]?.messageType === GroupMessageTypeEnum.Image ? "image" : "video",
            channelTitle: groupName || "",
        });
    }, [groupName])

    const onImagePreviewClose = useCallback(() => {
        setImagePreview?.({
            isVisible: false,
            uri: "",
            type: "image",
            channelTitle: "",
        });
    }, [])

    /**
   * 1. Makes a call to `fetchPreviousPage` function, which queries more recent messages.
   * 2. Ensures that we call `fetchPreviousPage`, once per content length
   * 3. If the call to `fetchPreviousPage` is in progress, we wait for it to finish to make sure scroll doesn't jump.
   */
    const maybeCallOnStartReached = async () => {
        //get all sections and find total no of messages in all sections
        const totalMessages = sections?.reduce((acc, section) => acc + section.data.length, 0);

        // If onStartReached has already been called for given data length, then ignore.
        if (
            totalMessages &&
            onStartReachedTracker.current[totalMessages]
        ) {
            return;
        }

        if (totalMessages) {
            onStartReachedTracker.current[totalMessages] = true;
        }

        if (!isFetchingPreviousPage && hasPreviousPage) {
            fetchPreviousPage();
        }
    };

    /**
   * 1. Makes a call to `fetchNextPage` function, which queries more older messages.
   * 2. Ensures that we call `loadMore`, once per content length
   * 3. If the call to `fetchNextPage` is in progress, we wait for it to finish to make sure scroll doesn't jump.
   */
    const maybeCallOnEndReached = async () => {
        //get all sections and find total no of messages in all sections
        const totalMessages = sections?.reduce((acc, section) => acc + section.data.length, 0);

        // If onEndReached has already been called for given messageList length, then ignore.
        if (totalMessages && onEndReachedTracker.current[totalMessages]) {
            return;
        }

        if (totalMessages) {
            onEndReachedTracker.current[totalMessages] = true;
        }

        if (!isFetchingNextPage && hasNextPage) {
            fetchNextPage();
        }
    };


    const onUserScrollEvent: NonNullable<ScrollViewProps['onScroll']> = (event) => {
        const nativeEvent = event.nativeEvent;
        const offset = nativeEvent.contentOffset.y;
        const visibleLength = nativeEvent.layoutMeasurement.height;
        const contentLength = nativeEvent.contentSize.height;
        if (!sections || !sections.length) {
            return;
        }

        // Check if scroll has reached either start or end of list.
        const isScrollAtStart = offset < 100;
        const isScrollAtEnd = contentLength - visibleLength - offset < 500;

        if (isScrollAtStart) {
            setIsAtStart(true);
            maybeCallOnStartReached();
        } else {
            setIsAtStart(false);
        }

        if (isScrollAtEnd) {
            maybeCallOnEndReached();
        }
    };

    const onScrollBeginDrag: ScrollViewProps['onScrollBeginDrag'] = (event) => {
        onUserScrollEvent(event);
    };

    const onScrollEndDrag: ScrollViewProps['onScrollEndDrag'] = (event) => {
        onUserScrollEvent(event);
    };

    const handleScroll: ScrollViewProps['onScroll'] = (event) => {
        const messageListHasMessages = sections?.length > 0;
        const offset = event.nativeEvent.contentOffset.y;

        // Show scrollToBottom button once scroll position goes beyond 150.
        const isScrollAtBottom = offset <= 150;
        const showScrollToBottomButton = messageListHasMessages && !isScrollAtBottom;
        /**
         * 1. If I scroll up -> show scrollToBottom button.
         * 2. If I scroll to bottom of screen hide scrollToBottom button.
         */
        setShowScrollToBottomIcon(showScrollToBottomButton);
    };

    const renderItem = useCallback(({ item, index, section: { data } }: TRenderItem) => {
        const nextMessage = data?.[index - 1];
        const previousMessage = data?.[index + 1];
        const isMyMessage = item.userId === user?.id;

        const isPreviousSameUser = isMyMessage ? previousMessage?.userId === user?.id : previousMessage?.userId === item?.userId;
        const isNextSameUser = isMyMessage ? nextMessage?.userId === user?.id : nextMessage?.userId === item?.userId;

        return (
            <Message
                messageItem={item}
                groupId={groupId || ""}
                loggedinUserId={user?.id || ""}
                isMyMessage={isMyMessage}
                isPreviousSameUser={isPreviousSameUser}
                isNextSameUser={isNextSameUser}
                amIOwner={amIOwner || false}
                clickedMessageId={msgId}
                onReplySelect={handleReplySelect}
                onPinSelect={handlePinMessage}
                onImagePress={handleImagePress}
                setLoadingOverlay={setLoadingOverlay}
                setMessageId={setMessageId}
                simultaneousHandlers={scrollRef}
            />
        )
    }, [user?.id, groupId, amIOwner, msgId, handleReplySelect, handlePinMessage, handleImagePress, setLoadingOverlay, setMessageId])

    return isLoading ? (
        <MessagesSkaleton />
    ) : sections && sections.length > 0 && user ? (
        <>
            {isLoadingOverlay && (
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={Colors.dark.brandOrange} />
                </View>
            )}
            {showScrollToBottomIcon ? (
                <TouchableOpacity style={[{ bottom: (footerHeight ?? 0) }, styles.scrollToBottomIcon]} onPress={onScrollToBottomButtonClick}>
                    <ArrowDownFilledIcon color={Colors.dark.white} height={22} width={22} />
                </TouchableOpacity>
            ) : null}
            <ScrollProtectedSectionList
                sections={sections}
                ref={sectionListRef}
                style={styles.sectionList}
                maxToRenderPerBatch={30}
                inverted //data is in descending order of dates. If display is inverted today's latest message will be at the bottom.
                keyExtractor={(item, index) => (item.id + index).toString()}
                renderItem={renderItem}
                showsVerticalScrollIndicator={false}
                onScroll={handleScroll}
                onScrollBeginDrag={onScrollBeginDrag}
                onScrollEndDrag={onScrollEndDrag}
                renderSectionFooter={({ section }) => (
                    <View style={styles.sectionHeaderFooter}>
                        {
                            hasNextPage && isFetchingNextPage && section.id === sections[sections.length - 1].id ? (
                                <ActivityIndicator style={{ marginVertical: 10 }} size="small" color={Colors.dark.brandOrange} />
                            ) : null
                        }
                        <ThemedText style={styles.sectionFooterText}>
                            {formatChatTimeDistance(section.title)}
                        </ThemedText>
                    </View>
                )}
                renderSectionHeader={({ section }) => (
                    <View style={styles.sectionHeaderFooter}>
                        {
                            hasPreviousPage && isFetchingPreviousPage && section.id === sections[0].id ? (
                                <ActivityIndicator style={{ marginVertical: 10 }} size="small" color={Colors.dark.brandOrange} />
                            ) : null
                        }
                    </View>
                )}
            />
            {
                imagePreview?.isVisible ? (
                    <ImagePreview
                        visible={imagePreview.isVisible}
                        onRequestClose={onImagePreviewClose}
                        imageUrl={imagePreview.uri}
                        title={groupName || ""}
                    />
                ) : null
            }
        </>
    ) : (
        <View style={styles.noMessagesContainer}>
            <ThemedText style={styles.defaultText}>
                No messages yet!
            </ThemedText>
        </View>
    )
}, (prevProps, nextProps) => {
    return prevProps.footerHeight === nextProps.footerHeight;
});

export default ChatRoom;

const styles = StyleSheet.create({
    sectionList: {
        flex: 1,
        paddingHorizontal: PADDING_HZT,
        height: '100%',
        width: '100%'
    },
    noMessagesContainer: {
        alignItems: 'center',
        flex: 1,
        marginTop: 50
    },
    defaultText: {
        fontSize: 15,
        marginHorizontal: "auto",
        marginVertical: 20,
        fontFamily: "InterSemiBold",
        fontWeight: "700",
    },
    sectionHeaderFooter: {
        alignItems: 'center',
    },
    sectionFooterText: {
        paddingVertical: 5,
        paddingHorizontal: 40,
        borderRadius: 20,
        borderWidth: 1,
        borderColor: Colors.dark.darkGrey,
        marginTop: 15,
        marginBottom: 20
    },
    loadingContainer: {
        flex: 1,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        zIndex: 1000
    },
    scrollToBottomIcon: {
        position: 'absolute',
        right: (screenWidth - (PADDING_HZT * 2)) / 2,
        zIndex: 1000,
        elevation: 1,
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderColor: Colors.dark.darkGrey,
        height: 40,
        width: 40,
        borderRadius: 20,
        backgroundColor: Colors.dark.background,
    }
});
