import {
    View,
  } from "react-native";
  import React, { memo } from "react";
  import { ANIMATION_TYPE, Skeleton } from "@/components/ui/skaleton";

export const ListSkeleton = memo(() => {
    return [1, 2, 3, 4, 5, 6].map((_, index) => (
        <View key={index}  style={{ flexDirection: 'row', paddingHorizontal: 25, marginVertical: 10 }}>
          <Skeleton
            style={{ borderRadius: 25 }}
            width={50}
            animationType={ANIMATION_TYPE.pulse}
            height={50}
          />
          <View style={{flex: 1, marginLeft: 10, justifyContent: 'center', gap: 10}}>
            <Skeleton
              width={"50%"}
              animationType={ANIMATION_TYPE.pulse}
              height={13}
            />
            <Skeleton
              width={"100%"}
              animationType={ANIMATION_TYPE.pulse}
              height={13}
            />
          </View>
        </View>
      ))
})