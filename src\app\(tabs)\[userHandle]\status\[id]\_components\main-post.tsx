import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useDeleteThreadMutation,
  useLikeThreadMutation,
  useRepostThreadMutation,
  useThreadByIdQuery,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
  useUserByHandleQuery,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { ThreadResponse } from "@/types";
import { useLocalSearchParams } from "expo-router";
import { MainPostUI } from "@/components/post";
import React from "react";
import {
  MainPostLoadingSkeleton,
} from "@/components/post-loading-skeletons";

export const MainPost = () => {
  const params = useLocalSearchParams() as { userHandle: string; id: string };
  const { data: userData } = useUserByHandleQuery(params.userHandle);
  const queryClient = useQueryClient();
  const { data, isLoading } = useThreadByIdQuery(params.id);

  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ["threads", params.id] });

      const previousThread = queryClient.getQueryData(["threads", params.id]);

      queryClient.setQueryData(
        ["threads", params.id],
        (old: ThreadResponse) => {
          return {
            ...old,
            thread: {
              ...old.thread,
              repostCount: old.thread.repostCount + 1,
              reposted: true,
            },
          };
        }
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["threads", params.id], context.previousThread);
    },
    onSuccess: (_, variables) => {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ["threads", params.id] });

      const previousThread = queryClient.getQueryData(["threads", params.id]);

      queryClient.setQueryData(
        ["threads", params.id],
        (old: ThreadResponse) => {
          return {
            ...old,
            thread: {
              ...old.thread,
              repostCount: old.thread.repostCount - 1,
              reposted: false,
            },
          };
        }
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["threads", params.id], context.previousThread);
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
    },
  });

  const { mutateAsync: deleteThread } = useDeleteThreadMutation({
    onMutate: async ({ threadId }) => {
      toast.red("Post deleted");
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed
      );
    },
  });

  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ["threads", params.id] });

      const previousThread = queryClient.getQueryData(["threads", params.id]);

      queryClient.setQueryData(
        ["threads", params.id],
        (old: ThreadResponse) => {
          return {
            ...old,
            thread: {
              ...old.thread,
              likeCount: old.thread.likeCount + 1,
              like: true,
            },
          };
        }
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["threads", params.id], context.previousThread);
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ["threads", params.id] });

      const previousThread = queryClient.getQueryData(["threads", params.id]);

      queryClient.setQueryData(
        ["threads", params.id],
        (old: ThreadResponse) => {
          return {
            ...old,
            thread: {
              ...old.thread,
              likeCount: old.thread.likeCount - 1,
              like: false,
            },
          };
        }
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["threads", params.id], context.previousThread);
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
    },
  });
  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ["threads", params.id] });

      const previousThread = queryClient.getQueryData(["threads", params.id]);

      queryClient.setQueryData(
        ["threads", params.id],
        (old: ThreadResponse) => {
          return {
            ...old,
            thread: {
              ...old.thread,
              bookmarkCount: old.thread.bookmarkCount + 1,
              bookmark: true,
            },
          };
        }
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["threads", params.id], context.previousThread);
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ["threads", params.id] });

      const previousThread = queryClient.getQueryData(["threads", params.id]);

      queryClient.setQueryData(
        ["threads", params.id],
        (old: ThreadResponse) => {
          return {
            ...old,
            thread: {
              ...old.thread,
              bookmarkCount: old.thread.bookmarkCount - 1,
              bookmark: false,
            },
          };
        }
      );

      return { previousThread };
    },
    onError: (err, variables, context) => {
      queryClient.setQueryData(["threads", params.id], context.previousThread);
    },
    onSuccess(_, variables) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === variables.threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === variables.threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
    },
  });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (data?.thread.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (data?.thread.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (data?.thread.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  const handleDelete = async ({ threadId }: { threadId: string }) => {
    await deleteThread({ threadId });
  };

  return (
    <>
      {isLoading && <MainPostLoadingSkeleton />}
      {!isLoading && data && (
        <MainPostUI
          thread={data.thread}
          handleLike={handleLike}
          handleBookmark={handleBookmark}
          handleRepost={handleRepost}
          handleDelete={handleDelete}
        />
      )}
    </>
  );
};
