// styles.js or styles.ts
import { Colors } from "@/constants/Colors";
import { Dimensions, StyleSheet } from "react-native";

const { width: windowWidth, height: windowHeight } = Dimensions.get("window");

export const globalStyles = StyleSheet.create({
  default: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    color: "rgba(237, 237, 237, 1)",
  },
  defaultGrey: {
    fontSize: 14,
    color: "rgba(157, 157, 157, 1)",
    fontFamily: "InterRegular",
    fontWeight: "400",
  },
  defaultSemiBold: {
    fontSize: 16,
    fontWeight: "600",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
  },
  subtitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  link: {
    fontSize: 16,
    color: "#0a7ea4",
  },
  lightGreySubtitle: {
    fontSize: 13,
    color: "rgba(181, 181, 181, 1)",
    fontWeight: "500",
  },
  greyText: {
    color: "rgba(128, 128, 128, 1)",
  },
  centerAligned: {
    justifyContent: "center",
    alignItems: "center",
  },
  rowSpaceBetween: {
    alignItems: "center",
    justifyContent: "space-between",
  },
  windowWidth: {
    width: windowWidth,
  },
  windowHeight: {
    width: windowHeight,
  },
  bottomSheetHeader: {
    fontWeight: "600",
    fontFamily: "InterSemiBold",
    fontSize: 16,
    marginBottom: 22,
  },
  textSmall: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.dark.offWhite,
  },
  fontBold: {
    fontFamily: "InterSemiBold"
  }
});
