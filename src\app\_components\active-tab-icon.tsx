import { StyleSheet, View } from "react-native";

interface IconWithDotProps {
  icon: JSX.Element;
  focused: boolean;
}

export const ActiveTabIcon: React.FC<IconWithDotProps> = ({ icon, focused }) => (
  <View style={styles.iconContainer}>
    {icon}
    {focused && <View style={styles.redDot} />}
  </View>
);

const styles = StyleSheet.create({
  iconContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  redDot: {
    position: "absolute",
    bottom: -8,
    width: 4,
    height: 4,
    borderRadius: 4,
    backgroundColor: "red",
  },
});
