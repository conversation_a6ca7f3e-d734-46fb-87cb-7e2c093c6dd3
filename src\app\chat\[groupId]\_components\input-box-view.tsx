import { SendOutlineIcon } from "@/components/icons"
import { AddCircleOutlineIcon } from "@/components/icons"
import { SmileyFilledIcon } from "@/components/icons-v2/smiley-filled"
import { toast } from "@/components/toast"
import { Colors } from "@/constants/Colors"
import { useSendMessageMutation } from "@/queries/chat-mutations"
import { InfiniteData } from "@tanstack/react-query"
import { removeParagraphTags } from "@/utils/string-formatter"
import { useQueryClient } from "@tanstack/react-query"
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from "react"
import { TouchableOpacity, View, StyleSheet, TextInput, LayoutChangeEvent } from "react-native"
import { MessageType } from "@/queries/types"
import { v4 as uuidv4 } from "uuid";
import { AxiosError } from "axios"
import { useGroup, useGroupStore } from "../_contexts/group-context"
import { useUser } from "@/stores/user"
import { EmojiSelector } from "@/components/ui/emoji-picker"
import { EmojiType } from "rn-emoji-keyboard"
import { useSocket } from "@/stores/socket"
import { SOCKET_MESSAGE } from "@/environments/socket-messages"
import AsyncStorage from '@react-native-async-storage/async-storage';

const MAX_CHARACTERS = 1000;

type TInputBoxView = {
    onLayout: (event: LayoutChangeEvent) => void;
}

export const InputBoxView = forwardRef<TextInput, TInputBoxView>((props, forwardedRef) => {

    const internalRef = useRef<TextInput>(null);

    //hooks
    const {
        groupId,
        setIsAttachImageOpen,
        messageToReplyTo,
        setMessageToReplyTo,
        preview,
        setPreview,
        files,
        setFiles,
        data,
    } = useGroup();
    const { group } = data || {};
    const { isDirect, ownerUserId } = group || {};
    const { user } = useUser();
    const { socket } = useSocket();
    const messageId = useGroupStore((state) => state.messageId);
    const queryClient = useQueryClient();

    //states
    const [message, setMessage] = useState("");
    const isSendDisabled = message.trim() === "" && files?.length === 0;
    const isAddAttachmentDisabled = !!preview;
    const [isEmojiKeyboardOpen, setIsEmojiKeyboardOpen] = useState(false);
    const timeout = useRef<ReturnType<typeof setTimeout> | null>(null);
    const [throttledValue, setThrottledValue] = useState<string | null>("");
    const lastUpdated = useRef<number | null>(null);
    
    const MESSAGE_DRAFT_KEY = `message-draft-${groupId}`;

    const storeDraft = async (message: string) => {
        try {
            await AsyncStorage.setItem(MESSAGE_DRAFT_KEY, message);
        } catch (error) {
            console.error("Error saving draft: ", error);
        }
    };

    const loadDraft = async () => {
        try {
            const draftedText = await AsyncStorage.getItem(MESSAGE_DRAFT_KEY);
            if (draftedText) {
                setMessage(draftedText);
            }
        } catch (error) {
            console.error("Error loading draft: ", error);
        }
    }

    const clearDraft = async () => {
        try {
            await AsyncStorage.removeItem(MESSAGE_DRAFT_KEY);
        } catch (error) {
            console.error("Error clearing draft: ", error);
        }
    };

    useEffect(()=>{
        loadDraft();
    },[]);

    useEffect(() => {
        if (socket && groupId && user) {
            socket.emit(SOCKET_MESSAGE.CHAT_TYPING, {
                groupId: groupId,
                user: { id: user.id, name: user.twitterName },
            });
        }

        return () => {
            if (timeout.current) {
                clearTimeout(timeout.current);
            }
        };
    }, [socket, groupId, user, throttledValue]);

    useImperativeHandle(
        forwardedRef,
        () => {
            return {
                focus() {
                    internalRef.current?.focus();
                },
            };
        },
        [internalRef],
    );

    const { mutate } = useSendMessageMutation({
        onMutate: (variables) => {
            const previousMessages = queryClient.getQueryData([
                "chat",
                "group-infinite-messages",
                {
                    groupId: groupId,
                    messageId,
                },
            ]);
            queryClient.setQueryData(
                [
                    "chat",
                    "group-infinite-messages",
                    {
                        groupId: groupId,
                        messageId,
                    },
                ],
                (
                    old: InfiniteData<{
                        messages: MessageType[];
                    }>,
                ) => {
                    if (!old) return old;
                    const messageId = uuidv4();
                    return {
                        ...old,
                        pages: old.pages.map((page, index) => {
                            //check the first page which is latest page
                            if (index === 0) {
                                return {
                                    ...page,
                                    messages: [
                                        {
                                            attachments:
                                                variables.files && variables.files?.length > 0
                                                    ? [
                                                        {
                                                            id: variables.files[0].id,
                                                            messageId,
                                                            messageType:
                                                                variables.files[0].fileType === "image"
                                                                    ? 2
                                                                    : 3,
                                                            url: variables.files[0].url,
                                                        },
                                                    ]
                                                    : [],
                                            createdOn: Date.now().toString(),
                                            id: `remove-${messageId}`,
                                            message: variables.text,
                                            reply: messageToReplyTo
                                                ? messageToReplyTo
                                                : undefined,
                                            groupId: groupId,
                                            messageType: 0,
                                            picture: user?.twitterPicture || "",
                                            userId: user?.id || "",
                                            userName: user?.twitterName || "",
                                        },
                                        ...page.messages,
                                    ],
                                };
                            }
                            return page;
                        }),
                    };
                },
            );
            return { previousMessages };
        },
        onError: (err, variables, context) => {
            console.log(err);
            if (err instanceof AxiosError) {
                if (
                    err.response?.data.message === "You are not a member of this group"
                ) {
                    if (isDirect && ownerUserId !== user?.id) {
                        toast.red("Your DM request is deleted by this user.");
                    } else if (isDirect && ownerUserId === user?.id) {
                        toast.red("You have deleted this user's DM request.");
                    }
                }
            }
            if (!messageId) {
                queryClient.setQueryData(
                    [
                        "chat",
                        "group-infinite-messages",
                        {
                            groupId: groupId,
                            messageId,
                        },
                    ],
                    context?.previousMessages,
                );
            }
            //clear reply message
            setMessageToReplyTo?.(null);
        },
        onSuccess: () => {
            //clear reply message
            setMessageToReplyTo?.(null);
            // invalidate conversations and direct messages
            queryClient.invalidateQueries({
                queryKey: ["chat", "conversations"],
            });
            queryClient.invalidateQueries({
                queryKey: ["chat", "direct-messages"],
            });
        },
    });

    const handleMessageChange = (text: string) => {
        setMessage(text);
        storeDraft(text);
        const interval = 700;
        const now = Date.now();
        if (lastUpdated.current && now >= lastUpdated.current + interval) {
          lastUpdated.current = now;
          setThrottledValue(text);
        } else {
          timeout.current = setTimeout(() => {
            lastUpdated.current = now;
            setThrottledValue(text);
          }, interval);
        }
    }

    const handlePick = (emojiObject: EmojiType) => {
        setMessage(prev => prev + emojiObject.emoji);
    }

    const handleSend = (msg: string) => {
        if (!groupId) {
            return;
        }
        let updatedMsg = msg.trim().length > 0 ? removeParagraphTags(msg) : "";
        if (updatedMsg.length > MAX_CHARACTERS) {
            toast.red(
                `Message too long. Please limit to ${MAX_CHARACTERS} characters.`,
            );
            return;
        }
        const request = {
            groupId: groupId,
            files: files || [],
            text: updatedMsg,
            replyId: messageToReplyTo ? messageToReplyTo.id : undefined,
        }
        setMessage('');
        clearDraft();
        setPreview?.(null);
        setFiles?.([]);
        setIsAttachImageOpen?.(false);
        mutate(request);
    };

    const handleAddAttachmentClick = () => {
        console.log("handleAddAttachmentClick");
        setIsAttachImageOpen?.(prev => {
            console.log("prev", prev);
            return !prev;
        });
    }

    return (
        <>
            <View onLayout={props.onLayout} style={[styles.inputAndSendBtnWrapper]}>
                <View style={styles.inputWrapper}>
                    <View style={styles.inputOptionsContainer}>
                        <TouchableOpacity style={styles.emojiButton} onPress={() => setIsEmojiKeyboardOpen(prev => !prev)}>
                            <SmileyFilledIcon
                                width={29}
                                height={29}
                                fill={Colors.dark.offWhite}
                            />
                        </TouchableOpacity>
                        <View
                            style={styles.inputContainer}
                        >
                            <TextInput
                                placeholder="Message"
                                ref={internalRef}
                                placeholderTextColor={Colors.dark.offWhite}
                                cursorColor={Colors.dark.offWhite}
                                multiline
                                style={styles.input}
                                value={message}
                                onChangeText={handleMessageChange}
                            />
                        </View>
                        <TouchableOpacity
                            style={{ opacity: isAddAttachmentDisabled ? 0.5 : 1 }}
                            disabled={isAddAttachmentDisabled}
                            onPress={handleAddAttachmentClick}
                        >
                            <AddCircleOutlineIcon
                                width={29}
                                height={29}
                                color={Colors.dark.offWhite}
                            />
                        </TouchableOpacity>
                    </View>
                </View>
                <TouchableOpacity
                    style={[styles.sendButton, { opacity: isSendDisabled ? 0.5 : 1 }]}
                    disabled={isSendDisabled}
                    onPress={() => handleSend(message)}
                >
                    <SendOutlineIcon width={28} height={28} color={Colors.dark.white} />
                </TouchableOpacity>
            </View>
            <EmojiSelector
                isEmojiKeyboardOpen={isEmojiKeyboardOpen}
                setIsEmojiKeyboardOpen={setIsEmojiKeyboardOpen}
                onEmojiSelected={handlePick}
            />
        </>
    )
})

const styles = StyleSheet.create({
    inputAndSendBtnWrapper: {
        flexDirection: "row",
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 10,
        paddingHorizontal: 20,
    },
    inputWrapper: {
        width: '87%',
        paddingVertical: 7,
        gap: 12
    },
    emojiButton: {
        marginRight: 5,
    },
    sendButton: {
        paddingVertical: 7,
        width: '13%',
        alignItems: 'flex-end',
    },
    inputContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        paddingBottom: 5,
        paddingHorizontal: 10,
    },
    input: {
        borderWidth: 0,
        color: Colors.dark.white,
        fontSize: 18,
        flex: 1,
    },
    inputOptionsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
})