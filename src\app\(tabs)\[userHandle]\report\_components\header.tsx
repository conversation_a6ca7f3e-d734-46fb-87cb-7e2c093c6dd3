import { View, Pressable } from "react-native";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { ThemedText } from "@/components/ThemedText";

interface HeaderProps {
  onBack: () => void;
  title?: string;
}

const Header = ({ onBack, title = "Report User" }: HeaderProps) => {
  return (
    <View className="flex-row items-center h-12 mb-4">
      <Pressable onPress={onBack}>
        <ArrowBackOutlineIcon className="text-off-white" width={24} height={24} />
      </Pressable>
      <ThemedText type="bold" className="flex-1 mr-2 text-center text-xl" style={{ fontSize: 18 }}>{title}</ThemedText>
    </View>
  );
};

export { Header };
