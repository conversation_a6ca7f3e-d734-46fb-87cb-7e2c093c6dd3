import { useMemo } from "react";

import { useGlobalSearchParams } from "expo-router";
import { FlatList, Image, View } from "react-native";

import { Button } from "@/components/ui/button";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useUserByHandleQuery } from "@/queries/user-queries";
import { useSharesStatsQuery } from "@/queries/shares-queries";

import { formatPrice } from "../_components/profile-header";

interface StatsData {
  statName: string;
  value: any;
}

const StatsCard = ({ statName, value }: StatsData) => {
  let buySellValue = statName === "Buys/Sells" ? value.split("/") : "";

  return (
    <ThemedView
      className="rounded-lg p-4 mb-2 gap-1.5"
      style={{ borderWidth: 0.5, borderColor: "#3B3B3B", flex: 1 }}
    >
      <ThemedText className="text-xs" style={{ color: '#A3A3A3' }}>{statName}</ThemedText>
      <ThemedView className="flex-row items-center" style={{ gap: 3 }}>
        {statName === "Total Volume" ? (
          <Image source={require("@assets/icons/statsVolumeLogo.png")} />
        ) : null}
        {statName === "Buys/Sells" ? (
          <ThemedView className="flex-row items-center">
            <ThemedText  style={{ color: 'green' }}>{buySellValue[0]}</ThemedText>
            <ThemedText>/</ThemedText>
            <ThemedText style={{ color: 'red' }}>{buySellValue[1]}</ThemedText>
          </ThemedView>
        ) : (
          <ThemedText>{value || 0}</ThemedText>
        )}
      </ThemedView>
    </ThemedView>
  );
};

export default function ProfileStats() {
  const params = useGlobalSearchParams() as { userHandle: string };

  const { data, isLoading: isUserDataLoading } = useUserByHandleQuery(
    params.userHandle
  );

  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: data?.user?.id,
    });

  const volume = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatPrice(statsData?.stats?.volume ?? "0");

    return formattedEther;
  }, [isStatsDataLoading, isUserDataLoading, statsData?.stats?.volume]);

  const generateBuysSellsValue = (
    buys: number | undefined,
    sells: number | undefined
  ) => `+${buys || 0}/-${sells || 0}`;

  const statsFlatListData = useMemo(() => {
    return [
      {
        statName: "Creator Fee(mock)",
        value: "7%",
      },
      {
        statName: "Buys/Sells",
        value: generateBuysSellsValue(
          statsData?.stats?.buys || 0,
          statsData?.stats?.sells || 0
        ),
      },
      {
        statName: "You hold",
        value: statsData?.holdingsByUser || 0,
      },
      {
        statName: "Supply",
        value: statsData?.stats?.supply || 0,
      },
      {
        statName: "Holdings",
        value: statsData?.totalHoldings || 0,
      },
      {
        statName: "Holders",
        value: statsData?.totalHolders || 0,
      },
      {
        statName: "Total Volume",
        value: volume,
      },
    ];
  }, [statsData]);

  const arenaBookBtnHandler = () => {};

  return (
    <View className="flex-1 px-2 pt-5 pb-5">
      <FlatList
        data={statsFlatListData}
        numColumns={2}
        keyExtractor={(item, index) => item.statName || index.toString()}
        renderItem={({ item }: { item: StatsData }) => (
          <StatsCard statName={item.statName} value={item.value} />
        )}
        columnWrapperStyle={{ justifyContent: "space-between", gap: 10 }}
        contentContainerStyle={{ paddingHorizontal: 10, paddingVertical: 20 }}
        ItemSeparatorComponent={() => <View style={{ height: 10 }} />}
      />
      <Button onPress={arenaBookBtnHandler} >See in ArenaBook</Button>
    </View>
  );
}
