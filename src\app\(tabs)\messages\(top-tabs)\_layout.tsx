import { StyleSheet } from "react-native";
import { router, withLayoutContext } from "expo-router";
import { Colors } from "@/constants/Colors";
import {
    createMaterialTopTabNavigator,
    MaterialTopTabNavigationEventMap,
    MaterialTopTabNavigationOptions,
} from "@react-navigation/material-top-tabs";
import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import { MessagingHeader } from "../_components/messaging-header";
import { MessagingProvider } from "../_contexts/messaging-context";
import { InfiniteData } from "@tanstack/react-query";
import { useEffect } from "react";
import { useUser } from "@/stores";
import { useQueryClient } from "@tanstack/react-query";
import { useSocket } from "@/hooks/use-socket";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { GroupsResponse } from "@/queries/types/chats";

const { Navigator } = createMaterialTopTabNavigator();

export const MessagingTopTabs = withLayoutContext<
    MaterialTopTabNavigationOptions,
    typeof Navigator,
    TabNavigationState<ParamListBase>,
    MaterialTopTabNavigationEventMap
>(Navigator);

export const paddingHorizontal = 25;

const MessagingTopTabLayout = () => {
    const { socket } = useSocket();
    const queryClient = useQueryClient();
    const { user } = useUser();

    useEffect(() => {
        socket?.on(SOCKET_MESSAGE.CHAT_MESSAGE_GROUP, (message) => {
            if (!message.group) return;
            if (message.group?.isDirect) {
                queryClient.setQueryData(
                    ["chat", "direct-messages"],
                    (oldData: InfiniteData<GroupsResponse>) => {
                        if (!oldData) return;

                        return {
                            ...oldData,
                            pages: oldData.pages.map((page) => {
                                return {
                                    ...page,
                                    groups: page.groups.map((g) => {
                                        if (g.id === message.group?.id) {
                                            return {
                                                ...g,
                                                ...message.group,
                                            };
                                        }

                                        return g;
                                    }),
                                };
                            }),
                        };
                    },
                );
            }

            queryClient.setQueryData(
                ["chat", "conversations"],
                (oldData: InfiniteData<GroupsResponse>) => {
                    if (!oldData) return;

                    return {
                        ...oldData,
                        pages: oldData.pages.map((page) => {
                            return {
                                ...page,
                                groups: page.groups.map((g) => {
                                    if (g.id === message.group?.id) {
                                        return {
                                            ...g,
                                            ...message.group,
                                        };
                                    }

                                    return g;
                                }),
                            };
                        }),
                    };
                },
            );
        });
    }, [socket, queryClient, user?.id]);

    useEffect(() => {
        socket?.on(SOCKET_MESSAGE.CHAT_MESSAGE_GROUP, (message) => {
            if (!message.group) return;
            if (!message.group?.isDirect) return;

            queryClient.setQueryData(
                ["chat", "direct-messages"],
                (oldData: InfiniteData<GroupsResponse>) => {
                    if (!oldData) return;

                    return {
                        ...oldData,
                        pages: oldData.pages.map((page) => {
                            return {
                                ...page,
                                groups: page.groups.map((g) => {
                                    if (g.id === message.group?.id) {
                                        return {
                                            ...g,
                                            ...message.group,
                                        };
                                    }

                                    return g;
                                }),
                            };
                        }),
                    };
                },
            );
        });
    }, [socket, queryClient, user?.id]);

    useEffect(() => {
        socket?.on(SOCKET_MESSAGE.LEAVE_GROUP, (message) => {
            if (!message.group) return;
            if (!message.group?.isDirect) return;

            queryClient.setQueryData(
                ["chat", "direct-messages"],
                (oldData: InfiniteData<GroupsResponse>) => {
                    if (!oldData) return;

                    return {
                        ...oldData,
                        pages: oldData.pages.map((page) => {
                            return {
                                ...page,
                                groups: page.groups.filter((g) => g.id !== message.group.id),
                            };
                        }),
                    };
                },
            );
        });

        socket?.on(SOCKET_MESSAGE.ACCEPT_GROUP, (message) => {
            if (!message.group) return;
            if (!message.group?.isDirect) return;

            queryClient.setQueryData(
                ["chat", "direct-messages"],
                (oldData: InfiniteData<GroupsResponse>) => {
                    if (!oldData) return;

                    return {
                        ...oldData,
                        pages: oldData.pages.map((page) => {
                            return {
                                ...page,
                                groups: page.groups.map((g) => {
                                    if (g.id === message.group.id) {
                                        return {
                                            ...g,
                                            isRequest: false,
                                        };
                                    }
                                    return g;
                                }),
                            };
                        }),
                    };
                },
            );
        });
    }, [socket, queryClient, user?.id]);

    return (
        <MessagingProvider>
            <MessagingHeader />
            <MessagingTopTabs
                screenOptions={{
                    tabBarStyle: styles.tabBarStyle,
                    tabBarItemStyle: styles.tabBarItemStyle,
                    tabBarLabelStyle: styles.tabBarLabelStyle,
                    tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
                    tabBarActiveTintColor: Colors.dark.offWhite,
                    tabBarInactiveTintColor: Colors.dark.darkGrey,
                }}
            >
                <MessagingTopTabs.Screen name="index" options={{ title: "Ticket Chats" }} />
                <MessagingTopTabs.Screen name="dms" options={{ title: "DMs" }} />
            </MessagingTopTabs>
        </MessagingProvider>
    );
}

export default MessagingTopTabLayout;

const styles = StyleSheet.create({
    tabBarStyle: {
        backgroundColor: "transparent",
    },
    tabBarItemStyle: {
        padding: 0,
    },
    tabBarLabelStyle: {
        fontSize: 14,
        fontWeight: "400",
        fontFamily: "InterSemiBold",
        textTransform: "none",
    },
    tabBarIndicatorStyle: {
        backgroundColor: Colors.dark.primary,
    }
});