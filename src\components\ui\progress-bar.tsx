import React, { useEffect } from 'react';
import { View, ViewStyle, StyleSheet } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming 
} from 'react-native-reanimated';

interface ProgressBarProps {
  progressValue: number;
  barStyle?: ViewStyle;
  height?: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progressValue, barStyle, height }) => {
  const normalizedProgress = progressValue / 100;
  
  const width = useSharedValue(0);
  
  useEffect(() => {
    const clampedValue = Math.min(Math.max(normalizedProgress, 0), 1);
    width.value = withTiming(clampedValue, { duration: 300 });
  }, [progressValue]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: `${width.value * 100}%`,
    };
  });

  return (
      <Animated.View 
        style={[{ height: height ?? 4 }, styles.progressBar, animatedStyle, barStyle]}
      />
  );
};

const styles = StyleSheet.create({
    progressBar: {
      minWidth: 16,
      backgroundColor: '#FF5500',
    },
  });  

export default ProgressBar;
