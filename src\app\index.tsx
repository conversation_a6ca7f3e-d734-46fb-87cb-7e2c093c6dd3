import ArenaLogo from '@/components/ArenaLogo';
import { ThemedView } from '@/components/ThemedView';
import { getCookie } from '@/cookies/secure-store';
import { navigationRoutes } from '@/navigationRoutes';
import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';

const index = () => {
  useEffect(() => {
    getCookie('token')
      .then(tokenCookie => {
        if (tokenCookie) {
          router.replace(navigationRoutes.home);
        } else {
          router.replace(navigationRoutes.login);
        }
      })
      .catch(error => {
        console.error('Error getting token cookie:', error);
        router.replace(navigationRoutes.login);
      });
  }, []);

  // This is a placeholder screen while we check for the token
  // and navigate to the appropriate screen.
  return (
    <ThemedView style={styles.container}>
      <ArenaLogo />
      <View style={styles.spacer} />
      <Text style={styles.text}>Please wait ...</Text>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  spacer: {
    height: 30,
  },
  text: {
    textAlign: 'center',
  },
});

export default index;
