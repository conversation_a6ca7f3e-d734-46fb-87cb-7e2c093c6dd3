import React, { createContext, useContext } from "react";
import {
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  createMaterialTopTabNavigator,
} from "@react-navigation/material-top-tabs";
import { withLayoutContext } from "expo-router";
import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import { StyleSheet } from "react-native";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  withTiming,
  useDerivedValue,
  Easing,
  SharedValue,
} from "react-native-reanimated";
import { Colors } from "@/constants/Colors";
import ProfileHeader from "../_components/profile-header";

const { Navigator } = createMaterialTopTabNavigator();

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

// Context to provide scrollY to tab screens using reanimated shared values
const ScrollSyncContext = createContext<{
  scrollY: SharedValue<number>;
  isScrollingDown: SharedValue<boolean>;
} | null>(null);

export const useScrollSync = () => {
  const ctx = useContext(ScrollSyncContext);
  if (!ctx) throw new Error("useScrollSync must be used within ScrollSyncContext.Provider");
  return ctx;
};

export default function TabLayout() {
  // Animation constants
  const maxHeaderHeight = 200;
  const minHeaderHeight = 80;
  const maxTabBarHeight = 60;
  const minTabBarHeight = 32;
  const scrollThreshold = maxHeaderHeight - minHeaderHeight;

  // Shared values for smooth animations
  const scrollY = useSharedValue(0);
  const isScrollingDown = useSharedValue(false);
  const lastScrollY = useSharedValue(0);
  const headerProgress = useSharedValue(0);

  // Derived value to track scroll direction and update smooth progress
  useDerivedValue(() => {
    const currentScroll = scrollY.value;
    const diff = currentScroll - lastScrollY.value;

    // Update scroll direction
    if (Math.abs(diff) > 1) {
      isScrollingDown.value = diff > 0;
      lastScrollY.value = currentScroll;
    }

    // Create smooth progress with momentum
    const rawProgress = Math.min(Math.max(currentScroll / scrollThreshold, 0), 1);

    // Apply smooth timing to progress changes
    headerProgress.value = withTiming(rawProgress, {
      duration: 200,
      easing: Easing.out(Easing.cubic),
    });

    return rawProgress;
  });

  // Animated styles for header
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const height = interpolate(
      headerProgress.value,
      [0, 1],
      [maxHeaderHeight, minHeaderHeight],
      'clamp'
    );

    return {
      height,
      overflow: 'hidden',
    };
  });

  // Animated styles for tab bar
  const tabBarAnimatedStyle = useAnimatedStyle(() => {
    const height = interpolate(
      headerProgress.value,
      [0, 1],
      [maxTabBarHeight, minTabBarHeight],
      'clamp'
    );

    return {
      height,
    };
  });

  return (
    <ScrollSyncContext.Provider value={{ scrollY, isScrollingDown }}>
      <Animated.View style={headerAnimatedStyle}>
        <ProfileHeader />
      </Animated.View>
      <MaterialTopTabs
        screenOptions={{
          tabBarStyle: [
            styles.tabBarStyle,
            tabBarAnimatedStyle,
          ],
          tabBarItemStyle: styles.tabBarItemStyle,
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
        }}
      >
        <MaterialTopTabs.Screen name="index" options={{ title: "Threads" }} />
        <MaterialTopTabs.Screen
          name="profileTicketHolders"
          options={{ title: "Ticket Holders" }}
        />
        <MaterialTopTabs.Screen
          name="profileStats"
          options={{ title: "Stats" }}
        />
      </MaterialTopTabs>
    </ScrollSyncContext.Provider>
  );
}

const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: "transparent",
  },
  tabBarItemStyle: {
    padding: 0
  },
  tabBarLabelStyle: {
    fontSize: 13,
    textTransform: "none",
  },
  tabBarIndicatorStyle: {
    backgroundColor: Colors.dark.primary
  }
});

// ---
// Usage in a tab screen (e.g. index.tsx):
// import { useScrollSync } from "./_layout";
// import Animated, { useAnimatedScrollHandler } from "react-native-reanimated";
//
// export default function TabScreen() {
//   const { scrollY } = useScrollSync();
//
//   const scrollHandler = useAnimatedScrollHandler({
//     onScroll: (event) => {
//       scrollY.value = event.contentOffset.y;
//     },
//   });
//
//   return (
//     <Animated.ScrollView
//       onScroll={scrollHandler}
//       scrollEventThrottle={1}
//     >
//       {/* ...content... */}
//     </Animated.ScrollView>
//   );
// }
