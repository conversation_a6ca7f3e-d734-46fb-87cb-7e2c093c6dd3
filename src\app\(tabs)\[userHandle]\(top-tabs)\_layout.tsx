import React, { useRef, createContext, useContext } from "react";
import {
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  createMaterialTopTabNavigator,
} from "@react-navigation/material-top-tabs";
import { withLayoutContext } from "expo-router";
import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import {
  Dimensions,
  Image,
  Pressable,
  StyleSheet,
  Text,
  View,
  Animated,
} from "react-native";
import { Colors } from "@/constants/Colors";
import ProfileHeader from "../_components/profile-header";

const { Navigator } = createMaterialTopTabNavigator();

const { height, width } = Dimensions.get("window");

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

// Context to provide scrollY to tab screens
const ScrollSyncContext = createContext<Animated.Value | null>(null);
export const useScrollSync = () => {
  const ctx = useContext(ScrollSyncContext);
  if (!ctx) throw new Error("useScrollSync must be used within ScrollSyncContext.Provider");
  return ctx;
};

export default function TabLayout() {
  const scrollY = useRef(new Animated.Value(0)).current;

  const maxHeaderHeight = 200;
  const minHeaderHeight = 80;
  const maxTabBarHeight = 60;
  const minTabBarHeight = 32;

  const headerHeight = scrollY.interpolate({
    inputRange: [0, maxHeaderHeight - minHeaderHeight],
    outputRange: [maxHeaderHeight, minHeaderHeight],
    extrapolate: "clamp",
  });

  const tabBarHeight = scrollY.interpolate({
    inputRange: [0, maxHeaderHeight - minHeaderHeight],
    outputRange: [maxTabBarHeight, minTabBarHeight],
    extrapolate: "clamp",
  });

  return (
    <ScrollSyncContext.Provider value={scrollY}>
      <Animated.View style={{ height: headerHeight, overflow: 'hidden' }}>
        <ProfileHeader />
      </Animated.View>
      <MaterialTopTabs
        screenOptions={{
          tabBarStyle: [
            styles.tabBarStyle,
            { height: tabBarHeight },
          ],
          tabBarItemStyle: styles.tabBarItemStyle,
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
        }}
      >
        <MaterialTopTabs.Screen name="index" options={{ title: "Threads" }} />
        <MaterialTopTabs.Screen
          name="profileTicketHolders"
          options={{ title: "Ticket Holders" }}
        />
        <MaterialTopTabs.Screen
          name="profileStats"
          options={{ title: "Stats" }}
        />
      </MaterialTopTabs>
    </ScrollSyncContext.Provider>
  );
}

const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: "transparent",
  },
  tabBarItemStyle: {
    padding: 0
  },
  tabBarLabelStyle: {
    fontSize: 13,
    textTransform: "none",
  },
  tabBarIndicatorStyle: {
    backgroundColor: Colors.dark.primary
  }
});

// ---
// Usage in a tab screen (e.g. index.tsx):
// import { useScrollSync } from "./_layout";
// import { Animated } from "react-native";
//
// export default function TabScreen() {
//   const scrollY = useScrollSync();
//   return (
//     <Animated.ScrollView
//       onScroll={Animated.event(
//         [{ nativeEvent: { contentOffset: { y: scrollY } } }],
//         { useNativeDriver: false }
//       )}
//       scrollEventThrottle={16}
//     >
//       {/* ...content... */}
//     </Animated.ScrollView>
//   );
// }
