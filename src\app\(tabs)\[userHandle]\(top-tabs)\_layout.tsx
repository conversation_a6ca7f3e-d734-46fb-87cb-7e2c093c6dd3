import React from "react";
import {
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  createMaterialTopTabNavigator,
} from "@react-navigation/material-top-tabs";
import { withLayoutContext } from "expo-router";
import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import {
  StyleSheet,
} from "react-native";
import { Colors } from "@/constants/Colors";
import ProfileHeader from "../_components/profile-header";
import {
  CollapsibleHeaderProvider,
  CollapsibleHeader,
  useCollapsibleTabBarStyle,
  useCollapsibleHeader,
} from "@/components/ui/CollapsibleHeader";

const { Navigator } = createMaterialTopTabNavigator();

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

// Export hook for backward compatibility
export const useScrollSync = () => {
  const { scrollY } = useCollapsibleHeader();
  return scrollY;
};

const TabsContent: React.FC = () => {
  const tabBarStyle = useCollapsibleTabBarStyle({ style: styles.tabBarStyle });

  return (
    <MaterialTopTabs
      screenOptions={{
        tabBarStyle,
        tabBarItemStyle: styles.tabBarItemStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
        tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
      }}
    >
      <MaterialTopTabs.Screen name="index" options={{ title: "Threads" }} />
      <MaterialTopTabs.Screen
        name="profileTicketHolders"
        options={{ title: "Ticket Holders" }}
      />
      <MaterialTopTabs.Screen
        name="profileStats"
        options={{ title: "Stats" }}
      />
    </MaterialTopTabs>
  );
};

export default function TabLayout() {
  const headerConfig = {
    maxHeaderHeight: 200,
    minHeaderHeight: 80,
    maxTabBarHeight: 60,
    minTabBarHeight: 32,
    springConfig: {
      damping: 20,
      stiffness: 200,
      mass: 0.8,
    },
  };

  return (
    <CollapsibleHeaderProvider config={headerConfig}>
      <CollapsibleHeader>
        <ProfileHeader />
      </CollapsibleHeader>
      <TabsContent />
    </CollapsibleHeaderProvider>
  );
}

const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: "transparent",
  },
  tabBarItemStyle: {
    padding: 0
  },
  tabBarLabelStyle: {
    fontSize: 13,
    textTransform: "none",
  },
  tabBarIndicatorStyle: {
    backgroundColor: Colors.dark.primary
  }
});

// ---
// Usage in a tab screen (e.g. index.tsx):
// import { useScrollSync } from "./_layout";
// import { useCollapsibleScrollHandler } from "@/components/ui/CollapsibleHeader";
// import Animated from "react-native-reanimated";
//
// export default function TabScreen() {
//   const scrollHandler = useCollapsibleScrollHandler();
//
//   return (
//     <Animated.ScrollView
//       onScroll={scrollHandler.onScroll}
//       scrollEventThrottle={16}
//     >
//       {/* ...content... */}
//     </Animated.ScrollView>
//   );
// }
//
// Alternative usage with useScrollSync (backward compatibility):
// const scrollY = useScrollSync();
// const scrollHandler = useAnimatedScrollHandler({
//   onScroll: (event) => {
//     scrollY.value = event.contentOffset.y;
//   },
// });
