import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { FlatList, Image, Pressable, StyleSheet, View } from "react-native";
import { WalletProfileHeaderCards } from "./wallet-profile-header-cards";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useBottomSheet } from "@/components/ui/BottomSheetContext";
import { WalletWithdraw } from "./wallet-withdraw";
import { WalletDepostit } from "./wallet-deposit";
import { useMemo, useState } from "react";
import { AVAX } from "@/environments/tokens";
import { useUser } from "@/stores";
import { useSolanaAddressQuery } from "@/queries/chain-queries";
import { useSharesStatsQuery } from "@/queries/shares-queries";
import {
  useBalancesQuery,
  useSolanaBalanceQuery,
} from "@/queries/balance-queries";
import { formatEther, formatUnits } from "@/utils";

const sampleWalletCardData = [
  {
    type: "Primary",
    address: "QL23A47TRUBASLC",
    balance: "410,906",
    coin: "AVAX",
    coinImage: require("@assets/coins/avax.png"),
  },
  {
    type: "Card",
    address: "QL23A47TRUBASLC",
    balance: "21.90",
    coin: "AVAX",
    coinImage: require("@assets/coins/avax.png"),
  },
  {
    type: "Card",
    address: "QL23A47TRUBASLC",
    balance: "14.23",
    coin: "SOL",
    coinImage: require("@assets/coins/solanaLogoMark.png"),
  },
];

export const WalletCurrencyCards = () => {
  const { openBottomSheet } = useBottomSheet();
  const Separator = () => <View style={styles.separator} />;

  const withdraw = () => {
    openBottomSheet(<WalletWithdraw />, 450);
  };

  const deposit = () => {
    openBottomSheet(<WalletDepostit />, 500);
  };

  const [token, setToken] = useState<{
    name: string;
    icon: string;
    native?: boolean;
  }>(AVAX);
  const { user } = useUser();

  const { data: solanaAddressData } = useSolanaAddressQuery();

  const { data: stats, isLoading: isStatsLoading } = useSharesStatsQuery({
    userId: user?.id,
  });

  const { data: balances, isLoading: isBalancesLoading } = useBalancesQuery({
    address: user?.address,
  });
  const { data: solanaBalanceData } = useSolanaBalanceQuery({
    address: solanaAddressData?.response.solanaAddress,
  });

  const balance = useMemo(() => {
    if (!balances) return "0.00";

    if (token.name === "SOL") {
      if (!solanaAddressData?.response.solanaAddress) {
        return "-";
      }
      return solanaBalanceData?.SOL ?? "0.00";
    }

    if (
      token.name === "Bonk" ||
      token.name === "$WIF" ||
      token.name === "USEDCAR" ||
      token.name === "Moutai" ||
      token.name === "HARAMBE"
    ) {
      if (!solanaAddressData?.response.solanaAddress) {
        return "-";
      }

      return solanaBalanceData?.splTokens[token.name] ?? "0.00";
    }

    if (token.name === "MEAT") {
      return formatUnits(balances[token.name as keyof typeof balances], 6);
    }

    return formatEther(balances[token.name as keyof typeof balances]);
  }, [balances, solanaAddressData, solanaBalanceData, token]);

  const [earnings, portfolioValue] = useMemo(() => {
    let earnings = "0.00";
    let portfolioValue = "0.00";

    if (!stats) return [earnings, portfolioValue];

    if (stats?.stats?.feesEarned) {
      earnings = formatEther(stats?.stats?.feesEarned);
    }

    if (stats?.portfolioValue) {
      portfolioValue = formatEther(stats?.portfolioValue.toString());
    }

    return [earnings, portfolioValue];
  }, [stats]);

  return (
    <View style={styles.container}>
      <View style={styles.walletCardsWrapper}>
        <FlatList
          data={sampleWalletCardData}
          renderItem={({ item }) => (
            <WalletProfileHeaderCards
              type={item.type}
              address={item.address}
              balance={item.balance}
              coin={item.coin}
              coinImage={item.coinImage}
            />
          )}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          ItemSeparatorComponent={() => <Separator />}
          keyExtractor={(item, index) => item.address || index.toString()}
        />
      </View>
      <View style={styles.profileMetricsWrapper}>
        <View style={styles.profileMetricCard}>
          <ThemedText type="greyText">Your earnings</ThemedText>
          <View style={styles.profileMetricCardLogoWrapper}>
            <Image
              style={styles.profileMetricCardLogo}
              source={require("@assets/coins/avax.png")}
            />
            <ThemedText style={styles.avaxValue}>{earnings}</ThemedText>
          </View>
        </View>
        <View style={styles.profileMetricCard}>
          <ThemedText type="greyText">Portfolio value</ThemedText>
          <View style={styles.profileMetricCardLogoWrapper}>
            <Image
              style={styles.profileMetricCardLogo}
              source={require("@assets/coins/avax.png")}
            />
            <ThemedText style={styles.avaxValue}>{portfolioValue}</ThemedText>
          </View>
        </View>
      </View>
      <View style={styles.walletFunctionButtons}>
        <Button
          variant="outline"
          onPress={deposit}
          style={[styles.buttonTransparent, styles.fullButtonWidth]}
        >
          Deposit
        </Button>
        <Button
          variant="outline"
          onPress={withdraw}
          style={[styles.buttonTransparent, styles.fullButtonWidth]}
          >
          Withdraw
        </Button>
      </View>

      <Pressable>
        <View style={[styles.buttonTransparent, styles.buyWithStripe]}>
          <ThemedText style={styles.buyAvaxWithStripeText}>
            Buy AVAX with{" "}
          </ThemedText>
          <Image
            style={styles.stripeLogo}
            source={require("@assets/images/stripe.png")}
          />
        </View>
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 15,
    shadowColor: "rgba(0, 0, 0, 0.25)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 5,
  },
  walletCardsWrapper: {
    overflow: "scroll",
    width: "100%",
  },
  separator: {
    width: 12,
  },
  profileMetricsWrapper: {
    flexDirection: "row",
    gap: 10,
    marginTop: 10,
  },
  profileMetricCard: {
    borderWidth: 1,
    borderColor: Colors.dark.darkGrey,
    flex: 1,
    borderRadius: 10,
    padding: 20,
  },
  profileMetricCardLogo: {
    width: 14,
    aspectRatio: 1,
  },
  profileMetricCardLogoWrapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  walletFunctionButtons: {
    flexDirection: "row",
    marginTop: 20,
    justifyContent: "space-between",
    gap: 12,
  },
  buttonTransparent: {
    backgroundColor: "transparent",
    borderWidth: 0.5,
    borderColor: Colors.dark.lightgrey,
  },
  fullButtonWidth: {
    flex: 1,
  },
  buyWithStripeBtn: {
    marginTop: 12,
  },
  buyWithStripe: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 30,
    paddingVertical: 12,
    marginTop: 12,
    borderColor: Colors.dark.brandOrange,
  },
  buyAvaxWithStripeText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.dark.offWhite,
    fontFamily: "InterSemiBold",
  },
  stripeLogo: {
    width: 40,
    height: 14,
  },
  avaxValue: {
    fontSize: 15,
    fontWeight: "600",
    color: Colors.dark.offWhite,
    fontFamily: "InterSemiBold",
  },
});
