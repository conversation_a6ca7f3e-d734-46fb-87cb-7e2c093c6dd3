
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

import { MinTokenData } from "@/environments/tokens";

export const enum SwapSide {
    BUY = "BUY",
    SELL = "SELL"
}

export  type Address = string;
export  type NumberAsString = string;

export  type OptimalRoute = {
    percent: number;
    swaps: OptimalSwap[];
};
export  type OptimalSwap = {
    srcToken: Address;
    srcDecimals: number;
    destToken: Address;
    destDecimals: number;
    swapExchanges: OptimalSwapExchange<any>[];
};
export  type OptimalSwapExchange<T> = {
    exchange: string;
    srcAmount: NumberAsString;
    destAmount: NumberAsString;
    percent: number;
    data?: T;
    poolAddresses?: Array<Address>;
};

export declare type OptionalRate = {
    exchange: string;
    srcAmount: NumberAsString;
    destAmount: NumberAsString;
    unit?: NumberAsString;
    data?: any;
};

export  type OptimalRate = {
    blockNumber: number;
    network: number;
    srcToken: Address;
    srcDecimals: number;
    srcAmount: NumberAsString;
    srcUSD: NumberAsString;
    destToken: Address;
    destDecimals: number;
    destAmount: NumberAsString;
    destUSD: NumberAsString;
    bestRoute: OptimalRoute[];
    gasCostUSD: NumberAsString;
    gasCost: NumberAsString;
    others?: OptionalRate[];
    side: SwapSide;
    contractMethod: string;
    tokenTransferProxy: Address;
    contractAddress: Address;
    maxImpact?: number;
    maxUSDImpact?: number;
    maxImpactReached?: boolean;
    partner?: string;
    partnerFee: number;
    hmac: string;
};

export const useGetRate = ({
    srcToken,
    destToken,
    srcAmount,
  }: {
    srcToken: Pick<MinTokenData, "address" | "decimals">;
    destToken: Pick<MinTokenData, "address" | "decimals">;
    srcAmount: any;
  }) => {
    const VERSION = 5;
    const networkID = 43114;
  
    const queryParams = {
      srcToken: srcToken.address,
      destToken: destToken.address,
      srcDecimals: srcToken.decimals.toString(),
      destDecimals: destToken.decimals.toString(),
      amount: srcAmount,
      side: SwapSide.SELL,
      network: networkID.toString(),
      version: VERSION.toString(),
      maxImpact: "100",
    };
  
    const apiURL = "https://api.paraswap.io";
    const searchString = new URLSearchParams(queryParams);
    const pricesURL = `${apiURL}/prices/?${searchString}`;
  
    return useQuery<OptimalRate, Error>({
      queryKey: [
        "getRate",
        srcToken.address,
        destToken.address,
        srcAmount.toString(),
      ],
      queryFn: async () => {
        const response = await axios.get<{ priceRoute: OptimalRate }>(pricesURL);
        return response.data.priceRoute;
      },
    });
  };