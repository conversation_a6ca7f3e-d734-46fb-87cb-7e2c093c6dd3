import { useState } from "react";
import { Modal, View, Text, ScrollView } from "react-native";
import { toast } from "@/components/toast";
import {
  useReportThreadMutation,
  useReportUserMutation,
} from "@/queries/report-mutations";
import { ThreadUser, User } from "@/types";

import { useUser } from "../../../../stores";
import { DetailsPage } from "./_components/details-page";
import { Footer } from "./_components/footer";
import { ReasonPage } from "./_components/reason-page";
import { SubmitPage } from "./_components/submit-page";
import { Header } from "./_components/header";
import { ThemedText } from "@/components/ThemedText";

interface ReportModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  user: User | ThreadUser;
  threadId?: string;
}

export const ReportModal = ({
  open,
  setOpen,
  user,
  threadId,
}: ReportModalProps) => {
  const [page, setPage] = useState<"reason" | "details" | "submit">("reason");
  const [reason, setReason] = useState<string | null>(null);
  const [description, setDescription] = useState("");
  const { user: currentUser } = useUser();
  const descriptionLength = description.length;
  const maxDescriptionLength = 280;

  const isContentReport = !!threadId;

  const { mutateAsync: reportUser, isPending: isReportUserPending } =
    useReportUserMutation({
      onSuccess: () => {
        toast.green("User reported!");
        setOpen(false);
        setTimeout(reset, 200);
      },
      onError: () => {
        toast.red("Failed to submit report");
      },
    });

  const { mutateAsync: reportThread, isPending: isReportThreadPending } =
    useReportThreadMutation({
      onSuccess: () => {
        toast.green("Post reported!");
        setOpen(false);
        setTimeout(reset, 200);
      },
      onError: () => {
        toast.red("Failed to submit report");
      },
    });

  const handleReport = async (blockUser: boolean) => {
    if (!reason) return;

    if (user.id === currentUser?.id) {
      toast.red("You cannot report yourself");
      return;
    }

    if (threadId) {
      await reportThread({
        threadId,
        reason,
        details: description || "",
        blockUser,
      });
    } else {
      await reportUser({
        userId: user.id,
        reason,
        details: description || "",
        blockUser,
      });
    }
    reset();
  };

  const reset = () => {
    setReason(null);
    setDescription("");
    setPage("reason");
  };

  const handleBackClick = () => {
    if (page === "reason") {
      setOpen(false);
    } else {
      setPage((prev) => {
        if (prev === "details") return "reason";
        if (prev === "submit") return "details";
        return prev;
      });
    }
  };

  return (
    <Modal
      visible={open}
      onRequestClose={() => {
        setOpen(false);
        setTimeout(reset, 200);
      }}
      animationType="slide"
      transparent
    >
      <View className="flex-1 bg-dark-bk">
        <View className="flex-1">
          <ScrollView className="flex-1">
            <View className="px-6 pb-4 pt-safe">
              <Header onBack={handleBackClick} title={page === "submit" ? "Submitted" : isContentReport ? "Report Content" : "Report User"} />
            </View>
            {page === "reason" && (
              <ReasonPage
                setReason={setReason}
                isContentReport={isContentReport}
                selectedReason={reason}
              />
            )}
            {page === "details" && (
              <DetailsPage
                reason={reason}
                description={description}
                setDescription={setDescription}
                maxDescriptionLength={maxDescriptionLength}
              />
            )}
            {page === "submit" && (
              <SubmitPage user={user} handleReport={handleReport} />
            )}
          </ScrollView>
        </View>
        <Footer
          page={page}
          reason={reason}
          description={description}
          descriptionLength={descriptionLength}
          maxDescriptionLength={maxDescriptionLength}
          isReportUserPending={isReportUserPending}
          isReportThreadPending={isReportThreadPending}
          setPage={setPage}
          handleReport={handleReport}
        />
      </View>
    </Modal>
  );
};
