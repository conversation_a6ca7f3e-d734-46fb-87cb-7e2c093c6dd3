import { TouchableOpacity, View, StyleSheet } from "react-native"
import { ThemedText } from "@/components/ThemedText"
import { Colors } from "@/constants/Colors"
import Avatar, { AvatarFallBack } from "@/components/ui/avatar"
import { User } from "@/queries/types/top-users-response"
import { memo } from "react"
import { router } from "expo-router"
import { v4 as uuid } from "uuid";

const AVATAR_SIZE = 40

export const UserItem = memo(({ user }: { user: User & { groupId?: string } }) => {
    const handleClick = () => {
        const newPathname = user.groupId || `${uuid()}?twitterHandle=${user.twitterHandle}`;
        router.push(`/chat/${newPathname}`);
    };

    return (
        <TouchableOpacity
            onPress={handleClick}
            activeOpacity={0.7}
            style={[
                styles.messageItemBox,
            ]}
        >
            <View style={styles.leftSection}>
                {user.twitterPicture ? (
                    <Avatar size={AVATAR_SIZE} src={user.twitterPicture} />
                ) : <AvatarFallBack avatarSize={AVATAR_SIZE} />
                }
            </View>
            <View style={styles.middleSection}>
                {
                    user.twitterName ? (
                        <ThemedText numberOfLines={1} style={styles.groupName}>{user.twitterName}</ThemedText>
                    ) : null
                }
                {
                    user.twitterHandle ? (
                        <ThemedText
                            ellipsizeMode="tail"
                            numberOfLines={1}
                            type="defaultGrey"
                        >
                            @{user.twitterHandle}
                        </ThemedText>
                    ) : null
                }
            </View>
        </TouchableOpacity>
    )
}, (prevProps, nextProps) => {
    return JSON.stringify(prevProps.user) === JSON.stringify(nextProps.user)
})

const styles = StyleSheet.create({
    messageItemBox: {
        flexDirection: "row",
        paddingVertical: 15,
        width: '100%',
    },
    leftSection: {
        width: '15%',
        justifyContent: "center",
    },
    middleSection: {
        gap: 3,
        width: '85%',
        justifyContent: 'center',
    },
    groupName: {
        fontSize: 14,
        color: Colors.dark.offWhite,
        fontWeight: "700",
    },
});
