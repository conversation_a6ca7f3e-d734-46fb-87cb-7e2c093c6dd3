import { View, Pressable, Text } from "react-native";
import { Href, Link, Slot, usePathname } from "expo-router";
import Header from "@/components/ui/header";

export default function PostReactionsLayout() {
  const pathname = usePathname();
  
  const postUrl = pathname.split("/postreactions")[0];
  const currentPath = pathname.split('/').pop();
  const isLikesActive = currentPath === 'likes';
  const isRepostsActive = currentPath === 'reposts';
  const isQuotesActive = currentPath === 'quotes';

  return (
    <View className="flex-1 bg-[#000]">
      <Header
        title="Post Reactions"
        titleStyle={{ fontSize: 16, fontWeight: "600", lineHeight: 20 }}
      />

      <View className="flex-row px-4 border-b border-[#333]">
        <Link
          href={`${postUrl}/postreactions/likes` as Href<string>}
          asChild
          replace
        >
          <Pressable className="flex-1 py-3 items-center relative" >
            <Text className = {`text-[13px] text-[#999] normal-case ${isLikesActive ? 'text-white font-medium' : ''}`}>
              Likes
            </Text>
            {isLikesActive && <View className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#EB540A]" />}
          </Pressable>
        </Link>
        
        <Link
          href={`${postUrl}/postreactions/reposts` as Href<string>}
          asChild
          replace
        >
          <Pressable className="flex-1 py-3 items-center relative"
          >
            <Text className = {`text-[13px] text-[#999] normal-case ${isRepostsActive ? 'text-white font-medium' : ''}`}>
              Reposts
            </Text>
            {isRepostsActive && <View className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#EB540A]" />}
          </Pressable>
        </Link>
        
        <Link
          href={`${postUrl}/postreactions/quotes` as Href<string>}
          asChild
          replace
        >
          <Pressable className="flex-1 py-3 items-center relative" >
            <Text className = {`text-[13px] text-[#999] normal-case ${isQuotesActive ? 'text-white font-medium' : ''}`} >
              Quotes
            </Text>
            {isQuotesActive && <View className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#EB540A]" />}
          </Pressable>
        </Link>
      </View>
      <View className="flex-1">
        <Slot />
      </View>
    </View>
  );
}
