{"expo": {"name": "The Arena", "slug": "arena_mobile_app", "version": "1.0.1", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "appstarsarena", "userInterfaceStyle": "dark", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#191919"}, "ios": {"bundleIdentifier": "com.preview.thearena", "buildNumber": "1", "supportsTablet": true, "icon": "./assets/images/icon.png", "config": {"usesNonExemptEncryption": false}, "infoPlist": {}}, "android": {"intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "appstarsarena"}], "category": ["BROWSABLE", "DEFAULT"]}], "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#191919"}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS"], "package": "com.anonymous.arena_mobile_app"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-av", "expo-font", "expo-secure-store", "./plugins/trust-local-certs.js"], "notification": {"icon": "./assets/images/icon.png", "color": "#191919", "androidMode": "default", "androidCollapsedTitle": "The Arena", "iosDisplayInForeground": true}, "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "20663154-9101-4455-b25a-51197ba8984c"}}, "owner": "the_arena", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/20663154-9101-4455-b25a-51197ba8984c"}}}