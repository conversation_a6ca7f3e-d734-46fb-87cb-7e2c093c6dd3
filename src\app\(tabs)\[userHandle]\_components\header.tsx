import React, { useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from "react-native";
import { Href, useRouter } from "expo-router";
import { ArrowBackOutlineIcon } from "@/components/icons";

const useBoundedScroll = (maxScroll: number) => {
  const scrollY = useRef(new Animated.Value(0)).current;

  const scrollYBoundedProgress = Animated.divide(scrollY, maxScroll);

  return {
    scrollY,
    scrollYBoundedProgress,
  };
};

export const Header = () => {
  const router = useRouter();

  const { scrollY, scrollYBoundedProgress } = useBoundedScroll(300);

  const translateY = scrollYBoundedProgress.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -100],
  });

  useEffect(() => {
    Animated.timing(scrollY, {
      toValue: 300,
      duration: 5000,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <Animated.View style={[styles.container, { transform: [{ translateY }] }]}>
      <View style={styles.leftContainer}>
        <TouchableOpacity
          onPress={() => {
            if (router.canGoBack()) {
              router.back();
            } else {
              router.push("home/(top-tabs)" as Href<string>);
            }
          }}
        >
          <ArrowBackOutlineIcon style={styles.icon} />
        </TouchableOpacity>
      </View>
      <Text style={styles.title}>Post</Text>
      <View style={styles.rightContainer} />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    zIndex: 50,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderBottomWidth: 1,
    borderBottomColor: "#333",
    backgroundColor: "rgba(0, 0, 0, 0.65)",
    paddingHorizontal: 16,
    paddingVertical: 10,
    paddingTop: 20,
    width: "100%",
  },
  leftContainer: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  rightContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
  },
  icon: {
    width: 24,
    height: 24,
    color: "#fff",
  },
});
