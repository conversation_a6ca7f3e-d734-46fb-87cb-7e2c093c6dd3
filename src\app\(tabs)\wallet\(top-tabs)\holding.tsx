import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import Avatar from "@/components/ui/avatar";
import { Colors } from "@/constants/Colors";
import { FlatList, StyleSheet, Text, View } from "react-native";
import { HoldingsNotFound } from "../_components/holdings-not-found";
import { useSharesHoldingsQuery } from "@/queries/shares-queries";

interface ItemCardProp {
  name: String;
  handle: String;
  tickets: string;
}

export const ProfileItemCard = ({ name, handle, tickets }: ItemCardProp) => {
  return (
    <View style={styles.header}>
      <View style={styles.headerProfile}>
        <Avatar
          size={35}
          src={require("@assets/images/sample-profile-pic.png")}
        />
        <View>
          <ThemedText type="defaultSemiBold" style={styles.username}>
            {name}
          </ThemedText>
          <ThemedText style={styles.xHandle}>{handle}</ThemedText>
        </View>
      </View>
      <ThemedText style={styles.ticketQty}>{tickets} Tickets</ThemedText>
    </View>
  );
};

export default function Holding() {
  const { data: holdingsData, isLoading: isHoldingsLoading } =
    useSharesHoldingsQuery();

  if (
    holdingsData &&
    !isHoldingsLoading &&
    holdingsData.holdings.length === 0
  ) {
    return <HoldingsNotFound />;
  }

  return (
    <ThemedView>
      {holdingsData && !isHoldingsLoading && (
        <FlatList
          data={holdingsData.holdings}
          keyExtractor={(item, index) => item.id || index.toString()}
          renderItem={({ item }) => (
            <ProfileItemCard
              key={item.id}
              name={item.subjectUser?.twitterName}
              handle={item.subjectUser?.twitterHandle}
              tickets={item.amount}
            />
          )}
          showsVerticalScrollIndicator={false}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 14,
  },
  headerProfile: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },
  username: {
    fontSize: 14,
    color: Colors.dark.white,
    fontWeight: "500",
  },
  xHandle: {
    fontSize: 12,
    color: Colors.dark.lightgrey,
  },
  ticketQty: {
    fontSize: 12,
  },
});
