import { StyleSheet, TouchableOpacity, View } from "react-native";

import { Colors } from "@/constants/Colors";
import { CloseOutlineIcon, SearchOutlineIcon } from "@/components/icons";
import { CustomTextInput } from "@/components/ui/text-input";

import { useSearch } from "../context/search-context";

export const ExploreSearch = () => {
  const { searchValue, setSearchValue } = useSearch();
  const searchHandler = (text: string) => setSearchValue(text);

  return (
    <View style={styles.container}>
      <CustomTextInput
        IconLeft={
          <SearchOutlineIcon
            color={Colors.dark.greyText}
            height={16}
            width={16}
          />
        }
        placeHolderColor={Colors.dark.greyText}
        placeholder={"Search the Arena"}
        inputStyle={styles.searchInput}
        editable
        onChangeText={searchHandler}
        value={searchValue}
        iconStyle={styles.iconStyle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  searchInput: {
    borderRadius: 100,
    borderColor: Colors.dark.darkGrey,
  },
  iconStyle: {
    left: 18,
  },
  clearIcon: {
    borderRadius: 100,
    borderColor: Colors.dark.darkGrey,
    borderWidth: 1,
    padding: 2
  }
});
