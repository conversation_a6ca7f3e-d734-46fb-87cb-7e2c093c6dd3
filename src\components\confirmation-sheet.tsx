import React, { useMemo } from "react";
import { View, Text } from "react-native";
import { Button } from "./ui/button";
import { BottomSheet } from "./ui/bottom-sheet";

export interface ConfirmationSheetProps {
  open: boolean;
  setOpen: (v: boolean) => void;
  title: string;
  confirmButtonLabel: string;
  children: React.ReactNode;
  onConfirm: () => void;
  destructive?: boolean;
  height?: number;
}

export const ConfirmationSheet: React.FC<ConfirmationSheetProps> = ({
  open,
  setOpen,
  title,
  confirmButtonLabel,
  children,
  onConfirm,
  destructive = false,
  height = 220,
}) => {

  const handleCancel = () => {
    setOpen(false);
  };

  const handleConfirm = () => {
    onConfirm();
    setOpen(false);
  };

  const content = useMemo(
    () => (
      <View className="p-4">
        <View className="mb-4">
          <Text className="text-lg text-off-white font-bold mb-2">{title}</Text>
          {typeof children === "string" ? (
            <Text className="text-sm text-gray-text">{children}</Text>
          ) : (
            <View>{children}</View>
          )}
        </View>

        <View style={{ flexDirection: 'row', gap: 8, marginTop: 8, alignItems: 'center' }}>
          <Button
            style={{ flex: 1 }}
            variant="outline"
            onPress={handleCancel}
            >
            Cancel
          </Button>
          <Button
            style={{ flex: 1 }}
            variant={destructive ? "destructive" : "secondary"}
            onPress={handleConfirm}
            >
            {confirmButtonLabel}
          </Button>
        </View>
      </View>
    ),
    [title, children, confirmButtonLabel, destructive]
  );

  return (
    <BottomSheet open={open} setOpen={setOpen} height={height}>
      { content }
    </BottomSheet>
  );
};
