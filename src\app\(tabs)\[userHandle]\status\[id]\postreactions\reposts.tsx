import { useLocalSearchParams } from "expo-router";
import {
  Text,
  View,
  FlatList,
} from "react-native";

import React from "react";
import { usePostReactionsRepostsInfiniteQuery } from "@/queries/postreactions-queries";
import { UserListItem } from "./_components/user-list-item";
import { UserListItemLoadingSkeleton } from "./_components/user-item-loading-skeleton";

export default function RepostsScreen() {
  const params = useLocalSearchParams() as { userHandle: string; id: string };

  const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage } =
    usePostReactionsRepostsInfiniteQuery({
      threadId: params.id,
    });
    
  const reposts = data?.pages.flatMap((page) => page.repostedUsers) || [];

  if (reposts.length === 0 && !isLoading) {
    return (
      <View className="flex-row h-full w-full items-center justify-center px-6 pt-16 ">
        <Text className="text-base text-off-white">No reposts yet</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={reposts}
      renderItem={({ item }) => (
        <UserListItem user={item} threadId={params.id} />
      )}
      keyExtractor={(item, index) => item.id || index.toString()}
      onEndReached={() => {
        if (hasNextPage) {
          fetchNextPage();
        }
      }}
      onEndReachedThreshold={0.7}
      ListEmptyComponent={
        isLoading ? (
          <>
            {Array.from({ length: 7 }).map((_, i) => (
              <UserListItemLoadingSkeleton key={i} />
            ))}
          </>
        ) : null
      }
      ListFooterComponent={
        isFetchingNextPage ? (
          <>
            {Array.from({ length: 5 }).map((_, i) => (
              <UserListItemLoadingSkeleton key={`footer-${i}`} />
            ))}
          </>
        ) : null
      }
    />
  );
}
