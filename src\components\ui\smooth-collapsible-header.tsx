import React, { createContext, useContext, ReactNode } from "react";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  interpolate,
  withTiming,
  useDerivedValue,
  Easing,
  SharedValue,
  useAnimatedScrollHandler,
} from "react-native-reanimated";
import { StyleSheet, ViewStyle } from "react-native";

// Types
interface SmoothCollapsibleHeaderContextType {
  scrollY: SharedValue<number>;
  isScrollingDown: SharedValue<boolean>;
}

interface SmoothCollapsibleHeaderProps {
  children: ReactNode;
  maxHeaderHeight?: number;
  minHeaderHeight?: number;
  headerContent: ReactNode;
  headerStyle?: ViewStyle;
  animationDuration?: number;
  easing?: typeof Easing.out;
}

interface CollapsibleScrollViewProps {
  children: ReactNode;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  [key: string]: any;
}

// Context
const SmoothCollapsibleHeaderContext = createContext<SmoothCollapsibleHeaderContextType | null>(null);

export const useSmoothCollapsibleHeader = () => {
  const ctx = useContext(SmoothCollapsibleHeaderContext);
  if (!ctx) {
    throw new Error("useSmoothCollapsibleHeader must be used within SmoothCollapsibleHeader");
  }
  return ctx;
};

// Main component
export const SmoothCollapsibleHeader: React.FC<SmoothCollapsibleHeaderProps> = ({
  children,
  maxHeaderHeight = 200,
  minHeaderHeight = 80,
  headerContent,
  headerStyle,
  animationDuration = 200,
  easing = Easing.out(Easing.cubic),
}) => {
  const scrollThreshold = maxHeaderHeight - minHeaderHeight;

  // Shared values for smooth animations
  const scrollY = useSharedValue(0);
  const isScrollingDown = useSharedValue(false);
  const lastScrollY = useSharedValue(0);
  const headerProgress = useSharedValue(0);

  // Derived value to track scroll direction and update smooth progress
  useDerivedValue(() => {
    const currentScroll = scrollY.value;
    const diff = currentScroll - lastScrollY.value;

    // Update scroll direction
    if (Math.abs(diff) > 1) {
      isScrollingDown.value = diff > 0;
      lastScrollY.value = currentScroll;
    }

    // Create smooth progress with momentum
    const rawProgress = Math.min(Math.max(currentScroll / scrollThreshold, 0), 1);

    // Apply smooth timing to progress changes
    headerProgress.value = withTiming(rawProgress, {
      duration: animationDuration,
      easing,
    });

    return rawProgress;
  });

  // Animated styles for header
  const headerAnimatedStyle = useAnimatedStyle(() => {
    const height = interpolate(
      headerProgress.value,
      [0, 1],
      [maxHeaderHeight, minHeaderHeight],
      'clamp'
    );

    return {
      height,
      overflow: 'hidden',
    };
  });

  return (
    <SmoothCollapsibleHeaderContext.Provider value={{ scrollY, isScrollingDown }}>
      <Animated.View style={[headerAnimatedStyle, headerStyle]}>
        {headerContent}
      </Animated.View>
      {children}
    </SmoothCollapsibleHeaderContext.Provider>
  );
};

// Collapsible ScrollView component
export const CollapsibleScrollView: React.FC<CollapsibleScrollViewProps> = ({
  children,
  style,
  contentContainerStyle,
  ...props
}) => {
  const { scrollY } = useSmoothCollapsibleHeader();

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  return (
    <Animated.ScrollView
      {...props}
      style={style}
      contentContainerStyle={contentContainerStyle}
      onScroll={scrollHandler}
      scrollEventThrottle={1}
    >
      {children}
    </Animated.ScrollView>
  );
};

// Collapsible FlatList component
export const CollapsibleFlatList: React.FC<any> = (props) => {
  const { scrollY } = useSmoothCollapsibleHeader();

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  return (
    <Animated.FlatList
      {...props}
      onScroll={scrollHandler}
      scrollEventThrottle={1}
    />
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default SmoothCollapsibleHeader;
