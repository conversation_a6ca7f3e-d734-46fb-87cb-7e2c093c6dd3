/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = "#0a7ea4";
const tintColorDark = "#fff";

export const Colors = {
  light: {
    text: "#F4F4F4",
    background: "#020202",
    tint: tintColorLight,
    icon: "#687076",
    tabIconDefault: "#687076",
    tabIconSelected: tintColorLight,
  },
  dark: {
    text: "#F4F4F4",
    background: "#020202",
    secondaryBackground: "#121212",
    secondaryBackgroundDark: "#1A1A1A",
    tint: tintColorDark,
    icon: "#9BA1A6",
    tabIconDefault: "#9BA1A6",
    tabIconSelected: tintColorDark,
    tagLineSecondary: "#F4F4F4",
    primary: "#EB540A",
    grey: "#808080",
    offWhite: "#F4F4F4",
    lightgrey: "#AAAAAA",
    white: "#FFFFFF",
    brandOrange: "#EB540A",
    brandOrangeDark: "#CB4A0B",
    brandOrangeLight: "#F5753D",
    darkGrey: "#3B3B3B",
    lightGreyText: "#B5B5B5",
    grayBg: "#0E0E0E",
    disableInputColor: "rgba(49, 49, 49, 1)",
    borderColor: "rgba(100, 100, 100, 1)",
    greyText: "rgba(128, 128, 128, 1)",
    greyBackground: "rgba(14, 14, 14, 1)",
    whiteShade: "#F2F2F2",
    green: "#40b877",
    },
  };
