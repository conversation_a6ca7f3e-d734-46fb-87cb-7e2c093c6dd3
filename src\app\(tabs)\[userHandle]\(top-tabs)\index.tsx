import { useThreadsByUserIdInfiniteQuery } from "@/queries";
import {
  useIsUserBlockedQuery,
  useUserByHandleQuery,
} from "@/queries/user-queries";
import { Thread } from "@/types";
import { useLocalSearchParams } from "expo-router";
import { useMemo, useState } from "react";
import {
  Text,
  View,
  RefreshControl,
} from "react-native";
import { ProfileTimelinePost } from "../_components/profile-timeline-post";
import { Colors } from "@/constants/Colors";
import React from "react";
import { PostLoadingSkeleton } from "@/components/post-loading-skeletons";
import { useShowReposts } from "../showRepostsContext";
import { useCollapsibleFlatList } from "./useCollapsibleFlatList";

export default function ProfileThreads() {
  const { showReposts } = useShowReposts();
  const params = useLocalSearchParams() as { userHandle: string };
  const { data: userData } = useUserByHandleQuery(params.userHandle);
  const { data: isBlocked } = useIsUserBlockedQuery(userData?.user?.id || "");

  const {
    data,
    fetchNextPage,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    refetch,
  } = useThreadsByUserIdInfiniteQuery(userData?.user?.id || "");

  const [refreshing, setRefreshing] = useState(false);

  const threads = useMemo(() => {
    if (!data) return [];

    const threads = data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);

    if (showReposts) {
      return threads;
    }

    return threads.filter((x) => !x.repost);
  }, [data, showReposts]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
    } finally {
      setRefreshing(false);
    }
  };

  if (isBlocked) {
    return (
      <View className="flex h-full w-full items-center justify-center px-6 pt-16 text-base text-off-white">
        <Text>@{userData?.user?.twitterHandle} is blocked</Text>
      </View>
    );
  }

  // Use the custom hook for a clean, scroll-synced FlatList
  const CollapsibleFlatList = useCollapsibleFlatList();

  return (
    <CollapsibleFlatList
      data={threads}
      renderItem={({ item }) => (
        <ProfileTimelinePost thread={item} userId={userData?.user?.id || ""} />
      )}
      keyExtractor={(item, index) => item.id || index.toString()}
      onEndReached={() => {
        if (hasNextPage) {
          fetchNextPage();
        }
      }}
      onEndReachedThreshold={0.7}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[Colors.dark.brandOrange]}
        />
      }
      ListEmptyComponent={
        isLoading ? (
          <>
            {Array.from({ length: 7 }).map((_, i) => (
              <PostLoadingSkeleton key={i} />
            ))}
          </>
        ) : null
      }
      ListFooterComponent={
        isFetchingNextPage ? (
          <>
            {Array.from({ length: 5 }).map((_, i) => (
              <PostLoadingSkeleton key={`footer-${i}`} />
            ))}
          </>
        ) : null
      }
    />
  );
}

