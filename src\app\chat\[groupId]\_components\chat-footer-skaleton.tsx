import { Skeleton } from "@/components/ui/skaleton";
import { View } from "react-native";

export const ChatFooterSkeleton = () => {
    return (
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', height: 65, paddingHorizontal: 10 }}>
            <Skeleton
                width="10%"
                height={40}
                style={{ borderRadius: 100 }}
            />
            <Skeleton
                width="60%"
                height={40}
                style={{ borderRadius: 30 }}
            />
            <Skeleton
                width="10%"
                height={40}
                style={{ borderRadius: 100 }}
            />
            <Skeleton
                width="10%"
                height={40}
                style={{ borderRadius: 10 }}
            />
        </View>
    )
}