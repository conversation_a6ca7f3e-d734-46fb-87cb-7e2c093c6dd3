import { useColorScheme } from '@/hooks/useColorScheme';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import React, { useEffect, useState } from 'react';
import 'react-native-reanimated';
import { dynamicClient } from '../dynamicClient';

import BackButton from '@/components/navigation/BackButton';
import { Colors } from '@/constants/Colors';
import { getCookie } from '@/cookies/secure-store';
import { axios } from '@/lib/axios';
import { usePostStore } from '@/stores';
import { withErrorBoundary } from './hoc/withErrorBoundry';
import { Providers } from './providers';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

type StackScreenOptions = React.ComponentProps<typeof Stack.Screen>['options'];

const headerOptions: StackScreenOptions = {
  headerTitleStyle: {
    fontSize: 16,
    fontWeight: '700',
    fontFamily: 'InterSemibold',
  },
  headerStyle: {
    backgroundColor: Colors.dark.background,
  },
  headerShadowVisible: true,
  headerTitleAlign: 'center',
  headerLeft: () => <BackButton />,
};

function RootLayout() {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState<string | null>(null);
  const [twitterUser, setTwitterUser] = useState(null);
  const type = usePostStore(state => state.type);

  const colorScheme = useColorScheme();

  const [loaded] = useFonts({
    InterRegular: require('../../assets/fonts/Inter-Regular.ttf'),
    InterMedium: require('../../assets/fonts/Inter-Medium.ttf'),
    InterSemiBold: require('../../assets/fonts/Inter-SemiBold.ttf'),
  });

  useEffect(() => {
    async function loadCookies() {
      const tokenCookie = await getCookie('token');

      setToken(tokenCookie ? tokenCookie.value : null);

      const userCookie = await getCookie('user');
      setUser(userCookie ? JSON.parse(userCookie.value || '{}') : null);

      const twitterUserCookie = await getCookie('twitterUser');
      setTwitterUser(
        twitterUserCookie ? JSON.parse(twitterUserCookie.value || '{}') : null,
      );
    }

    loadCookies();
  }, []);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  if (token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }
  return (
    <>
      <dynamicClient.reactNative.WebView />
      <Providers
        token={token}
        user={user}
        twitterUser={twitterUser}
        setUser={setUser}
        setTwitterUser={setTwitterUser}
        setToken={setToken}
      >
        <Stack>
          <Stack.Screen name="index" options={{ headerShown: false }} />
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
          <Stack.Screen name="login/index" options={{ headerShown: false }} />
          <Stack.Screen
            name="(modals)/addPost/index"
            options={{
              headerTitle:
                type === 'reply'
                  ? 'Reply'
                  : type === 'quote'
                  ? 'New Quote'
                  : 'New Post',
              headerTitleStyle: {
                fontSize: 16,
                fontWeight: '700',
                fontFamily: 'InterSemibold',
              },
              headerTitleAlign: 'center',
              headerLeft: () => <BackButton />,
            }}
          />
          <Stack.Screen
            name="(modals)/tip-modal/index"
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name="chat/message-settings/index"
            options={{
              headerTitle: 'Messages Settings',
              ...headerOptions,
            }}
          />
          <Stack.Screen
            name="chat/message-requests/index"
            options={{
              headerTitle: 'Message Requests',
              ...headerOptions,
            }}
          />
          <Stack.Screen
            name="chat/new-dm/index"
            options={{
              headerTitle: 'New Direct Message',
              ...headerOptions,
            }}
          />
          <Stack.Screen
            name="chat/[groupId]/index"
            options={{ headerShown: false }}
          />
        </Stack>
      </Providers>
    </>
  );
}

export default withErrorBoundary(RootLayout);
