import React, { useMemo } from "react";
import { FlatList, View, StyleSheet } from "react-native";

import { useThreadAnswersInfiniteQuery } from "@/queries";
import { Thread } from "@/types";
import { CommentPost } from "./comment-post";
import { useLocalSearchParams } from "expo-router";
import { PostLoadingSkeleton } from "@/components/post-loading-skeletons";

export const Comments = () => {
  const params = useLocalSearchParams() as { id: string };

  const {
    data: answersData,
    fetchNextPage: fetchAnswersNextPage,
    isFetchingNextPage: isFetchingAnswersNextPage,
    isLoading: isAnswersLoading,
    hasNextPage,
  } = useThreadAnswersInfiniteQuery(params.id);

  const threads = useMemo(() => {
    if (!answersData) return [];

    return answersData.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [answersData]);

  return (
    <View className="pb-4">
      <FlatList
        data={threads}
        keyExtractor={(item, index) => item.id.toString() || index.toString()}
        renderItem={({ item }) => <CommentPost thread={item} />}
        scrollEnabled={false}
        onEndReached={() => {
          if (hasNextPage) {
            fetchAnswersNextPage();
          }
        }}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={
          isAnswersLoading ? (
            <>
              {Array.from({ length: 5 }).map((_, i) => (
                <PostLoadingSkeleton key={i} />
              ))}
            </>
          ) : null
        }
        ListFooterComponent={
          isFetchingAnswersNextPage ? (
            <>
              {Array.from({ length: 5 }).map((_, i) => (
                <PostLoadingSkeleton key={`footer-${i}`} />
              ))}
            </>
          ) : null
        }
        initialNumToRender={10}
      />
    </View>
  );
};
