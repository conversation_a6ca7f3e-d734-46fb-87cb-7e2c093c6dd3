import React from 'react';
import {
  useWindowDimensions,
  GestureResponderEvent,
  Linking,
} from 'react-native';
import RenderHtml, {
  MixedStyleDeclaration,
  TRenderEngineConfig,
  RenderersProps,
} from 'react-native-render-html';
import { router, Href } from 'expo-router';

const TAGS_STYLES = {
  body: { color: '#F4F4F4' },
  p: { margin: 0, padding: 0 },
  a: { color: '#EB540A', textDecorationLine: 'none' as 'none' },
} satisfies Readonly<Record<string, MixedStyleDeclaration>>;

const ANCHOR_PROPS: RenderersProps = {
  a: {
    onPress: (
      _event: GestureResponderEvent,
      href: string,
      attrs: Record<string, string>,
      _target: '_blank' | '_self' | '_parent' | '_top',
    ) => {
      if (attrs.class?.includes('thread-tag') && attrs['data-user-handle']) {
        router.push(`/${attrs['data-user-handle']}` as Href<string>);
        return;
      }
      if (attrs['data-internal'] === 'true' && attrs['data-path']) {
        router.push(attrs['data-path'] as Href<string>);
        return;
      }
      if (href?.startsWith('/') && !href.startsWith('//')) {
        router.push(href as Href<string>);
        return;
      }

      if (href) Linking.openURL(href);
    },
  },
  img: {},
  ol: {},
  ul: {},
};

type PostContentHtmlProps = {
  html: string;
  width?: number;
  tagsStyles?: Partial<typeof TAGS_STYLES>;
  renderersProps?: Partial<typeof ANCHOR_PROPS>;
  otherProps?: Partial<Omit<TRenderEngineConfig, 'source'>>;
};

const CustomRenderHtml = ({
  html,
  width,
  tagsStyles,
  renderersProps,
  otherProps,
}: PostContentHtmlProps) => {
  const { width: winWidth } = useWindowDimensions();

  return (
    <RenderHtml
      contentWidth={width ?? winWidth}
      source={{ html }}
      tagsStyles={{ ...TAGS_STYLES, ...tagsStyles }}
      renderersProps={{ ...ANCHOR_PROPS, ...renderersProps }}
      {...otherProps}
    />
  );
};

export default CustomRenderHtml;
