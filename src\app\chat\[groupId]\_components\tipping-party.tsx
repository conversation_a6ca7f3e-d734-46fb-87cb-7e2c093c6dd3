import { View, StyleSheet, TouchableOpacity, ScrollView, Modal, Image, ImageSourcePropType } from "react-native"
import { Colors } from "@/constants/Colors"
import { ThemedText } from "@/components/ThemedText"
import BackButton from "@/components/navigation/BackButton"
import { SettingsIcon } from "@/components/icons/settings"
import { LinearGradient } from "expo-linear-gradient"
import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react"
import { EditTipReceivers } from "./edit-tip-receivers"
import { useSharesHoldersQuery } from "@/queries/shares-queries"
import { SharesHolder } from "@/queries/types"
import { Controller, useForm } from "react-hook-form";
import Dropdown from "@/components/ui/select"
import { AVAX, avaxToken, swapTokens, tokensWithoutSolana } from "@/environments/tokens"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { useBalancesQuery, useGetTokenPriceQuery } from "@/queries/balance-queries"
import { useUser } from "@/stores/user"
import { formatEther, formatUnits, removeCommas } from "@/utils"
import { CustomTextInput } from "@/components/ui/text-input"
import { useGetRate } from "@/queries/rate-queries"
import { ethers } from "ethers"
import { InformationCircleOutlineIcon } from "@/components/icons"
import { CenterModal } from "@/components/ui/center-modal"
import { TippingPartyOutlineIcon } from "@/components/icons/tipping-party-outline"
import { Button } from "@/components/ui/button"

const numberFormatter = new Intl.NumberFormat("en-US", {
    maximumFractionDigits: 20,
});

type TTippingParty = {
    closeTippingParty: () => void
    ownerUserId: string
    loggedInUserId: string
}

export interface TipReceiver extends SharesHolder {
    isChecked: boolean;
}


const getFloatValue = (value: string) => {
    return parseFloat(removeCommas(value));
}


export const TippingParty = memo(({ closeTippingParty, ownerUserId, loggedInUserId }: TTippingParty) => {
    //useStates
    const [activeTab, setActiveTab] = useState<0 | 1>(0)
    const [isEditTipReceiversOpen, setIsEditTipReceiversOpen] = useState(false)
    const [isTipInfoOpen, setIsTipInfoOpen] = useState(false)
    const [tipReceivers, setTipReceivers] = useState<TipReceiver[]>([])
    const [isPendingDynamic, setIsPendingDynamic] = useState(false);
    const [tipAmount, setTipAmount] = useState("0"); //represents the amount per tip in terms of token
    const [token, setToken] = useState<{
        name: string;
        icon: ImageSourcePropType;
        native?: boolean;
    }>(AVAX);

    //refs
    const feePercentage = useRef("2");


    const tipUniqueHolder = activeTab === 0;
    const tipEveryTicket = activeTab === 1;

    //custom hooks
    const { user } = useUser();

    const { data: holdersData, isLoading: isHoldersLoading } = useSharesHoldersQuery({
        userId: ownerUserId,
        enabled: !!ownerUserId,
    });

    const { data: arenaOptimalRate } = useGetRate({
        srcToken: swapTokens["ARENA"],
        destToken: avaxToken,
        srcAmount: ethers.parseUnits("1", swapTokens["ARENA"].decimals).toString(),
    });

    const { data: champOptimalRate } = useGetRate({
        srcToken: swapTokens["CHAMP"],
        destToken: avaxToken,
        srcAmount: ethers.parseUnits("1", swapTokens["CHAMP"].decimals).toString(),
    });

    const { data: winkOptimalRate } = useGetRate({
        srcToken: swapTokens["WINK"],
        destToken: avaxToken,
        srcAmount: ethers.parseUnits("1", swapTokens["WINK"].decimals).toString(),
    });

    const { data: tokenPrice } = useGetTokenPriceQuery();

    const { data: balances } = useBalancesQuery({
        address: user?.address,
    });

    // const { primaryWallet } = useDynamicContext();

    useEffect(() => {
        if (!holdersData || !loggedInUserId) return;
        // holdersData.holders = [
        //     {
        //         "id": "b901c6a3-61b3-4007-88f2-5c96b44bdfe4",
        //         "createdOn": "2025-02-05T16:43:47.010Z",
        //         "amount": 1,
        //         "subjectId": "4a80198f-d6dd-4dde-b48b-0d3874d03db5",
        //         "traderId": "8a0b5271-9e33-4c47-846d-27db7a934e49",
        //         "subjectUser": {
        //             "id": "4a80198f-d6dd-4dde-b48b-0d3874d03db5",
        //             "createdOn": "2025-01-16T04:24:29.310Z",
        //             "twitterId": "1604512146653843456",
        //             "twitterHandle": "varun_kukade",
        //             "twitterName": "Varun Kukade",
        //             "twitterPicture": "https://static.starsarena.com/uploads/6abfaad5-4a2e-91db-3b5c-ab42020c4ea41737001469090.png",
        //             "lastLoginTwitterPicture": "https://pbs.twimg.com/profile_images/1759080487052828672/_lshumsy.jpg",
        //             "bannerUrl": null,
        //             "address": "******************************************",
        //             "addressBeforeDynamicMigration": null,
        //             "dynamicAddress": "******************************************",
        //             "ethereumAddress": null,
        //             "solanaAddress": null,
        //             "prevAddress": null,
        //             "addressConfirmed": false,
        //             "twitterDescription": "📱React Native Engineer\n💡Transitioning into a Full-Fledged Mobile Engineer (Hybrid & Native)\n🌟Lifelong Learner \n💡 Always curious, always evolving",
        //             "signedUp": false,
        //             "subscriptionCurrency": "'AVAX'::character varying",
        //             "subscriptionCurrencyAddress": null,
        //             "subscriptionPrice": "0",
        //             "keyPrice": "6600000000000000",
        //             "lastKeyPrice": null,
        //             "threadCount": 0,
        //             "followerCount": 5,
        //             "followingsCount": 5,
        //             "twitterFollowers": 17,
        //             "subscriptionsEnabled": false,
        //             "userConfirmed": true,
        //             "twitterConfirmed": false,
        //             "flag": 0,
        //             "ixHandle": "varun_kukade",
        //             "handle": "varun_kukade"
        //         },
        //         "traderUser": {
        //             "id": "8a0b5271-9e33-4c47-846d-27db7a934e49",
        //             "createdOn": "2024-01-15T18:06:20.921Z",
        //             "twitterId": "1432448950213218310",
        //             "twitterHandle": "MaxMantegna",
        //             "twitterName": "Max Mantegna ⚔️",
        //             "twitterPicture": "https://static.starsarena.com/uploads/4351df92-62a4-21e6-653d-e40283180d121723124100761.png",
        //             "lastLoginTwitterPicture": "https://pbs.twimg.com/profile_images/1821540512353914880/gbGEz9R_.jpg",
        //             "bannerUrl": "https://static.starsarena.com/uploads/53de4882-3541-edc5-03a7-6560484e54651730468246029.jpeg",
        //             "address": "******************************************",
        //             "addressBeforeDynamicMigration": "******************************************",
        //             "dynamicAddress": "******************************************",
        //             "ethereumAddress": "******************************************",
        //             "solanaAddress": "Aj6EBSK4EotXwakFXy3ZZK2qbRqSwq5ojuoaKgT4Xyf2",
        //             "prevAddress": null,
        //             "addressConfirmed": false,
        //             "twitterDescription": "I make buttons and dark mode for The Arena. <br/>Art Supportooooooor / Scammer Findooooooorr!<br/>🇦🇷",
        //             "signedUp": false,
        //             "subscriptionCurrency": "'AVAX'::character varying",
        //             "subscriptionCurrencyAddress": null,
        //             "subscriptionPrice": "0",
        //             "keyPrice": "6600000000000000",
        //             "lastKeyPrice": "5382400000000000000",
        //             "threadCount": 9613,
        //             "followerCount": 5265,
        //             "followingsCount": 557,
        //             "twitterFollowers": 543,
        //             "subscriptionsEnabled": false,
        //             "userConfirmed": true,
        //             "twitterConfirmed": false,
        //             "flag": 0,
        //             "ixHandle": "maxmantegna",
        //             "handle": "MaxMantegna"
        //         }
        //     },
        //     {
        //         "id": "b901c6a3-61b3-4007-88f2-5c96b44bdf78",
        //         "createdOn": "2025-02-05T16:43:47.010Z",
        //         "amount": 6,
        //         "subjectId": "4a80198f-d6dd-4dde-b48b-0d3874d03db5",
        //         "traderId": "8a0b5271-9e33-4c47-846d-27db7a934e49",
        //         "subjectUser": {
        //             "id": "4a80198f-d6dd-4dde-b48b-0d3874d03db5",
        //             "createdOn": "2025-01-16T04:24:29.310Z",
        //             "twitterId": "1604512146653843456",
        //             "twitterHandle": "varun_kukade",
        //             "twitterName": "Varun Kukade",
        //             "twitterPicture": "https://static.starsarena.com/uploads/6abfaad5-4a2e-91db-3b5c-ab42020c4ea41737001469090.png",
        //             "lastLoginTwitterPicture": "https://pbs.twimg.com/profile_images/1759080487052828672/_lshumsy.jpg",
        //             "bannerUrl": null,
        //             "address": "******************************************",
        //             "addressBeforeDynamicMigration": null,
        //             "dynamicAddress": "******************************************",
        //             "ethereumAddress": null,
        //             "solanaAddress": null,
        //             "prevAddress": null,
        //             "addressConfirmed": false,
        //             "twitterDescription": "📱React Native Engineer\n💡Transitioning into a Full-Fledged Mobile Engineer (Hybrid & Native)\n🌟Lifelong Learner \n💡 Always curious, always evolving",
        //             "signedUp": false,
        //             "subscriptionCurrency": "'AVAX'::character varying",
        //             "subscriptionCurrencyAddress": null,
        //             "subscriptionPrice": "0",
        //             "keyPrice": "6600000000000000",
        //             "lastKeyPrice": null,
        //             "threadCount": 0,
        //             "followerCount": 5,
        //             "followingsCount": 5,
        //             "twitterFollowers": 17,
        //             "subscriptionsEnabled": false,
        //             "userConfirmed": true,
        //             "twitterConfirmed": false,
        //             "flag": 0,
        //             "ixHandle": "varun_kukade",
        //             "handle": "varun_kukade"
        //         },
        //         "traderUser": {
        //             "id": "8a0b5271-9e33-4c47-846d-27db7a934e09",
        //             "createdOn": "2024-01-15T18:06:20.921Z",
        //             "twitterId": "1432448950213218310",
        //             "twitterHandle": "varunkukade",
        //             "twitterName": "Varun Kukade",
        //             "twitterPicture": "https://static.starsarena.com/uploads/4351df92-62a4-21e6-653d-e40283180d121723124100761.png",
        //             "lastLoginTwitterPicture": "https://pbs.twimg.com/profile_images/1821540512353914880/gbGEz9R_.jpg",
        //             "bannerUrl": "https://static.starsarena.com/uploads/53de4882-3541-edc5-03a7-6560484e54651730468246029.jpeg",
        //             "address": "******************************************",
        //             "addressBeforeDynamicMigration": "******************************************",
        //             "dynamicAddress": "******************************************",
        //             "ethereumAddress": "******************************************",
        //             "solanaAddress": "Aj6EBSK4EotXwakFXy3ZZK2qbRqSwq5ojuoaKgT4Xyf2",
        //             "prevAddress": null,
        //             "addressConfirmed": false,
        //             "twitterDescription": "I make buttons and dark mode for The Arena. <br/>Art Supportooooooor / Scammer Findooooooorr!<br/>🇦🇷",
        //             "signedUp": false,
        //             "subscriptionCurrency": "'AVAX'::character varying",
        //             "subscriptionCurrencyAddress": null,
        //             "subscriptionPrice": "0",
        //             "keyPrice": "6600000000000000",
        //             "lastKeyPrice": "5382400000000000000",
        //             "threadCount": 9613,
        //             "followerCount": 5265,
        //             "followingsCount": 557,
        //             "twitterFollowers": 543,
        //             "subscriptionsEnabled": false,
        //             "userConfirmed": true,
        //             "twitterConfirmed": false,
        //             "flag": 0,
        //             "ixHandle": "maxmantegna",
        //             "handle": "MaxMantegna"
        //         }
        //     }
        // ]
        setTipReceivers(
            holdersData?.holders
                .filter(
                    (holder) =>
                        holder.traderId !== loggedInUserId && parseFloat(holder.amount) >= 1,
                )
                .map((holder) => {
                    return {
                        ...holder,
                        isChecked: true,
                    };
                }),
        );
    }, [holdersData, loggedInUserId]);

    const balance = useMemo(() => {
        return {
            AVAX: balances ? formatEther(balances.AVAX.toString()) ?? "0.00" : "0.00",
            ARENA: balances
                ? formatEther(balances.ARENA.toString()) ?? "0.00"
                : "0.00",
            COQ: balances ? formatEther(balances.COQ.toString()) ?? "0.00" : "0.00",
            GURS: balances ? formatEther(balances.GURS.toString()) ?? "0.00" : "0.00",
            NOCHILL: balances
                ? formatEther(balances.NOCHILL.toString()) ?? "0.00"
                : "0.00",
            MEAT: balances
                ? formatUnits(balances.MEAT.toString(), 6) ?? "0.00"
                : "0.00",
            KIMBO: balances
                ? formatEther(balances.KIMBO.toString()) ?? "0.00"
                : "0.00",
            JOE: balances ? formatEther(balances.JOE.toString()) ?? "0.00" : "0.00",
            TECH: balances ? formatEther(balances.TECH.toString()) ?? "0.00" : "0.00",
            CHAMP: balances
                ? formatEther(balances.CHAMP.toString()) ?? "0.00"
                : "0.00",
            KET: balances ? formatEther(balances.KET.toString()) ?? "0.00" : "0.00",
            WINK: balances ? formatEther(balances.WINK.toString()) ?? "0.00" : "0.00",
        };
    }, [balances]);

    const totalNumberOfTips = useMemo(() => {
        const checkedReceivers = tipReceivers.filter(
            (tipReceiver) => tipReceiver.isChecked,
        );
        if (tipEveryTicket) {
            return checkedReceivers
                .filter((tipReceiver) => tipReceiver.isChecked)
                .reduce(
                    (total, tipReciever) =>
                        total + Math.floor(Number(tipReciever.amount)),
                    0,
                );
        }

        return checkedReceivers.length;
    }, [tipReceivers, tipEveryTicket]);

    //represents the amount per tip in terms of USD
    const singleTipUSD = useMemo(() => {
        const currency = token.name;
        const value = removeCommas(tipAmount);
        if (currency.toLowerCase() === "arena") {
            const rate = Number((arenaOptimalRate && arenaOptimalRate.srcUSD) || 0);
            return numberFormatter.format(Number((Number(value) * rate).toFixed(2)));
        } else if (currency.toLowerCase() === "champ") {
            const rate = Number((champOptimalRate && champOptimalRate.srcUSD) || 0);
            return numberFormatter.format(Number((Number(value) * rate).toFixed(2)));
        } else if (currency.toLowerCase() === "wink") {
            const rate = Number((winkOptimalRate && winkOptimalRate.srcUSD) || 0);
            return numberFormatter.format(Number((Number(value) * rate).toFixed(2)));
        } else {
            if (!tokenPrice) {
                return "0";
            }
            const systemRateAVAX = Number(tokenPrice["avalanche-2"].usd);
            const systemRateCOQ = Number(tokenPrice["coq-inu"].usd);
            const systemRateGURS = Number(tokenPrice.gursonavax.usd);
            const systemRateNOCHILL = Number(tokenPrice["avax-has-no-chill"].usd);
            const systemRateMEAT = Number(tokenPrice["sausagers-meat"].usd);
            const systemRateKIMBO = Number(tokenPrice.kimbo.usd);
            const systemRateJOE = Number(tokenPrice.joe.usd);
            const systemRateTECH = Number(tokenPrice.tech.usd);
            const systemRateKET = Number(tokenPrice.ket.usd);

            const currencyRates: { [key: string]: number } = {
                COQ: systemRateCOQ,
                GURS: systemRateGURS,
                NOCHILL: systemRateNOCHILL,
                AVAX: systemRateAVAX,
                MEAT: systemRateMEAT,
                KIMBO: systemRateKIMBO,
                JOE: systemRateJOE,
                TECH: systemRateTECH,
                KET: systemRateKET,
            };
            const rate = currencyRates[currency];
            return numberFormatter.format(Number((Number(value) * rate).toFixed(2)));
        }
    }, [token, tipAmount]);

    const isTippingDisabled = useMemo(() => {
        const requiredBalance = Number(removeCommas(tipAmount)) * totalNumberOfTips;
        if (
            requiredBalance > getFloatValue(balance[token.name as keyof typeof balance]) ||
            requiredBalance === 0
        ) {
            return true;
        } else {
            return false;
        }
    }, [tipAmount, token.name, totalNumberOfTips, balance]);

    const isDisabled = useMemo(() => {
        return isTippingDisabled || isPendingDynamic
    }, [isTippingDisabled, isPendingDynamic])

    const totalAmountInUSD = useMemo(() => {
        const singleTipUSDValue = Number(removeCommas(singleTipUSD))
        const isZero = totalNumberOfTips === 0 || singleTipUSDValue === 0
        const isLessThanPointZeroOne = totalNumberOfTips * singleTipUSDValue < 0.01;

        return isZero ? "$0"
            : isLessThanPointZeroOne
                ? "< $0.01"
                : `$${(totalNumberOfTips * singleTipUSDValue * (1 + Number(feePercentage.current) / 100))
                    .toFixed(2)
                    .replace(/\.?0+$/, "")}`
    }, [totalNumberOfTips, singleTipUSD]);

    const sendTipsSchema = z.object({
        currency: z.string(),
        amountPerTip: z
            .string()
            .min(1, {
                message: "Tip amount is required",
            })
            .refine((v) => !isNaN(getFloatValue(v)), {
                message: "Tip amount must be a number",
            })
            .refine(
                (v) =>
                    totalNumberOfTips * getFloatValue(v) <= getFloatValue(balance[token.name as keyof typeof balance]),
                {
                    message: "Insufficient balance",
                },
            ),
        totalAmount: z.string(),
    });

    type sendTipsSchemaType = z.infer<typeof sendTipsSchema>;

    const form = useForm<sendTipsSchemaType>({
        defaultValues: {
            currency: AVAX.name,
            amountPerTip: "",
            totalAmount: "",
        },
        resolver: zodResolver(sendTipsSchema),
        reValidateMode: "onChange",
    });

    const onSendTipsClick = () => {
        console.log("onSendTipsClick")
    }

    const changeTab = () => {
        form.reset()
        setToken(AVAX);
        setTipAmount("0");
        setActiveTab(prev => prev === 0 ? 1 : 0)
    }

    const handleSettingsClick = () => {
        setIsEditTipReceiversOpen(true)
    }

    const closeEditTipReceivers = useCallback(() => {
        setIsEditTipReceiversOpen(false)
    }, [])


    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <BackButton onPress={closeTippingParty} />
                <TouchableOpacity disabled={isHoldersLoading || !holdersData} onPress={handleSettingsClick}>
                    <SettingsIcon height={23} width={23} />
                </TouchableOpacity>
            </View>
            <ScrollView showsVerticalScrollIndicator={false}>
                <ThemedText style={styles.subTitle}>Tip</ThemedText>
                <ThemedText style={styles.title}>Tipping Party</ThemedText>
                <View style={styles.header}>
                    <TouchableOpacity style={[styles.eachTab, activeTab === 1 && styles.inactiveTab]} activeOpacity={0.8} onPress={changeTab}>
                        {
                            activeTab === 0 ? (
                                <LinearGradient
                                    colors={['#F36A27', '#CE4909']}  // Color gradient
                                    start={{ x: 0.109, y: 0 }}  // Start point for the gradient (109 degrees)
                                    end={{ x: 0.7968, y: 1 }}   // End point for the gradient (79.68%)
                                    style={styles.gradient}
                                >
                                    <ThemedText style={styles.eachTabTitle}>Tip Every Unique Holder</ThemedText>
                                </LinearGradient>
                            ) : (
                                <View style={styles.gradient}>
                                    <ThemedText style={styles.eachTabTitle}>Tip Every Unique Holder</ThemedText>
                                </View>
                            )
                        }
                    </TouchableOpacity>
                    <TouchableOpacity style={[styles.eachTab, activeTab === 0 && styles.inactiveTab]} activeOpacity={0.8} onPress={changeTab}>
                        {
                            activeTab === 1 ? (
                                <LinearGradient
                                    colors={['#F36A27', '#CE4909']}  // Color gradient
                                    start={{ x: 0.109, y: 0 }}  // Start point for the gradient (109 degrees)
                                    end={{ x: 0.7968, y: 1 }}   // End point for the gradient (79.68%)
                                    style={styles.gradient}
                                >
                                    <ThemedText style={styles.eachTabTitle}>Tip Every Ticket</ThemedText>
                                </LinearGradient>
                            ) : (
                                <View style={styles.gradient}>
                                    <ThemedText style={styles.eachTabTitle}>Tip Every Ticket</ThemedText>
                                </View>
                            )
                        }
                    </TouchableOpacity>
                </View>
                {
                    tipUniqueHolder ? (
                        <ThemedText style={{ textAlign: 'center', fontSize: 15, lineHeight: 20 }}>Every unique holder will receive the same tip, regardless of how many tickets they hold.</ThemedText>
                    ) : (
                        <ThemedText style={{ textAlign: 'center', fontSize: 15, lineHeight: 20 }}>Every single ticket will receive the same tip. For example, if someone holds 5 tickets, they will receive 5 tips.</ThemedText>
                    )
                }
                <ThemedText type="defaultGrey" style={{ textAlign: 'center' }}>(If you hold your own ticket, you will be excluded)</ThemedText>
                <Controller
                    name="currency"
                    control={form.control}
                    render={({ field: { name, onChange, value } }) => {
                        return (
                            <View style={{ marginTop: 20 }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10, }}>
                                    <ThemedText style={styles.inputLabel}>
                                        BALANCE:
                                    </ThemedText>
                                    <ThemedText style={styles.balance}>{balance[token.name as keyof typeof balance]}</ThemedText>
                                </View>
                                <Dropdown
                                    data={tokensWithoutSolana}
                                    onChange={(value) => {
                                        setToken(
                                            tokensWithoutSolana.find((token) => token.name === value.name) ??
                                            AVAX
                                        );
                                        setTipAmount("0");
                                        form.setValue("amountPerTip", "0");
                                        form.setValue("totalAmount", "0");
                                        onChange(value.name);
                                    }}
                                    dropdownStyle={{
                                        backgroundColor: Colors.dark.secondaryBackground,
                                        borderColor: Colors.dark.darkGrey,
                                        borderWidth: 1,
                                    }}
                                    value={value}
                                    placeholder={token.name}
                                    style={styles.inputStyle}
                                    defaultValue={AVAX}
                                    balance={balance}
                                />
                            </View>
                        );
                    }}
                />
                <Controller
                    name="amountPerTip"
                    control={form.control}
                    render={({ field: { name, onChange, value, onBlur, ref, } }) => {
                        return (
                            <CustomTextInput
                                labelText={
                                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10, }}>
                                        <ThemedText style={styles.inputLabel}>
                                            AMOUNT PER TIP:
                                        </ThemedText>
                                        <ThemedText style={styles.balance}>
                                            {Number(removeCommas(singleTipUSD)) === 0
                                                ? "$0"
                                                : Number(removeCommas(singleTipUSD)) < 0.01
                                                    ? "< $0.01"
                                                    : `$${singleTipUSD}`}
                                        </ThemedText>
                                    </View>
                                }
                                placeholder="How much do you want to tip?"
                                value={value}
                                onChangeText={(text) => {
                                    let value: string | number = text;
                                    if (value === "") {
                                        setTipAmount("0");
                                        onChange(value);
                                        return;
                                    }

                                    if (!value.includes(".")) {
                                        value = getFloatValue(value);
                                        if (isNaN(value)) return;
                                        value = numberFormatter.format(value);
                                    }
                                    setTipAmount(value);
                                    onChange(value);
                                }}
                                onBlur={onBlur}
                                inputStyle={{
                                    borderColor: Colors.dark.lightgrey,
                                    borderWidth: 1,
                                    borderRadius: 8,
                                }}
                                errorMessage={form.formState.errors.amountPerTip?.message}
                            />
                        );
                    }}
                />
                <View style={{ marginTop: 20 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10, }}>
                        <ThemedText style={styles.inputLabel}>
                            TOTAL AMOUNT:
                        </ThemedText>
                        <ThemedText style={styles.balance}>
                            {totalAmountInUSD}
                        </ThemedText>
                    </View>
                    <View style={styles.totalAmountContainer}>
                        <Image
                            style={styles.placeholderImage}
                            source={token.icon}
                        />
                        <ThemedText style={styles.totalAmountText}>
                            {form.watch("amountPerTip")
                                ? Number((totalNumberOfTips * Number(removeCommas(form.watch("amountPerTip")))).toFixed(2))
                                : ""}
                        </ThemedText>
                    </View>
                </View>
            </ScrollView>
            <Button
                onPress={onSendTipsClick}
                style={styles.sendTips}
                disabled={isDisabled}
            >
                <ThemedText type="bold">Send tips</ThemedText>
            </Button>
            <ThemedText type="defaultGrey" style={styles.tipCount}>
                {totalNumberOfTips} {`${totalNumberOfTips === 1 ? "tip" : "tips"}`} will be sent
            </ThemedText>
            <TouchableOpacity style={styles.tippingInfoContainer} onPress={() => setIsTipInfoOpen(true)}>
                <InformationCircleOutlineIcon color={Colors.dark.offWhite} height={25} width={25} />
                <ThemedText style={{ textDecorationLine: 'underline' }} type="bold">
                    Tipping Information
                </ThemedText>
            </TouchableOpacity>
            <Modal
                animationType="fade"
                transparent={false}
                visible={isEditTipReceiversOpen}
                onRequestClose={() => {
                    setIsEditTipReceiversOpen(prev => !prev);
                }}>
                <EditTipReceivers
                    tipReceivers={tipReceivers}
                    setTipReceivers={setTipReceivers}
                    closeEditTipReceivers={closeEditTipReceivers}
                    showTipsCount={!tipUniqueHolder}
                />
            </Modal>
            <CenterModal isOpen={isTipInfoOpen} setIsOpen={setIsTipInfoOpen}>
                <View style={{ alignItems: 'center', gap: 20 }}>
                    <TippingPartyOutlineIcon color={Colors.dark.white} height={23} width={23} />
                    <ThemedText style={{ fontSize: 18, fontWeight: '600', fontFamily: "InterSemiBold", color: Colors.dark.offWhite }}>
                        Tipping Information
                    </ThemedText>
                    <ThemedText type="lightGreySubtitle" style={{ textAlign: 'center', fontSize: 14, lineHeight: 20 }}>
                      A 2% fee is deducted from every tip sent on the platform. This fee is withdrawn from the total tipped amount, ensuring that senders do not incur any extra charges.
                    </ThemedText>
                    <Button
                        onPress={() => setIsTipInfoOpen(false)}
                        style={{ width: '100%' }}
                    >
                        <ThemedText type="bold">Close</ThemedText>
                    </Button>
                </View>
            </CenterModal>
        </View>
    )
}, (prevProps, nextProps) => {
    return (
        prevProps.ownerUserId === nextProps.ownerUserId &&
        prevProps.loggedInUserId === nextProps.loggedInUserId &&
        prevProps.closeTippingParty === nextProps.closeTippingParty
    )
})

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.dark.secondaryBackground,
        padding: 20
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        marginBottom: 20
    },
    subTitle: {
        fontSize: 16,
        fontWeight: '600',
        fontFamily: "InterSemiBold",
        color: Colors.dark.grey
    },
    title: {
        fontSize: 26,
        fontWeight: '600',
        fontFamily: "InterSemiBold",
        color: Colors.dark.offWhite,
        marginBottom: 20
    },
    eachTab: {
        width: '48%',
        height: 70,
        borderRadius: 10,
    },
    eachTabTitle: {
        fontSize: 14,
        fontWeight: '600',
        fontFamily: "InterSemiBold",
        color: Colors.dark.offWhite,
        lineHeight: 18
    },
    gradient: {
        height: '100%',
        paddingHorizontal: 20,
        alignItems: 'flex-start',
        justifyContent: 'center',
        borderRadius: 10,
    },
    inactiveTab: {
        backgroundColor: Colors.dark.secondaryBackground,
        borderColor: Colors.dark.grey,
        borderWidth: 1,
    },
    inputLabel: {
        color: Colors.dark.lightgrey,
        fontSize: 12,
        marginRight: 5
    },
    inputStyle: {
        marginBottom: 20,
    },
    balance: {
        fontSize: 12,
        fontWeight: '600',
        fontFamily: "InterSemiBold",
        color: Colors.dark.offWhite,
    },
    placeholderImage: {
        width: 20, // Adjust width as needed
        height: 20, // Adjust height as needed
        borderRadius: 10, // Adjust border radius if you want rounded images
    },
    totalAmountContainer: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: Colors.dark.lightgrey,
        borderRadius: 8,
        height: 50,
        paddingHorizontal: 15,
        gap: 10,
        flexDirection: 'row',
        alignItems: 'center'
    },
    totalAmountText: {
        fontSize: 15,
        fontFamily: "InterSemiBold",
        color: Colors.dark.white,
    },
    sendTips: {
        width: '100%', 
        marginBottom: 15
    },
    tipCount: {
        textAlign: 'center',
    },
    tippingInfoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
        justifyContent: 'center',
        marginTop: 15
    },
})