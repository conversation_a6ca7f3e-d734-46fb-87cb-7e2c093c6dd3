import { StyleSheet, TouchableOpacity, View } from "react-native";
import React, { memo, useEffect, useMemo } from "react";
import { PinOutlineIcon } from "@/components/icons";
import { ThemedText } from "@/components/ThemedText";
import { Nullable } from "@/types/common";
// import { BlurView } from "expo-blur";
import { globalStyles } from "@/app/globalStyles";
import { useGroup, useGroupStore } from "../_contexts/group-context";
import { usePinnedMessagesQuery } from "@/queries/chat-queries";
import { Colors } from "@/constants/Colors";
import { GroupMessageTypeEnum } from "@/queries/types/chats";

const ICON_SIZE = 16;

type TPinnedMessages = {
    headerHeight: Nullable<number>;
}

const PinnedMessages = memo(({ headerHeight }: TPinnedMessages) => {
    const { isOwnerSuspended, groupId } = useGroup();

    const setMessageId = useGroupStore((state) => state.actions.setMessageId);
    const messageId = useGroupStore((state) => state.messageId);
    const setLoadingOverlay = useGroupStore((state) => state.actions.setLoadingOverlay);
    const setPinnedMessagesCount = useGroupStore(
        (state) => state.actions.setPinnedMessagesCount,
    );

    const { data: pinnedMessagesData } = usePinnedMessagesQuery({
        groupId: groupId || '',
    });

    const pinnedMessages = useMemo(() => {
        if (!pinnedMessagesData) return [];

        return pinnedMessagesData.messages.reverse();
    }, [pinnedMessagesData]);
    

    const [activeMessage, activeMessageIndex] = useMemo(() => {
        if (pinnedMessages.length === 0) return [null, null];

        let index = 0;
        if (messageId && pinnedMessages.some((m) => m.id === messageId)) {
            const currentIndex =
                pinnedMessages.findIndex((m) => m.id === messageId) + 1;

            if (pinnedMessages.length > currentIndex) {
                index = currentIndex;
            }
        }
        const activeMessage = pinnedMessages[index];

        return [activeMessage, index];
    }, [messageId, pinnedMessages]);

    const handleMessageClick = () => {
        setMessageId(activeMessage?.id ?? '');
        setLoadingOverlay(true);
    };

    useEffect(() => {
        setPinnedMessagesCount(pinnedMessages?.length ?? 0);
    }, [pinnedMessages]);

    const isImage = activeMessage?.attachments?.[0]?.messageType === GroupMessageTypeEnum.Image;
    const isVideo = activeMessage?.attachments?.[0]?.messageType === GroupMessageTypeEnum.Video;
    const messageText = activeMessage?.message;

    return !isOwnerSuspended && headerHeight && pinnedMessages.length > 0 ? (
        // <BlurView intensity={isAndroid ? 20 : 50} experimentalBlurMethod='dimezisBlurView' style={[styles.blurContainer, { top: headerHeight }]}>
        <View style={[styles.blurContainer, { top: headerHeight }]}>
            <TouchableOpacity style={styles.subContainer} onPress={handleMessageClick}>
                <View style={styles.animation}>
                    {
                        Array.from({ length: pinnedMessages?.length }).map((_, index) => (
                            <View key={index} style={[styles.animationItem, { opacity: activeMessageIndex === index ? 1 : 0.3 }]} />
                        ))
                    }
                </View>
                <View style={[styles.messageContainer, { flex: 0.90 }]}>
                    <ThemedText style={[styles.title, { color: Colors.dark.brandOrange }]} type="bold">
                        Pinned Message {(activeMessageIndex ?? 0) + 1}
                    </ThemedText>
                    <View style={styles.messageTextContainer}>
                        {/* {
                            isImage ? (
                                <ImageOutlineIcon
                                    width={15}
                                    height={15}
                                    color={"rgba(157, 157, 157, 1)"} />
                            ) : null
                        } */}
                        <ThemedText style={[styles.title]} numberOfLines={1}>
                            {messageText ? messageText : isImage ? "Image" : isVideo ? "Video" : ''}
                        </ThemedText>
                    </View>
                </View>
                <View style={styles.pinIconContainer}>
                    <PinOutlineIcon color={Colors.dark.grey} height={ICON_SIZE} width={ICON_SIZE} />
                </View>
            </TouchableOpacity>
        </View>
        // </BlurView>
    ) : null;
}, (prevProps, nextProps) => prevProps.headerHeight === nextProps.headerHeight);

export default PinnedMessages;

const styles = StyleSheet.create({
    blurContainer: {
        backgroundColor: "#14141499",
        ...globalStyles.windowWidth,
        zIndex: 1,
        position: 'absolute',
    },
    subContainer: {
        paddingHorizontal: 20,
        paddingVertical: 10,
        gap: 5,
        flexDirection: 'row',
        alignItems: 'center',
    },
    title: {
        fontSize: 12,
        color: Colors.dark.grey
    },
    animation: {
        flex: 0.05,
        gap: 2
    },
    animationItem: {
        flex: 1,
        backgroundColor: Colors.dark.brandOrange,
        width: '20%',
        borderRadius: 10
    },
    pinIconContainer: {
        flex: 0.05,
        alignItems: 'flex-end',
    },
    messageContainer: {
        gap: 4,
    },
    messageTextContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },

});
