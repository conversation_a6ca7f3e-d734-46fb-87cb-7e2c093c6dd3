import { Svg, Path, SvgProps } from "react-native-svg";

export const NotificationsOutlineIcon = (props: SvgProps) => (
  <Svg
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <Path
      d="M11.9961 21.9948C10.4966 21.9948 9.15244 21.059 8.65527 19.6643L8.5632 19.4076H2.78916C2.35512 19.4076 2 19.0577 2 18.6299V17.4556C2 17.2482 2.08155 17.0564 2.22623 16.9086L4.50164 14.6377V8.97343C4.50427 8.73493 4.52532 8.49644 4.56741 8.26312C4.58319 8.12314 4.61213 7.97278 4.65159 7.82761C4.86466 6.80881 5.31974 5.81594 5.96159 4.97084C6.04577 4.85418 6.14047 4.74012 6.24306 4.63383C6.40089 4.44718 6.55347 4.28386 6.70078 4.14128C7.70827 3.17952 8.96304 2.49773 10.323 2.1711C10.3441 2.16591 10.3651 2.16332 10.3862 2.16332H10.3967L11.007 2.05962C11.0228 2.05962 11.0359 2.05703 11.0517 2.05703C11.3542 2.02074 11.6725 2 11.9882 2C12.3038 2 12.6116 2.01815 12.9167 2.05703H12.9352C12.9509 2.05703 12.9667 2.05703 12.9825 2.06222L13.5875 2.17369H13.5981C13.6191 2.17369 13.6401 2.17369 13.6612 2.18146C15.0185 2.50551 16.2654 3.18211 17.2729 4.1361C17.4439 4.30719 17.6044 4.48088 17.7517 4.65457C17.8437 4.75308 17.9411 4.86714 18.0279 4.98639C18.6697 5.82631 19.1275 6.81918 19.351 7.85353C19.3879 7.98833 19.4168 8.13869 19.4352 8.28905C19.4721 8.5094 19.4957 8.74789 19.4984 8.9838V14.6377L21.7711 16.9112C21.9158 17.059 21.9974 17.2508 22 17.4556V18.6325C22 19.0603 21.6449 19.4102 21.2108 19.4102H15.4394L15.3474 19.6695C14.9896 20.6701 14.2004 21.4478 13.1851 21.8004C12.8036 21.9326 12.4064 22 12.0066 22L11.9961 21.9948ZM10.5729 19.4025C10.8807 19.8743 11.4147 20.175 11.9987 20.1802C12.5932 20.1802 13.1351 19.8795 13.4455 19.4025H10.5756H10.5729ZM19.8561 17.5878L17.849 15.5995C17.7017 15.4543 17.6202 15.2599 17.6202 15.0551V9.00713C17.6202 8.84122 17.6044 8.67272 17.5728 8.51199C17.5728 8.50162 17.5702 8.49384 17.5702 8.48347C17.5649 8.4057 17.5491 8.32793 17.5281 8.25275C17.3597 7.43357 17.0125 6.67142 16.518 6.02074C16.4653 5.94556 16.4127 5.88075 16.3549 5.81854C16.2365 5.67596 16.1102 5.53856 15.984 5.41413C15.2158 4.68827 14.2557 4.17239 13.214 3.92871L12.6695 3.82761C12.4538 3.80169 12.217 3.78872 11.9829 3.78872C11.7488 3.78872 11.512 3.80169 11.2779 3.8302L10.736 3.9313C9.70748 4.17498 8.74997 4.69086 7.97659 5.41931C7.94765 5.45042 7.90293 5.49968 7.85558 5.54893C7.77404 5.63448 7.68986 5.72262 7.6162 5.81076C7.55307 5.88075 7.50046 5.94556 7.45311 6.01555C6.95331 6.67401 6.60608 7.43876 6.44298 8.23461C6.41405 8.33052 6.40089 8.4083 6.39563 8.48866C6.39563 8.49903 6.39563 8.5094 6.393 8.51717C6.36144 8.68049 6.34565 8.849 6.34565 9.01491V15.0603C6.34565 15.2677 6.26411 15.4595 6.1168 15.6047L4.13337 17.593H19.8614L19.8561 17.5878Z"
      fill="currentColor"
    />
  </Svg>
);
