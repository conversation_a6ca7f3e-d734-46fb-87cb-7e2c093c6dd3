import { Modal, StyleSheet, TouchableOpacity, View } from "react-native";
import React, { useCallback, useState } from "react";
import { Colors } from "@/constants/Colors";
import { ThemedText } from "@/components/ThemedText";
import { GIFOutlineIcon, ImageOutlineIcon } from "@/components/icons";
import { GAP } from "./account-suspended";
import { useGroup } from "../_contexts/group-context";
import { Nullable } from "@/types/common";
import * as ImagePicker from "expo-image-picker";
import { toast } from "@/components/toast";
import { upload } from "@/utils";
import { v4 as uuid } from "uuid";
import { GifList } from "./gif-list";
import { GIFType } from "@/queries/types/threads";
import { v4 as uuidv4 } from "uuid";
import { FileType } from "@/queries/types/chats";
import { TippingPartyOutlineIcon } from "@/components/icons/tipping-party-outline";
import { TippingParty } from "./tipping-party";
import { useUser } from "@/stores/user";

type TAddAttachmentContainer = {
  footerHeight: Nullable<number>;
}

const AddAttachmentContainer = ({ footerHeight }: TAddAttachmentContainer) => {
  const { isAttachImageOpen, setIsAttachImageOpen, setPreview, setFiles, data, isLoading, isSuccess } = useGroup();
  const { user } = useUser();
  const showPopup = isAttachImageOpen && footerHeight;
  const [gifListModalVisible, setGifListModalVisible] = useState(false);
  const [tippingPartyModalVisible, setTippingPartyModalVisible] = useState(false);
  const isEnabled = !isLoading && isSuccess && !!data?.group;
  const hasTippingParty =
    isEnabled && data?.group?.ownerUserId && !data?.group?.isDirect;

  const handleGifSelection = useCallback((gif: GIFType) => {
    const id = uuidv4();
    setPreview?.({
      uri: gif.media_formats.webp.url,
      type: "image",
      progress: 0,
      loading: false
    })
    const files: FileType[] = [
      {
        id,
        fileType: "image",
        mimetype: "image/webp",
        url: gif.media_formats.webp.url,
      },
    ];
    setFiles?.(files);
  }, [setPreview, setFiles])

  const closeGifList = useCallback(() => {
    setGifListModalVisible(false)
  }, [])

  const handleGifPress = () => {
    setIsAttachImageOpen?.(false)
    setGifListModalVisible(true)
  }

  const closeTippingParty = useCallback(() => {
    setTippingPartyModalVisible(false)
  }, [])

  const handleTippingPartyPress = () => {
    setIsAttachImageOpen?.(false)
    setTippingPartyModalVisible(true)
  }

  const openGallery = async () => {
    setIsAttachImageOpen?.(false)
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    const selectedFile = result?.assets?.[0];
    if (!selectedFile) return;

    if (selectedFile.mimeType?.includes("video/ogg")) {
      toast.danger("Please upload video in either mp4 or webm format");
      return;
    }
    if (
      selectedFile.mimeType?.includes("image") &&
      !(
        selectedFile.mimeType?.includes("image/jpeg") ||
        selectedFile.mimeType?.includes("image/gif") ||
        selectedFile.mimeType?.includes("image/png")
      )
    ) {
      toast.danger("Please upload image in JPEG, JPG, PNG or GIF format");
      return;
    }

    if (
      selectedFile.mimeType?.includes("image") &&
      selectedFile.fileSize &&
      selectedFile.fileSize > 5 * 1024 * 1024
    ) {
      toast.danger("Uploaded image file cannot exceed 5 MB");
      return;
    }

    if (
      selectedFile.mimeType?.includes("video") &&
      selectedFile.fileSize &&
      selectedFile.fileSize > 100 * 1024 * 1024
    ) {
      toast.danger("Uploaded video file cannot exceed 100 MB");
      return;
    }

    setPreview?.({
      uri: selectedFile.uri,
      type: selectedFile.mimeType?.includes("image") ? "image" : "video",
      progress: 0,
      loading: true
    })

    const file = {
      name: `${uuid()}.${selectedFile?.mimeType?.split("/")[1]}`,
      type: selectedFile.mimeType,
      lastModified: Date.now(),
      lastModifiedDate: new Date(),
      webkitRelativePath: "",
      size: selectedFile?.fileSize,
      uri: selectedFile.uri
    };

    try {
      const res = await upload({
        file,
        onProgressChange: (progress) => {
          setPreview?.((prev) => ({
            ...prev,
            progress: progress
          }));
        },
      });
      setFiles?.([
        {
          id: res.id,
          fileType: file.type?.includes("image") ? "image" : "video",
          mimetype: file.type ?? "",
          url: res.url,
        },
      ]);
    } catch (error) {
      console.error(error);
      toast.danger("File upload failed");
      setPreview?.(null)
    } finally {
      setPreview?.((prev) => ({
        ...prev,
        loading: false
      }))
    }
  }

  return (
    <View style={[styles.plusMenuContainer, {
      bottom: footerHeight,
      paddingHorizontal: showPopup ? 25 : 0,
      paddingVertical: showPopup ? 25 : 0,
    }]}>
      {
        showPopup ? (
          <>
            {
              hasTippingParty ? (
                <TouchableOpacity style={styles.row} onPress={handleTippingPartyPress}>
                  <TippingPartyOutlineIcon color={Colors.dark.white} height={23} width={23} />
                  <ThemedText style={styles.semiBoldText}>
                    Tipping Party
                  </ThemedText>
                </TouchableOpacity>
              ) : null
            }
            <TouchableOpacity style={styles.row} onPress={handleGifPress}>
              <GIFOutlineIcon color={Colors.dark.white} height={27} width={27} />
              <ThemedText style={styles.semiBoldText}>
                GIFs
              </ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.row} onPress={openGallery}>
              <ImageOutlineIcon color={Colors.dark.white} height={27} width={27} />
              <ThemedText style={styles.semiBoldText}>
                Images & Video
              </ThemedText>
            </TouchableOpacity>
          </>
        ) : null
      }
      <Modal
        animationType="fade"
        transparent={false}
        visible={gifListModalVisible}
        onRequestClose={() => {
          setGifListModalVisible(prev => !prev);
        }}>
        <GifList closeGifList={closeGifList} handleGifSelection={handleGifSelection} />
      </Modal>
      <Modal
        animationType="fade"
        transparent={false}
        visible={tippingPartyModalVisible}
        onRequestClose={() => {
          setTippingPartyModalVisible(prev => !prev);
        }}>
        <TippingParty
          closeTippingParty={closeTippingParty}
          ownerUserId={data?.group?.ownerUserId ?? ''}
          loggedInUserId={user?.id ?? ''} />
      </Modal>
    </View>
  )
};

export default AddAttachmentContainer;

const styles = StyleSheet.create({
  plusMenuContainer: {
    position: 'absolute',
    zIndex: 1,
    right: 0,
    backgroundColor: Colors.dark.secondaryBackground,
    borderRadius: 6,
    gap: GAP * 2
  },
  semiBoldText: {
    fontSize: 18,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: GAP * 1.5,
  }
});
