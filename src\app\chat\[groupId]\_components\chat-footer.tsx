import { KeyboardAvoidingView, LayoutChangeEvent, StyleSheet, TextInput, TouchableOpacity, View } from "react-native";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { CloseOutlineIcon, ReplyOutlineIcon } from "@/components/icons";
import { Colors } from "@/constants/Colors";
import { isIos } from "@/constants/Device";
import { useGroup } from "../_contexts/group-context";
import { useUser } from "@/stores";
import ReplyMessage from "./reply-message";
import Preview from "./preview";
import { MessageRequestView } from "./message-request-view";
import { acceptChat, leaveChat } from "@/api/client/chat";
import { router } from "expo-router";
import { InputBoxView } from "./input-box-view";
import { NeedFollowView } from "./need-follow-view";
import { ChatFooterSkeleton } from "./chat-footer-skaleton";
import { NeedKeyholderView } from "./need-keyholder-view";
import { DMDisabledView } from "./dm-disabled-view";
import { YouBlockedUserView } from "./you-blocked-user-view";
import { YouBlockedByUserView } from "./you-blocked-by-user-view";

export const maxTextBoxAndSendBtnHeight = 30;

type TChatFooter = {
  onLayout: (event: LayoutChangeEvent) => void;
}

const ChatFooter = ({ onLayout }: TChatFooter) => {
  const { user } = useUser();
  const {
    groupId,
    data,
    messageToReplyTo,
    setMessageToReplyTo,
    preview,
    updateIsRequest,
    isBlockAPILoading,
    isYouBlockedUser,
    isYouBlockedByUser,
    isSuspended,
    isAdmin,
    isLoading,
    isSuccess,
    isChatMateLoading,
    isOwnerLoading
  } = useGroup();

  //chatmate settings is the settings of the chatmate with whom loggedin user is trying to chat
  const { group, chatMateSettings } = data || {};
  const { isRequest, lastUserId } = group || {};
  const isFetchingGroup = isLoading || !isSuccess || !group
  //isDirect property denotes that the group is a dm group
  const showDMView = group?.isDirect && !isAdmin;

  //states
  const inputRef = useRef<TextInput | null>(null);
  const [isTicketPurchased, setIsTicketPurchased] = useState<boolean>(false);

  /* 
  In case of non-dm groups (ticket chats/rooms), directly input box will be shown unless owner is suspended.

  In case of dm groups, scenario: User 1 Trying to initate chat with User 2
  when any user try to initiate new message request to other, depending on message settings, different views are shown
  1. User 2 requires User 1 to follow him in order to start the chat.
  2. User 2 requires User 1 to be a keyholder (purchase ticket) in order to start the chat.
  3. User 2 disables the DM feature i.e nobody can message him.
  4. User 1 block User 2. Hence chat can't be initiated.
  5. User 1 get blocked by User 2. Hence chat can't be initiated.
  6. Finally, if all conditions are met, then the message request can be initiated and the message request view will be shown
  7. If message request is accepted by User 2, then the input box will be shown
  8. If message request is rejected by User 2, then the input box will be shown. But if message is tried to send from any party it will throw an error.
  */

  const isTemporary = group && group.isTemporary;

  //check if loggedin user is not a keyholder of chatmate
  const isHolderNeed =
    chatMateSettings &&
    chatMateSettings.chatMate &&
    chatMateSettings.chatMate.id !== user?.id &&
    chatMateSettings.holders &&
    !chatMateSettings.isKeyholder;

  //check if loggedin user is a keyholder of chatmate
  const holdersFulfilled =
    chatMateSettings &&
    chatMateSettings.chatMate &&
    chatMateSettings.chatMate.id !== user?.id &&
    chatMateSettings.holders &&
    chatMateSettings.isKeyholder;

  //check if chatmate is not a follower of loggedin user
  const isFollowerNeed =
    chatMateSettings && !chatMateSettings.holders && chatMateSettings.followers && !chatMateSettings.isFollower;

  //check if chatmate is a follower of loggedin user
  const followersFulfilled =
    chatMateSettings && chatMateSettings.followers && chatMateSettings.isFollower;

  //check if chatmate has disabled DM feature
  const isNobody = chatMateSettings && !chatMateSettings.holders && !chatMateSettings.followers;

  //check if message request is initiated by another user
  const isMessageRequest = isRequest && lastUserId !== user?.id;

  //input box view only if user is not suspended
  const inputBoxView = isSuspended ? null : <InputBoxView ref={inputRef} onLayout={onLayout} />

  const showNeedFollowView = useMemo(() => {
    return !holdersFulfilled &&
      isFollowerNeed &&
      !isRequest &&
      isTemporary &&
      !isYouBlockedUser &&
      !isYouBlockedByUser
  }, [holdersFulfilled, isFollowerNeed, isRequest, isTemporary, isYouBlockedUser, isYouBlockedByUser]);

  const showNeedKeyholderView = useMemo(() => {
    return !followersFulfilled &&
      isHolderNeed &&
      !isRequest &&
      isTemporary &&
      !isTicketPurchased &&
      !isYouBlockedUser &&
      !isYouBlockedByUser
  }, [followersFulfilled, isHolderNeed, isRequest, isTemporary, isTicketPurchased, isYouBlockedUser, isYouBlockedByUser]);

  const showYouBlockedUserView = useMemo(() => {
    return isYouBlockedUser && !isBlockAPILoading
  }, [isYouBlockedUser, isBlockAPILoading]);

  const showYouBlockedByUserView = useMemo(() => {
    return isYouBlockedByUser && !isBlockAPILoading
  }, [isYouBlockedByUser, isBlockAPILoading]);

  const showDisabledDMView = useMemo(() => {
    return isTemporary &&
      isNobody &&
      !isRequest &&
      !isYouBlockedUser &&
      !isYouBlockedByUser
  }, [isTemporary, isNobody, isRequest, isYouBlockedUser, isYouBlockedByUser]);

  //check if message request view should be shown
  const showMessageRequestView = useMemo(() => {
    return isMessageRequest && !isYouBlockedUser && !isYouBlockedByUser;
  }, [isMessageRequest, isYouBlockedUser, isYouBlockedByUser])

  const showInputBoxView = useMemo(() => {
    return !(isNobody && isTemporary) &&
      (followersFulfilled ||
        holdersFulfilled ||
        !isTemporary ||
        isTicketPurchased) &&
      !isRequest &&
      !isYouBlockedUser &&
      !isYouBlockedByUser
  }, [isNobody, followersFulfilled, holdersFulfilled, isTemporary, isTicketPurchased, isRequest, isYouBlockedUser, isYouBlockedByUser])


  useEffect(() => {
    if (messageToReplyTo) {
      inputRef.current?.focus();
    }
  }, [messageToReplyTo])


  const handleDelete = useCallback(() => {
    leaveChat({ groupId: groupId || "" });
    router.back();
  }, [groupId])

  const handleAccept = useCallback(() => {
    //update the isRequest to false so that request form is now hidden
    updateIsRequest?.(false);
    acceptChat({ groupId: groupId || "" });
  }, [groupId, updateIsRequest])

  if (isFetchingGroup || isChatMateLoading || isOwnerLoading || isBlockAPILoading) {
    return <ChatFooterSkeleton />;
  }

  return (
    <KeyboardAvoidingView
      behavior={isIos ? 'padding' : undefined}
      keyboardVerticalOffset={80}
    >
      {
        preview ? (
          <Preview uri={preview.uri} type={preview.type} progress={preview.progress} loading={preview.loading} />
        ) : null
      }
      {!!messageToReplyTo ?
        (
          <View style={styles.replyMessageContainer}>
            <ReplyOutlineIcon width={23} height={23} color={Colors.dark.white} />
            <View style={{ flex: 1 }}>
              <ReplyMessage
                messageToReplyToUserId={messageToReplyTo.userId}
                messageToReplyToUserName={messageToReplyTo.userName}
                messageToReplyToMessage={messageToReplyTo.message}
                userId={user?.id || ""}
                type={"chat-footer"}
                attachmentType={messageToReplyTo.attachments?.[0]?.messageType}
                attachmentUrl={messageToReplyTo.attachments?.[0]?.url}
              />
            </View>
            <TouchableOpacity onPress={() => setMessageToReplyTo?.(null)}>
              <CloseOutlineIcon width={30} height={30} color={Colors.dark.white} />
            </TouchableOpacity>
          </View>
        ) : null
      }
      {showDMView ? (
        <>
          {
            showMessageRequestView ? (
              <MessageRequestView
                chatMateName={chatMateSettings?.chatMate?.twitterHandle || ""}
                handleDelete={handleDelete}
                handleAccept={handleAccept}
                onLayout={onLayout}
              />
            ) : null
          }
          {
            showInputBoxView ? inputBoxView : null
          }
          {
            showNeedFollowView ? (
              <NeedFollowView onLayout={onLayout} />
            ) : null
          }
          {
            showNeedKeyholderView ? (
              <NeedKeyholderView onLayout={onLayout} />
            ) : null
          }
          {
            showDisabledDMView ? (
              <DMDisabledView onLayout={onLayout} />
            ) : null
          }
          {
            showYouBlockedUserView ? (
              <YouBlockedUserView
                onLayout={onLayout}
                chatMateHandle={chatMateSettings?.chatMate?.twitterHandle || ""}
              />
            ) : null
          }
          {
            showYouBlockedByUserView ? (
              <YouBlockedByUserView
                onLayout={onLayout}
              />
            ) : null
          }
        </>
      ) : inputBoxView}
    </KeyboardAvoidingView >
  );
};

export default ChatFooter;

const styles = StyleSheet.create({
  replyMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 10,
    paddingHorizontal: 10,
    paddingTop: 10
  }
});
