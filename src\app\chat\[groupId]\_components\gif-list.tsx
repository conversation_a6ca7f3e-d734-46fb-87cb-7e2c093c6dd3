import { ThemedText } from "@/components/ThemedText"
import { View, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity } from "react-native"
import BackButton from "@/components/navigation/BackButton"
import { CustomTextInput } from "@/components/ui/text-input"
import { Colors } from "@/constants/Colors"
import { SearchFilledIcon } from "@/components/icons"
import { useState, memo, useCallback } from "react"
import { screenWidth } from "@/constants/Device"
import useThrottle from "@/hooks/use-throttle"
import { useGIFsQuery } from "@/queries/threads-query"
import { GIFType } from "@/queries/types/threads"
import { Image } from 'expo-image';

const WIDTH = screenWidth;

type TGifItem = {
    gif: GIFType,
    handleGifSelection: (gif: GIFType) => void,
    closeGifList: () => void
}

const GifItem = memo(({ gif, handleGifSelection, closeGifList }: TGifItem) => {

    const handleGifSelect = useCallback(() => {
        closeGifList();
        handleGifSelection(gif);
    }, [handleGifSelection, closeGifList, gif]);    

    return (
        <TouchableOpacity
            style={{
                width: WIDTH / 3, // This will ensure 3 items per row
                marginBottom: 10,
                height: 100,
            }}
            onPress={handleGifSelect}
        >
            <Image
                style={{ width: '100%', height: '100%' }}
                source={{ uri: gif.media_formats.webp.url, isAnimated: true }}
                contentFit="contain"
            />
        </TouchableOpacity>
    )
}, (prevProps, nextProps) => {
    return (
        prevProps.gif === nextProps.gif &&
        prevProps.handleGifSelection === nextProps.handleGifSelection &&
        prevProps.closeGifList === nextProps.closeGifList
    )
})

type TGifList = {
    closeGifList: () => void,
    handleGifSelection: (gif: GIFType) => void
}

export const GifList = memo(({ closeGifList, handleGifSelection }: TGifList) => {
    const [searchValue, setSearchValue] = useState<string>('');
    const throttledSearch = useThrottle(searchValue);
    const { data, isFetching } = useGIFsQuery(throttledSearch);

    const renderItem = useCallback(({ item }: { item: GIFType }) => {
        return <GifItem gif={item} handleGifSelection={handleGifSelection} closeGifList={closeGifList} />
    }, [handleGifSelection, closeGifList]);

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <View style={styles.backButtonContainer}>
                    <BackButton onPress={closeGifList} />
                </View>
                <View style={styles.searchContainer}>
                    <CustomTextInput
                        IconLeft={<SearchFilledIcon width={16} height={16} fill={Colors.dark.grey} />}
                        placeHolderColor={Colors.dark.grey}
                        placeholder="Search for GIFs"
                        inputStyle={styles.searchInput}
                        editable
                        onChangeText={setSearchValue}
                        value={searchValue}
                    />
                </View>
            </View>
            <View style={styles.separator} />
            {
                isFetching ? (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator size="small" color={Colors.dark.white} />
                    </View>
                ) :
                    data?.results && data?.results?.length > 0 ? (
                        <FlatList
                            data={data.results}
                            numColumns={3}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => index.toString()}
                        />
                    ) : (
                        <View style={styles.noResultsContainer}>
                            <ThemedText>No results found</ThemedText>
                        </View>
                    )
            }
        </View>
    )
}, (prevProps, nextProps) => {
    return prevProps.closeGifList === nextProps.closeGifList
})

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingVertical: 10,
        backgroundColor: Colors.dark.secondaryBackground
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%',
        paddingHorizontal: 16
    },
    searchInput: {
        borderRadius: 100,
        borderColor: Colors.dark.darkGrey,
    },
    backButtonContainer: {
        width: '10%',
    },
    searchContainer: {
        width: '90%',
    },
    separator: {
        height: 1,
        backgroundColor: Colors.dark.darkGrey,
        marginVertical: 15
    },
    noResultsContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
    }
})