import { create } from "zustand";
import { persist } from "zustand/middleware";

interface Store {
  tab: "following" | "trending";
}

interface Actions {
  setTab: (tab: "following" | "trending") => void;
}

export const useHomeTabStore = create(
  persist<Store & Actions>(
    (set) => ({
      tab: "following",
      setTab: (tab) => set({ tab }),
    }),
    {
      name: "home-tab-storage",
    },
  ),
);
