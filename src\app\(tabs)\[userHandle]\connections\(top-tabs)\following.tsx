import React, { useMemo } from "react";
import { View, FlatList, ActivityIndicator } from "react-native";
import { useGlobalSearchParams } from "expo-router";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { UserListItem } from "../_components/user-list-item";
import { UserListItemSkeleton } from "../_components/user-list-item-skeleton";
import { useFollowingInfiniteQuery } from "@/queries/follow-queries";
import { useUserByHandleQuery } from "@/queries/user-queries";
import { Colors } from "@/constants/Colors";
import { Following } from "@/queries/types/follow";
import { useSearchString } from "./SearchStringContext";

export default function FollowingTab() {
  const searchString = useSearchString();
  const params = useGlobalSearchParams<{ userHandle: string }>();
  const { data: userData, isLoading: isUserDataLoading } = useUserByHandleQuery(params.userHandle as string);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isFetching,
  } = useFollowingInfiniteQuery({
    userId: userData?.user.id,
    searchString,
  });

  const following = useMemo(() => {
    if (!data) return [];
    return data?.pages.reduce((prev, current) => {
      return [...prev, ...(current?.followingsWithFollowedByloggedInUser || [])];
    }, [] as Following[]);
  }, [data]);

  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <View className="py-5 items-center">
          <ActivityIndicator size="small" color={Colors.dark.offWhite} />
        </View>
      );
    }
    return null;
  };

  const handleEndReached = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };

  if (isUserDataLoading || isLoading || isFetching) {
    // Show a list of skeletons while loading or fetching new search results
    return (
      <ThemedView className="flex-1 bg-dark-background pt-2 mt-1">
        {Array.from({ length: 8 }).map((_, idx) => (
          <UserListItemSkeleton key={idx} />
        ))}
      </ThemedView>
    );
  }

  return (
    <ThemedView className="flex-1 bg-dark-background">
      {following.length === 0 ? (
        <View className="flex-1 items-center justify-center pt-10">
          <ThemedText className="text-sm text-dark-greyText">
            Not following anyone
          </ThemedText>
        </View>
      ) : (
        <FlatList
          data={following}
          renderItem={({ item }) => (
            <UserListItem
              user={{ ...item.following, following: null }}
              isFollowing={item.followedByloggedInUser}
            />
          )}
          keyExtractor={(item) => item.id.toString()}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          className="pt-2 mt-1"
        />
      )}
    </ThemedView>
  );
}
