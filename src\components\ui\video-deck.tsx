import { Colors } from "@/constants/Colors";
import { memo, useState } from "react";
import VideoPlayer from 'react-native-video-player';

type TVideoDeck = {
    attachmentUrl: string;
    borderRadius: number;
}

export const VideoDeck = memo(({ attachmentUrl, borderRadius }: TVideoDeck) => {
    const [videoResizeMode, setVideoResizeMode] = useState<'cover' | 'contain'>('cover');

    return (
        <VideoPlayer
            // source={{ uri: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4' }}
            source={{ uri: attachmentUrl }}
            autoplay={false}
            defaultMuted
            repeat={false}
            fullScreenOnLongPress
            onFullscreenPlayerWillPresent={() => setVideoResizeMode('contain')}
            onFullscreenPlayerWillDismiss={() => setVideoResizeMode('cover')}
            resizeMode={videoResizeMode}
            style={{
                width: '100%',
                height: '100%',
                borderRadius: borderRadius
            }}
            customStyles={{
                videoWrapper: {
                    borderRadius: borderRadius
                },
                controls: {
                    backgroundColor: 'transparent'
                },
                seekBarKnob: {
                    backgroundColor: Colors.dark.white
                },
                seekBarProgress: {
                    backgroundColor: Colors.dark.white
                },
            }}
        />
    )
}, (prevProps, nextProps) => {
    return prevProps.attachmentUrl === nextProps.attachmentUrl && prevProps.borderRadius === nextProps.borderRadius;
})