import { View, StyleSheet, FlatList, RefreshControl } from "react-native";
import { useCallback, useState } from "react";
import MessageSearch from "@/app/(tabs)/messages/_components/message-search";
import { Colors } from "@/constants/Colors";
import { paddingHorizontal } from "@/app/(tabs)/messages/(top-tabs)/_layout";
import { useRequestUsersSearchQuery } from "@/queries";
import useThrottle from "@/hooks/use-throttle";
import { ListSkeleton } from "@/app/(tabs)/messages/_components/skeleton";
import { ThemedText } from "@/components/ThemedText";
import { globalStyles } from "@/app/globalStyles";
import { User } from "@/queries/types/top-users-response";
import { UserItem } from "./_components/user-item";

export default function NewDM() {
    const [searchValue, setSearchValue] = useState<string>('');
    const throttledSearchValue = useThrottle(searchValue).trim();
    const [refreshing, setRefreshing] = useState(false);

    const { data: searchData, isLoading: isSearchDataLoading, refetch } =
        useRequestUsersSearchQuery(throttledSearchValue);

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await refetch();
        } catch (error) {
        } finally {
            setRefreshing(false);
        }
    };

    const renderItem = useCallback(({ item }: { item: User }) => {
        return <UserItem user={item} />
    }, []);

    return (
        <View style={styles.container}>
            <MessageSearch
                placeHolder="Search The Arena"
                placeHolderColor={Colors.dark.grey}
                searchHandler={setSearchValue}
                value={searchValue}
            />
            {
                isSearchDataLoading ? (
                    <ListSkeleton />
                ) : searchData && searchData?.users?.length > 0 ? (
                    <FlatList
                        data={searchData?.users}
                        style={{ flex: 1, marginTop: 10 }}
                        renderItem={renderItem}
                        onEndReachedThreshold={0.7}
                        keyExtractor={(item) => item.id}
                        refreshControl={
                            <RefreshControl
                                refreshing={refreshing}
                                onRefresh={handleRefresh}
                                colors={[Colors.dark.brandOrange]}
                            />
                        }
                    />
                ) : (
                    <View style={styles.noDataContainer}>
                        <ThemedText style={styles.defaultText}>
                            No users found
                        </ThemedText>
                    </View>
                )
            }
        </View>
    )
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        paddingHorizontal: paddingHorizontal,
        marginTop: 10
    },
    noDataContainer: {
        ...globalStyles.centerAligned,
        height: '100%',
    },
    defaultText: {
        fontSize: 15,
        marginHorizontal: "auto",
        marginVertical: 20,
        fontFamily: "InterSemiBold",
        fontWeight: "700",
    },
})