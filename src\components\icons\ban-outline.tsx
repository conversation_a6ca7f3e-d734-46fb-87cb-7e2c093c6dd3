import { Svg, Path, Circle, SvgProps } from "react-native-svg";

export const BanOutlineIcon = (props: SvgProps) => (
  <Svg viewBox="0 0 512 512" {...props}>
    <Circle
      cx={256}
      cy={256}
      r={208}
      fill="none"
      stroke={props.stroke || "#B0B0B0"}
      strokeMiterlimit={10}
      strokeWidth={32}
    />
    <Path
      fill="none"
      stroke={props.stroke || "#B0B0B0"}
      strokeMiterlimit={10}
      strokeWidth={32}
      d="m108.92 108.92 294.16 294.16"
    />
  </Svg>
);
