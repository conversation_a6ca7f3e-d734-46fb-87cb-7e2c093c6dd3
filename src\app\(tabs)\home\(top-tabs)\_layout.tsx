import { AddOutlineIcon } from '@/components/icons';
import { Colors } from '@/constants/Colors';
import {
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  createMaterialTopTabNavigator,
} from '@react-navigation/material-top-tabs';
import { ParamListBase, TabNavigationState } from '@react-navigation/native';
import { Link, withLayoutContext } from 'expo-router';
import { StyleSheet, TouchableOpacity } from 'react-native';

const { Navigator } = createMaterialTopTabNavigator();

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

export default function HomeTabLayout() {
  return (
    <>
      <MaterialTopTabs
        screenOptions={{
          tabBarContentContainerStyle: styles.tabBarContentContainerStyle,
          tabBarStyle: styles.tabBarStyle,
          tabBarItemStyle: styles.tabBarItemStyle,
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarIndicatorContainerStyle: styles.tabBarIndicatorContainerStyle,
          tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
        }}
      >
        <MaterialTopTabs.Screen name="index" options={{ title: 'Following' }} />
        <MaterialTopTabs.Screen
          name="trending-timeline"
          options={{ title: 'Trending' }}
        />
      </MaterialTopTabs>
      <Link href="/(modals)/addPost" asChild>
        <TouchableOpacity style={styles.floatingButton}>
          <AddOutlineIcon
            fill={Colors.dark.offWhite}
            color={Colors.dark.offWhite}
            height={28}
            width={28}
          />
        </TouchableOpacity>
      </Link>
    </>
  );
}

const styles = StyleSheet.create({
  tabBarContentContainerStyle: {},
  tabBarStyle: {
    backgroundColor: 'transparent',
  },
  tabBarItemStyle: {
    padding: 0,
  },
  tabBarLabelStyle: {
    fontSize: 13,
    textTransform: 'none',
  },
  tabBarIndicatorContainerStyle: {},
  tabBarIndicatorStyle: {
    backgroundColor: Colors.dark.primary,
    height: 1,
  },
  floatingButton: {
    width: 52,
    height: 52,
    borderRadius: 100,
    backgroundColor: '#EB540A',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 15,
    right: 15,
    zIndex: 1,
  },
});
