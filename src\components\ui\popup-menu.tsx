import React, { useState, useRef, useEffect, useMemo } from "react";
import {
    View,
    StyleSheet,
    TouchableOpacity,
    StatusBar,
    StyleProp,
    ViewStyle,
} from "react-native";
import { Portal } from "@gorhom/portal";
import useKeyboardHeight from "@/hooks/use-keyboard-height";
import { isIos, screenHeight, screenWidth } from "@/constants/Device";

type TMenuProps = {
    trigger: React.ReactNode,
    popup1?: React.ReactNode,
    popup2?: React.ReactNode,
    alignModal?: "right" | "left",
    popup1Styles?: StyleProp<ViewStyle>,
    popup2Styles?: StyleProp<ViewStyle>,
    isMenuOpen?: boolean,
    setIsMenuOpen?: React.Dispatch<React.SetStateAction<boolean>>
}

const OFFSET = 20;

//https://hartaniyassir.medium.com/how-to-create-a-popup-menu-in-react-native-d2fc8908e932
export const Menu = (
    { 
        trigger, 
        popup1, 
        popup2, 
        alignModal = "right", 
        popup1Styles, 
        popup2Styles, 
        isMenuOpen, 
        setIsMenuOpen 
    }: TMenuProps) => {
    const triggerWrapperRef = useRef<TouchableOpacity>(null);
    const popup1Ref = useRef<View>(null);
    const popup2Ref = useRef<View>(null);
    // states to hold the trigger and modal dimensions
    const [triggerDimensions, setTriggerDimensions] = useState({
        top: 0,
        left: 0,
        width: 0,
        height: 0,
    });

    const [popup1Dimensions, setPopup1Dimensions] = useState({
        width: 0,
        height: 0,
    });

    const [popup2Dimensions, setPopup2Dimensions] = useState({
        width: 0,
        height: 0,
    });

    const { keyboardHeight } = useKeyboardHeight()


    const styles = StyleSheet.create({
        modalWrapper: {
            ...StyleSheet.absoluteFillObject,
            zIndex: 10,
            backgroundColor: "#000000B3",
        },
        activeSection: {
            alignSelf: "flex-start",
            zIndex: 99,
        }
    });

    const closeModal = () => {
        setIsMenuOpen?.(false);
    };

    const calculateDimensions = () => {
        triggerWrapperRef?.current?.measureInWindow((x, y, width, height) => {
            setTriggerDimensions({
                top: Math.max(y, 0),
                left: x,
                width,
                height,
            });
        });

        setTimeout(() => {
            popup1Ref?.current?.measureInWindow((x, y, width, height) => {
                setPopup1Dimensions({ width, height });
            });
            popup2Ref?.current?.measureInWindow((x, y, width, height) => {
                setPopup2Dimensions({ width, height });
            });
        }, 200);
    };

    // run the calculateDimensions each time the menu is visible
    useEffect(() => {
        if (isMenuOpen) {
            if (triggerWrapperRef?.current) calculateDimensions();
        }
    }, [isMenuOpen]);

    const { top } = useMemo(() => {
        const totalHeight = popup1Dimensions.height + popup2Dimensions.height + OFFSET / 2;
        let top = 0;
        if (isIos) {
            const initialTriggerTop =
                triggerDimensions.top + triggerDimensions.height + 10;
            if (
                totalHeight + initialTriggerTop >
                screenHeight - keyboardHeight
            )
                top = triggerDimensions.top - totalHeight - 10;
            else top = initialTriggerTop;
        } else {
            const initialTriggerTop =
                triggerDimensions.top +
                triggerDimensions.height +
                (StatusBar.currentHeight ?? 0);

            top =
                initialTriggerTop + totalHeight >
                    screenHeight - keyboardHeight
                    ? initialTriggerTop -
                    triggerDimensions.height -
                    totalHeight
                    : initialTriggerTop;
        }

        return { top };
    }, [popup1Dimensions, popup2Dimensions, triggerDimensions, keyboardHeight, alignModal]);

    const { left: popup1Left } = useMemo(() => {
        let left = alignModal === "right" ? screenWidth - (Math.max(popup1Dimensions.width, popup2Dimensions.width)) - OFFSET : OFFSET;
        return { left };
    }, [popup1Dimensions, popup2Dimensions, triggerDimensions, keyboardHeight, alignModal]);

    const { left: popup2Left } = useMemo(() => {
        let left = alignModal === "right" ? screenWidth - (Math.min(popup1Dimensions.width, popup2Dimensions.width)) - OFFSET : OFFSET;
        // left =
        //     triggerDimensions.left - modalDimensions.width + triggerDimensions.width;
        // // if the popup is outside the screen from the left
        // if (triggerDimensions.left - modalDimensions.width < 0)
        //     left = triggerDimensions.left;
        return { left };
    }, [popup1Dimensions, popup2Dimensions, triggerDimensions, keyboardHeight, alignModal]);

    const popup1PositionStyles = { left: popup1Left, top }
    const popup2PositionStyles = { left: popup2Left, top }

    return (
        <>
            <TouchableOpacity
                activeOpacity={0.7}
                onLongPress={() => {
                    setIsMenuOpen?.(true);
                }}
                ref={triggerWrapperRef}
                style={{
                    paddingVertical: 5,
                    paddingHorizontal: 16
                }}
            >
                {trigger}
            </TouchableOpacity>
            {isMenuOpen && (popup1 || popup2) && (
                <Portal hostName="menu">
                    <TouchableOpacity
                        activeOpacity={1}
                        onPress={closeModal}
                        style={styles.modalWrapper}
                    >
                        {
                            popup1 ? (
                                <View
                                    ref={popup1Ref}
                                    style={[
                                        styles.activeSection,
                                        popup1Styles,
                                        popup1PositionStyles,
                                        { opacity: popup1Dimensions.width !== 0 && triggerDimensions.left !== 0 ? 1 : 0, }]}
                                    // for android as the ref may not return the item position
                                    collapsable={false}
                                >
                                    {/* pass the closeModal to children prop  */}
                                    {/* {Array.isArray(children)
                                ? children.map((childrenItem, index) => {
                                    return React.cloneElement(childrenItem, {
                                        key: index,
                                        closeModal,
                                    });
                                })
                                : React.cloneElement(children, {
                                    key: '123',
                                    closeModal,
                                })} */}
                                    {popup1}
                                </View>
                            ) : null
                        }
                        {
                            popup2 ? (
                                <View
                                    ref={popup2Ref}
                                    style={[
                                        styles.activeSection,
                                        popup2Styles,
                                        popup2PositionStyles,
                                        { opacity: popup2Dimensions.width !== 0 && triggerDimensions.left !== 0 ? 1 : 0, marginTop: OFFSET / 2 }]}
                                    // for android as the ref may not return the item position
                                    collapsable={false}
                                >
                                    {popup2}
                                </View>
                            ) : null
                        }
                    </TouchableOpacity>
                </Portal>
            )}
        </>
    );
};

export const MenuItem = (
    {
        children,
        onSelect,
        style
    }: {
        children: React.ReactNode,
        onSelect: () => void,
        style: StyleProp<ViewStyle>
    }) => {

    const handleOnPress = () => {
        onSelect();
    };

    return (
        <TouchableOpacity style={style} onPress={handleOnPress}>
            {children}
        </TouchableOpacity>
    );
};