import React, { type ComponentProps } from "react";
import { Colors } from "@/constants/Colors";
import {
  ColorValue,
  LayoutChangeEvent,
  StyleProp,
  StyleSheet,
  Text,
  TextInput,
  TextStyle,
  View,
  ViewStyle,
} from "react-native";
import { useState } from "react";

interface InputWithLabelProps {
  containerStyle?: StyleProp<ViewStyle>;
  labelStyle?: StyleProp<TextStyle>;
  inputStyle?: StyleProp<TextStyle>;
  iconStyle?: StyleProp<ViewStyle>;
  placeHolderColor?: ColorValue;
  labelText?: React.ReactNode | string;
  placeholder?: string;
  value?: string;
  onChangeText?: (text: string) => void;
  IconLeft?: React.ReactElement;
  IconRight?: React.ReactElement;
  editable?: boolean;
  iconColor?: string;
  maxLength?: number;
  multiline?: boolean;
  onBlur?: () => void;
  errorMessage?: string;
}

export const CustomTextInput: React.FC<InputWithLabelProps> = ({
  containerStyle,
  labelStyle,
  inputStyle,
  labelText,
  placeholder,
  placeHolderColor = Colors.dark.grey,
  value,
  onChangeText,
  IconLeft,
  IconRight,
  editable,
  iconColor,
  iconStyle,
  maxLength,
  multiline,
  onBlur,
  errorMessage,
}) => {
  return (
    <View style={containerStyle}>
      {labelText && <Text style={[styles.label, labelStyle]}>{labelText}</Text>}
      <View style={[{ flexDirection: 'row', alignItems: 'center', position: 'relative' }]}>
        {IconLeft && (
          <View style={[styles.iconLeft, styles.icon, iconStyle, { alignSelf: 'center' }]}>
            {IconLeft}
          </View>
        )}
        <TextInput
          style={[
            styles.input,
            inputStyle,
            IconLeft && { paddingLeft: 50 },
            IconRight && { paddingRight: 50 },
            errorMessage ? { borderColor: "#D14848" } : null,
            { flex: 1 },
          ]}
          placeholder={placeholder}
          placeholderTextColor={placeHolderColor}
          value={value}
          onChangeText={onChangeText}
          editable={editable}
          selectTextOnFocus={editable}
          maxLength={maxLength}
          multiline={multiline}
          onBlur={onBlur}
        />
        {IconRight && (
          <View style={[styles.iconRight, styles.icon, { alignSelf: 'center' }]}>
            {IconRight}
          </View>
        )}
      </View>
      {errorMessage && (
        <Text className="text-xs text-[#D14848] mt-1">{errorMessage}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  label: {
    fontWeight: "300",
    marginBottom: 7,
    color: Colors.dark.grey,
    fontSize: 12,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    paddingVertical: 8,
    paddingHorizontal: 10,
    borderRadius: 5,
    height: 45,
    color: "white",
    width: "100%",
  },
  icon: {
    position: "absolute",
    zIndex: 1,
  },
  iconLeft: {
    left: 20,
  },
  iconRight: {
    right: 10,
  },
});
