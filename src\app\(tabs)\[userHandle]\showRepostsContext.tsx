import React, { createContext, useContext, useState, ReactNode } from "react";

interface ShowRepostsContextType {
  showReposts: boolean;
  setShowReposts: (v: boolean) => void;
}

const ShowRepostsContext = createContext<ShowRepostsContextType | undefined>(undefined);

export const ShowRepostsProvider = ({ children }: { children: ReactNode }) => {
  const [showReposts, setShowReposts] = useState(true);
  return (
    <ShowRepostsContext.Provider value={{ showReposts, setShowReposts }}>
      {children}
    </ShowRepostsContext.Provider>
  );
};

export const useShowReposts = () => {
  const ctx = useContext(ShowRepostsContext);
  if (!ctx) throw new Error("useShowReposts must be used within ShowRepostsProvider");
  return ctx;
}; 

