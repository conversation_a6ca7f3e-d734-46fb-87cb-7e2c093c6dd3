import { Colors } from "@/constants/Colors";

export const cleanMessage = (message: string, replacementChar: string = " ") => {
    const _message = (message || "").replace(/<br>/g, replacementChar); // replace <br> with space
    return _message.replace(/<\/?[^>]+(>|$)/g, ""); // remove any other HTML tags
};

export const removeParagraphTags = (text: string) => {
    return text.replace(/<p>/g, "").replace(/<\/p>/g, "");
}

const DEFAULT_COLOR_LIST = [
    "#D53535",
    Colors.dark.brandOrange,
    "#B6B840",
    "#40B877",
    "#40B8B8",
    "#0059E0",
    "#8340B8",
    "#B84081",
];

export const stringToColor = (str: string, colorList: string[] = DEFAULT_COLOR_LIST) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    const index = Math.abs(hash) % colorList.length;
    return colorList[index];
}

export const getUnicodeFromEmoji = (emoji: string) => {
    return [...emoji].map(char => char.codePointAt(0)?.toString(16)).join('-');
};

export const getEmojiFromUnicode = (unicode: string) => {
    return String.fromCodePoint(parseInt(unicode, 16));
};

export const isAnimatedGif = (uri: string) => {
    return uri.includes("https://media.tenor.com");
}

export const removeCommas = (value: string) => {
    return value.replace(/,/g, "");
}