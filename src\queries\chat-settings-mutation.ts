import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { setSettings, SetSettingsRequest } from "@/api/client/chat";

type ChatSettingsMutation = MutationOptions<
  unknown,
  DefaultError,
  SetSettingsRequest,
  any
>;

export const useChatSettingsMutation = (options?: ChatSettingsMutation) => {
  return useMutation({
    mutationFn: setSettings,
    ...options,
  });
};
