import { TabBarIcon } from "@/components/navigation/TabBarIcon";
import { ThemedText } from "@/components/ThemedText";
import { Button } from "@/components/ui/button";
import Dropdown from "@/components/ui/select";
import { Colors } from "@/constants/Colors";
import { tokens } from "@/environments/tokens";
import { LinearGradient } from "expo-linear-gradient";
import { Image, ImageSourcePropType, StyleSheet, View } from "react-native";

interface HeaderCardProps {
  type: string;
  address: string;
  balance: string;
  coin: string;
  coinImage: ImageSourcePropType;
}

export const WalletProfileHeaderCards = ({
  type,
  address,
  balance,
  coin,
  coinImage,
}: HeaderCardProps) => {
  return (
    <LinearGradient
      colors={
        type === "Primary"
          ? ["#D64C05", "#FF5626"]
          : [Colors.dark.grayBg, Colors.dark.grayBg]
      }
      style={styles.cardContainer}
    >
      {type === "Primary" ? (
        <Image
          style={styles.posAbsLogo}
          source={require("@assets/images/logo-blur-partial.png")}
        />
      ) : null}
      <View style={styles.cardHeaderWrapper}>
        <View style={styles.cardHeaderLeftChild}>
          <View style={styles.coinLogoContainer}>
            <Image style={styles.coinLogo} source={coinImage} />
          </View>
          <ThemedText
            style={styles.ellipsisText}
            numberOfLines={1}
            ellipsizeMode="middle"
          >
            {address}
          </ThemedText>
          <TabBarIcon
            name="copy-outline"
            size={15}
            color={Colors.dark.offWhite}
          />
        </View>
        <View style={styles.cardHeaderRightChild}>
          {type === "Primary" ? (
            <Button variant="outline" style={styles.headerButton} >CLAIM</Button>
          ) : (
            <Dropdown fullWidth={true} style={styles.dropDownStyle} textStyle={styles.dropDownPlaceholderText} data={tokens} placeholderImage={coinImage} onChange={() => {}} placeholder={coin} />
          )}
        </View>
      </View>
      <View style={styles.cardBody}>
        <ThemedText type='lightGreySubtitle'>Balance</ThemedText>
        <ThemedText type="subtitle">
          {balance} {type === "Primary" ? "$ARENA" : coin}
        </ThemedText>
        {type === "Primary" ? (
          <ThemedText style={styles.dollarValue}>$60,250 USD</ThemedText>
        ) : null}
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    borderRadius: 10,
    padding: 8,
    width: 300,
    position: "relative",
    gap: 20,
    justifyContent: "space-between",
  },
  cardHeaderWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  cardHeaderLeftChild: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  coinLogoContainer: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 7 },
    shadowOpacity: 0.34,
    shadowRadius: 6.37,
    elevation: 10, // Required for Android
  },
  coinLogo: {
    height: 20,
    aspectRatio: 1,
  },
  ellipsisText: {
    maxWidth: 60,
    fontSize: 11,
  },
  cardHeaderRightChild: {},
  headerButton: {
    backgroundColor: "transparent",
    borderRadius: 30,
    borderWidth: 0.5,
    borderColor: Colors.dark.offWhite,
    paddingVertical: 4,
    paddingHorizontal: 10,
  },
  dollarValue: {
    fontSize: 13,
    fontWeight: "600",
  },
  cardBody: {
    gap: 2,
  },
  posAbsLogo: {
    position: "absolute",
    right: 0,
  },
  dropDownStyle: {
    width: 120,
    height: 30,
    borderRadius: 20
  },
  dropDownPlaceholderText: {
    fontSize: 12
  }
});
