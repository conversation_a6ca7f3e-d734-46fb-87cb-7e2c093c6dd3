import React from "react";
import { Href, router } from "expo-router";
import { EllipsisHorizontalFilledIcon } from "@/components/icons";
import BackButton from "@/components/navigation/BackButton";
import { Colors } from "@/constants/Colors";
import { View, TouchableOpacity, Share } from "react-native";
import { useBottomSheet } from "@/components/ui/BottomSheetContext";
import ProfileMenuBottomSheet from "@/app/(tabs)/[userHandle]/_components/profile-menu-bottom-sheet";
import { useUser } from "@/stores";
import { useUserByHandleQuery, useIsUserBlockedQuery } from "@/queries/user-queries";
import { useLocalSearchParams } from "expo-router";
import * as Clipboard from "expo-clipboard";
import { toast } from "@/components/toast";
import { useShowReposts } from "../showRepostsContext";
import { ReportModal } from "../report/index";
import { useState } from "react";
import BlockUnblockConfirmSheet from "./block-unblock-confirm-sheet";
import { useQueryClient } from "@tanstack/react-query";

export const ProfileFloatingHeader = () => {
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();
  const { user } = useUser();
  const params = useLocalSearchParams() as { userHandle: string };
  const { data } = useUserByHandleQuery(params.userHandle);
  const { data: isBlocked } = useIsUserBlockedQuery(data?.user?.id);
  const { showReposts, setShowReposts } = useShowReposts();
  const [reportOpen, setReportOpen] = useState(false);
  const [blockSheetOpen, setBlockSheetOpen] = useState(false);
  const [blockMode, setBlockMode] = useState<"block" | "unblock">("block");
  const queryClient = useQueryClient();

  const isMe = user && data && user.id === data.user?.id;
  
  const handleCreatorFeeSettings = () => {
    closeBottomSheet();
    router.push(`/${params.userHandle}/fee-settings` as Href<string>);
  };

  const handleHideReposts = () => {
    if (showReposts) {
      setShowReposts(false);
    } else {
      setShowReposts(true);
    }
    closeBottomSheet();
  };

  const handleShareProfile = async () => {
    if (!data?.user?.twitterHandle) return;
    const url = `https://arena.social/${data.user.twitterHandle}`;
    try {
      await Share.share({ message: `Check out my profile on Arena: ${url}` });
    } catch (e) {
      toast.danger("Failed to share profile");
    }
    closeBottomSheet();
  };
  const handleBlockUnblock = () => {
    closeBottomSheet();
    setBlockMode(isBlocked ? "unblock" : "block");
    setBlockSheetOpen(true);
  };

  const handleCopyLink = async () => {
    if (!data?.user?.twitterHandle) return;
    const url = `https://arena.social/${data.user.twitterHandle}`;
    await Clipboard.setStringAsync(url);
    toast.green("Copied to clipboard");
    closeBottomSheet();
  };

  const handleReport = () => {
    closeBottomSheet();
    setReportOpen(true);
  };

  const handleMenuPress = () => {
    openBottomSheet(
      <ProfileMenuBottomSheet
        isMe={!!isMe}
        onCreatorFeeSettings={handleCreatorFeeSettings}
        onHideReposts={handleHideReposts}
        onShareProfile={handleShareProfile}
        onReport={handleReport}
        onBlock={handleBlockUnblock}
        onCopyLink={handleCopyLink}
        showReposts={showReposts}
        isBlocked={!!isBlocked}
      />,
      isMe ? 200 : 230
    );
  };

  return (
    <>
      <View className="absolute flex-row justify-between px-5 py-5 w-full">
        <View className="bg-dark-bk rounded-full p-1">
          <BackButton />
        </View>
        <View className="bg-dark-bk rounded-full p-1">
          <TouchableOpacity onPress={handleMenuPress}>
            <EllipsisHorizontalFilledIcon height={20} width={20} fill={Colors.dark.offWhite} />
          </TouchableOpacity>
        </View>
      </View>
      {data?.user && (
        <ReportModal open={reportOpen} setOpen={setReportOpen} user={data.user} />
      )}
      {data?.user && data.user.id && (
        <BlockUnblockConfirmSheet
          open={blockSheetOpen}
          mode={blockMode}
          username={data.user.twitterHandle}
          userId={data.user.id}
          onClose={() => setBlockSheetOpen(false)}
        />
      )}
    </>
  );
};

