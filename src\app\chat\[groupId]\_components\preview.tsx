import { ActivityIndicator, StyleSheet, TouchableOpacity } from "react-native"
import { Colors } from "@/constants/Colors"
import { View } from "react-native"
import { memo, useEffect } from "react"
import { XCircleOutlineIcon } from "@/components/icons"
import { useGroup } from "../_contexts/group-context"
import Animated, { runOnUI, useSharedValue, withTiming, useAnimatedStyle, Easing, cancelAnimation } from "react-native-reanimated"
import { ResizeMode } from "expo-av"
import { Video } from "expo-av"
import { BORDER_RADIUS } from "./message"
import { Image } from 'expo-image';
import { isAnimatedGif } from "@/utils"

type PreviewProps = {
    uri: string;
    type: "image" | "video";
    progress: number;
    loading: boolean;
}

const Preview = ({ uri, type, progress, loading }: PreviewProps) => {

    const { setPreview } = useGroup();

    const onClose = () => {
        setPreview?.(null);
    }

    const progressValue = useSharedValue(progress);

    useEffect(() => {
        return () => {
            cancelAnimation(progressValue);
        }
    }, []);

    useEffect(() => {
        const updateProgress = (progress: number) => {
            'worklet';
            progressValue.value = withTiming(progress, {
                duration: 500, // Duration for the animation
                easing: Easing.ease, // Easing function for the animation
            });
        }
        runOnUI(updateProgress)(progress);
        if(progress === 100){
            setTimeout(() => {
                progressValue.value = withTiming(0, {
                    duration: 0, // Duration for the animation
                    easing: Easing.ease, // Easing function for the animation
                });
            }, 1000);
        }
    }, [progress]);

    const progressBarStyle = useAnimatedStyle(() => ({
        width: `${progressValue.value}%`,
    }));

    return (
        <View style={styles.container}>
            {
                (type === "image" || type === "video") && uri ? (
                    <View style={styles.previewContainer}>
                        {
                            type === "image" ? (
                                <View style={styles.imageContainer}>
                                    <Image source={{ uri, isAnimated: isAnimatedGif(uri) }} style={styles.image} />
                                </View>
                            ) : type === "video" ? (
                                <Video
                                    source={{
                                        uri: uri,
                                    }}
                                    useNativeControls={false}
                                    resizeMode={ResizeMode.COVER}
                                    style={styles.image}
                                    isLooping
                                    shouldPlay
                                    isMuted
                                />
                            ) : null
                        }
                        <TouchableOpacity style={styles.closeIcon} onPress={onClose}>
                            <XCircleOutlineIcon width={20} height={20} color={Colors.dark.white} />
                        </TouchableOpacity>
                        <Animated.View
                            style={[styles.progressBar, progressBarStyle]}
                        />
                        {loading ? <View style={styles.loader}>
                            <ActivityIndicator size="small" color={Colors.dark.white} />
                        </View> : null}
                    </View>
                ) : null
            }
        </View>
    )
}

export default memo(Preview, (prevProps, nextProps) => {
    return prevProps.uri === nextProps.uri &&
        prevProps.type === nextProps.type &&
        prevProps.progress === nextProps.progress &&
        prevProps.loading === nextProps.loading
});

const styles = StyleSheet.create({
    container: {
        width: '100%',
        paddingHorizontal: 10,
        paddingVertical: 5,
    },
    closeIcon: {
        position: "absolute",
        top: 0,
        right: 0,
        zIndex: 1000,
        padding: 5
    },
    previewContainer: {
        width: 150,
        height: 150,
        position: "relative"
    },
    image: {
        width: '100%',
        height: '100%',
        borderRadius: BORDER_RADIUS
    },
    progressBar: {
        height: 2,
        backgroundColor: Colors.dark.brandOrange,
        position: "absolute",
        bottom: 0,
        left: 0,
    },
    loader: {
        position: "absolute",
        height: "100%",
        width: "100%",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "rgba(0, 0, 0, 0.5)"
    },
    imageContainer: {
        overflow: 'hidden', 
        width: '100%', 
        height: '100%', 
        borderRadius: BORDER_RADIUS            
    }
})