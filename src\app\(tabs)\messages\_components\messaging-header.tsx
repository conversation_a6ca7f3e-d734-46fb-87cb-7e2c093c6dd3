import { View, StyleSheet, TouchableOpacity } from "react-native";
import { useMessaging } from "../_contexts/messaging-context";
import MessageLandingHeader from "./messsage-landing-header";
import MessageSearch from "./message-search";
import { Colors } from "@/constants/Colors";
import { SettingsCircleOutlineIcon } from "@/components/icons";
import { Href, router } from "expo-router";
import { useMemo } from "react";
import { GroupSelectionHeader } from "./group-selection-header";

export const MessagingHeader = () => {
    const { selectedGroup, setSearchValue, searchValue } = useMessaging();

    const HeaderRight = useMemo(() => {
        return (
            <TouchableOpacity style={styles.settingsIconContainer} onPress={() => {
                router.push("/chat/message-settings" as Href<string>);
            }}>
                <SettingsCircleOutlineIcon
                    width={28}
                    height={28}
                />
            </TouchableOpacity>
        );
    }, []);

    return (
        <View style={styles.messageHeaderSearchWrap}>
            {
                selectedGroup ? (
                    <GroupSelectionHeader
                    />
                ) : (
                    <MessageLandingHeader
                        headerTitle="Messages"
                        HeaderRight={HeaderRight}
                    />
                )}
            <MessageSearch
                placeHolder="Search Messages"
                placeHolderColor={Colors.dark.grey}
                searchHandler={setSearchValue}
                value={searchValue}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    messageHeaderSearchWrap: {
        marginVertical: 10,
        paddingHorizontal: 25,
        gap: 20,
    },
    settingsIconContainer: {
        paddingHorizontal: 10,
        paddingVertical: 5
    }
});