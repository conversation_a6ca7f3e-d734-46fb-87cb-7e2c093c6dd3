import { useMemo, useState } from "react";

import { z } from "zod";
import { Modal, StyleSheet, TouchableOpacity, View, Text } from "react-native";
import { Controller, useForm } from "react-hook-form";

import { Thread } from "@/types";
import { useUser } from "@/stores";
import { toast } from "@/components/toast";
import { Colors } from "@/constants/Colors";
import Dropdown from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { formatEther, formatUnits } from "@/utils";
import { ThemedView } from "@components/ThemedView";
import { useTheme } from "@react-navigation/native";
import { ThemedText } from "@/components/ThemedText";
import { AVAX, tokens } from "@/environments/tokens";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { useTipMutation } from "@/queries/tip-mutation";
import { ArrowBackOutlineIcon, XCircleOutlineIcon } from "@/components/icons";
import { CustomTextInput } from "@/components/ui/text-input";
import { useSolanaAddressQuery } from "@/queries/chain-queries";
import {
  useBalancesQuery,
  useSolanaBalanceQuery,
} from "@/queries/balance-queries";
import ProfileHeaderCard from "@/app/(tabs)/[userHandle]/_components/profile-header-card";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";

interface TipModalProps {
  thread: Thread;
  onClose: () => void;
  visible: boolean;
  children: React.ReactNode;
}

const wallets = {
  ARENA: "The Arena",
  PHANTOM: "Phantom",
};

export const TipModal = ({
  thread,
  onClose,
  visible,
  children,
}: TipModalProps) => {
  const { colors, dark } = useTheme();

  let ColorScheme = dark ? "dark" : "light";

  const queryClient = useQueryClient();
  const [token, setToken] = useState<{
    name: string;
    icon: string;
    native?: boolean;
  }>(AVAX);
  const [wallet, setWallet] = useState(wallets.ARENA);
  const { user } = useUser();

  const { data: balances } = useBalancesQuery({
    address: user?.address,
  });
  const { data: solanaAddressData } = useSolanaAddressQuery();
  const { data: solanaBalanceData } = useSolanaBalanceQuery({
    address: solanaAddressData?.response.solanaAddress,
  });

  const provider =
    typeof window !== "undefined" && (window as any).phantom?.solana;

  const balance = useMemo(() => {
    return {
      AVAX: balances ? formatEther(balances.AVAX.toString()) ?? "0.00" : "0.00",
      COQ: balances ? formatEther(balances.COQ.toString()) ?? "0.00" : "0.00",
      GURS: balances ? formatEther(balances.GURS.toString()) ?? "0.00" : "0.00",
      NOCHILL: balances
        ? formatEther(balances.NOCHILL.toString()) ?? "0.00"
        : "0.00",
      MEAT: balances
        ? formatUnits(balances.MEAT.toString(), 6) ?? "0.00"
        : "0.00",
      KIMBO: balances
        ? formatEther(balances.KIMBO.toString()) ?? "0.00"
        : "0.00",
      JOE: balances ? formatEther(balances.JOE.toString()) ?? "0.00" : "0.00",
      TECH: balances ? formatEther(balances.TECH.toString()) ?? "0.00" : "0.00",
      SOL:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.SOL.toString() ?? "0.00"
          : "0.00",
      Bonk:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.Bonk?.toString() ?? "0.00"
          : "0.00",
      $WIF:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.$WIF?.toString() ?? "0.00"
          : "0.00",
      USEDCAR:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.USEDCAR?.toString() ?? "0.00"
          : "0.00",
      Moutai:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.Moutai?.toString() ?? "0.00"
          : "0.00",
      HARAMBE:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.HARAMBE?.toString() ?? "0.00"
          : "0.00",
    };
  }, [balances, solanaBalanceData, solanaAddressData]);

  const isSolanaCurrency = [
    "SOL",
    "Bonk",
    "$WIF",
    "USEDCAR",
    "Moutai",
    "HARAMBE",
  ].includes(token.name);

  const showWarning =
    isSolanaCurrency && !provider && wallet === wallets.PHANTOM;

  const TipInput = z.object({
    currency: z.string(),
    tipAmount: z
      .string()
      .min(1, {
        message: "Tip amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Tip amount must be a number",
      })
      .refine(
        (v) =>
          isSolanaCurrency
            ? true
            : parseFloat(v.replace(/,/g, "")) <=
              parseFloat(
                balance[token.name as keyof typeof balance].replace(/,/g, "")
              ),
        {
          message: "Insufficient balance",
        }
      ),
  });
  type TipInputType = z.infer<typeof TipInput>;

  const form = useForm<TipInputType>({
    defaultValues: {
      currency: AVAX.name,
      tipAmount: "",
    },
    resolver: zodResolver(TipInput),
    reValidateMode: "onChange",
  });

  const { mutateAsync: tip, isPending } = useTipMutation({
    onSuccess: () => {
      toast.green("Tip sent!");
      form.reset();
      setWallet(wallets.ARENA);
      setToken(AVAX);
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balances", user?.address],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "wallet",
          "solana_balance",
          solanaAddressData?.response.solanaAddress,
        ],
      });
    },
  });

  const onSubmit = async (values: TipInputType) => {
    if (!thread) return;
    const tipAmount = values.tipAmount.replace(/,/g, "");

    await tip({
      currency: values.currency,
      tipAmount,
      userId: thread?.user.id,
      threadId: thread.id,
      wallet: isSolanaCurrency ? wallet : wallets.ARENA,
    });
  };

  const defaultCoinValue = {
    name: "AVAX",
    icon: require("@assets/coins/avax.png"),
    native: true,
  };

  const defaultWalletValue = {
    name: wallets.ARENA,
  };

  const walletsDropdownData = [
    {
      name: wallets.ARENA,
    },
    {
      name: wallets.PHANTOM,
    },
  ];

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      {children}
      <SafeAreaProvider>
        <SafeAreaView style={[styles.modalContainer]}>
          <View className="bg-dark-bk" style={styles.container}>
            <View className="flex-row items-center w-full py-4">
              <TouchableOpacity onPress={onClose}>
                <ArrowBackOutlineIcon
                  height={20}
                  width={20}
                  className="text-off-white mr-4"
                />
              </TouchableOpacity>
            </View>

            <ThemedView>
              <ThemedText style={styles.headerText}>Tip</ThemedText>
              <ThemedText
                style={styles.fontFamily}
                className="text-[26px] font-semibold leading-[30px] text-white sm:hidden"
              >
                {thread.user?.twitterHandle}
              </ThemedText>
            </ThemedView>
            <ProfileHeaderCard showTwitterHandle={true} data={thread} />

            <Controller
              name="currency"
              control={form.control}
              render={({ field: { name, onChange, value } }) => {
                return (
                  <ThemedView>
                    <ThemedText style={styles.inputLabel}>
                      BALANCE: {balance[token.name as keyof typeof balance]}
                    </ThemedText>
                    <Dropdown
                      data={tokens}
                      onChange={(value) => {
                        setToken(
                          tokens.find((token) => token.name === value.name) ??
                            AVAX
                        );
                        onChange(value);
                      }}
                      value={value}
                      placeholder="AVAX"
                      style={styles.inputStyle}
                      defaultValue={defaultCoinValue}
                      balance={balance}
                    />
                  </ThemedView>
                );
              }}
            />
            {isSolanaCurrency && (
              <ThemedView>
                <ThemedText style={styles.inputLabel}>Wallet</ThemedText>
                <Dropdown
                  data={walletsDropdownData}
                  onChange={(e) => {
                    setWallet(e.name);
                  }}
                  placeholder="AVAX"
                  style={styles.inputStyle}
                  defaultValue={defaultWalletValue}
                />
              </ThemedView>
            )}
            <ThemedView>
              <Controller
                name="tipAmount"
                control={form.control}
                render={({ field: { name, onChange, value, onBlur, ref } }) => {
                  return (
                    <CustomTextInput
                      labelText="TIP AMOUNT"
                      placeholder="How much do you want to tip?"
                      value={value}
                      onChangeText={(text) => {
                        let value: string | number = text;
                        if (value === "") {
                          onChange(value);
                          return;
                        }

                        if (!value.includes(".")) {
                          value = parseFloat(value.replace(/,/g, ""));
                          if (isNaN(value)) return;
                          value = numberFormatter.format(value);
                        }

                        onChange(value);
                      }}
                      onBlur={onBlur}
                      errorMessage={form.formState.errors.tipAmount?.message}
                    />
                  );
                }}
              />
            </ThemedView>
            {!showWarning && (
              <Button
                onPress={form.handleSubmit(onSubmit)}
                >
                Send tip
              </Button>
            )}
            {showWarning && (
              <View className="flex w-full items-center gap-[10px] rounded-lg border border-[#BE5D5D] bg-[#D14848] px-4 py-3">
                <XCircleOutlineIcon
                  height={6}
                  width={6}
                  fill={Colors.dark.offWhite}
                />
                <Text style={{ fontSize: 12, lineHeight: 20, color: Colors.dark.offWhite }}>
                  No Phantom wallet detected. Please install the Phantom wallet
                  extension or use Phantom wallet browser on mobile
                </Text>
              </View>
            )}
          </View>
        </SafeAreaView>
      </SafeAreaProvider>
    </Modal>
  );
};

const numberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 20,
});

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  container: {
    paddingHorizontal: 20,
    height: "100%",
    gap: 24,
  },

  headerText: {
    color: Colors.dark.lightgrey,
    fontSize: 17,
    marginTop: 70,
  },
  inputLabel: {
    color: Colors.dark.lightgrey,
    fontSize: 12,
    marginBottom: 10,
  },
  inputStyle: {
    borderColor: Colors.dark.brandOrange,
  },
  fontFamily: {
    fontFamily: "InterSemibold",
  },
});
