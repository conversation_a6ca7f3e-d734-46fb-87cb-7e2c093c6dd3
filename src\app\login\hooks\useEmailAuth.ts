import { loginEmail } from '@/api/client/login';
import { toast } from '@/components/toast';
import { getCookie } from '@/cookies/secure-store';
import { navigationRoutes } from '@/navigationRoutes';
import { useUser } from '@/stores';
import { useQuery } from '@tanstack/react-query';
import { router } from 'expo-router';
import { useEffect } from 'react';

export const useEmailAuth = (
  emailToken: string | null,
  closeBottomSheet: () => void,
) => {
  const { setUser, setTwitterUser, setToken } = useUser();

  const { isError, isSuccess, error } = useQuery({
    queryKey: ['login', 'email'],
    queryFn: () => loginEmail({ token: emailToken ?? '', ref: null }),
    enabled: !!emailToken,
  });

  useEffect(() => {
    async function populateUserData() {
      const tokenCookie = await getCookie('token');
      setToken(tokenCookie ? tokenCookie.value : null);

      const userCookie = await getCookie('user');
      setUser(userCookie ? JSON.parse(userCookie.value || '{}') : null);

      const twitterUserCookie = await getCookie('twitterUser');
      setTwitterUser(
        twitterUserCookie ? JSON.parse(twitterUserCookie.value || '{}') : null,
      );
      closeBottomSheet();
    }

    if (isSuccess && emailToken) {
      populateUserData();
      router.push(navigationRoutes.home);
    }
  }, [
    isSuccess,
    emailToken,
    router,
    closeBottomSheet,
    setToken,
    setUser,
    setTwitterUser,
  ]);

  useEffect(() => {
    if (isError && emailToken) {
      console.error('error', error);
      toast.danger('Something went wrong with email login!');
    }
  }, [isError, error, emailToken]);

  return {
    isError,
    isSuccess,
    error,
    emailToken,
  };
};
