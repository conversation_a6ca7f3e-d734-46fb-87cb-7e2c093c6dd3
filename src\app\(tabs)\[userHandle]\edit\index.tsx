import { BannerPicture } from "./_components/banner-picture";
import { ProfileForm } from "./_components/profile-form";
import { getCookie } from "@/cookies/secure-store";
import { Href, router, useLocalSearchParams } from "expo-router";

function EditProfilePage() {
  const params = useLocalSearchParams() as { userHandle: string };
  
  getCookie("user").then((userCookie) => {
    const user = userCookie ? JSON.parse(userCookie.value || "{}") : null;

    if (user && user.twitterHandle !== params.userHandle) {
      return router.replace(`/${params.userHandle}` as Href<string>);
    }
  });

  return (
    <>
      <BannerPicture />
      <ProfileForm />
    </>
  );
}

export default EditProfilePage;
