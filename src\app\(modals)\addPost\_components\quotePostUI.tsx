import { View, Text, Image } from "react-native";
import { parseISO } from "date-fns";
import { Video, ResizeMode } from "expo-av";
import { WebView } from "react-native-webview";
import { Href, Link } from "expo-router";

import { checkContent, formatTimeDistance } from "@/utils";
import { Thread, UserFlaggedEnum } from "@/types";

import { useIsBlockedByUserQuery, useIsUserBlockedQuery } from "@/queries";

import Avatar from "@/components/ui/avatar";
import { ThemedView } from "@/components/ThemedView";
import CustomRenderHtml from "@/components/custom-render-html";

export const QuotePost = (props: Thread) => {

  const {
    userId,
    userName,
    userHandle,
    createdDate,
    content,
    images,
    videos,
    user,
    threadType,
    stage,
    isDeleted,
  } = props;

  const date = parseISO(createdDate);

  const [finalContent, isTruncated, youtubeEmbedUrl] = checkContent({
    content,
    characterLimit: 205,
  });

  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(userId);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(userId);

  const isSuspended = user?.flag === UserFlaggedEnum.SUSPENDED;

  // TODO: add terms of use screen
  return (
    <View className="w-full rounded-lg border border-dark-gray bg-dark-bk p-3">
      {isDeleted && (
        <View className="flex flex-col text-sm">
          <Text className="text-gray-500">
            Post unavailable. This post has violated The Arena's{" "}
            <Link href={'/terms-of-use' as Href<string>} className="font-semibold text-white">
              terms of use.
            </Link>
          </Text>
        </View>
      )}
      {isSuspended && !isDeleted && (
        <View className="flex flex-col text-sm">
          <Text className="text-gray-500">
            Post unavailable. The owner was suspended for violating{" "}
            <Link href={'/terms-of-use' as Href<string>} className="font-semibold text-white">
              terms of use.
            </Link>
          </Text>
        </View>
      )}
      {isUserBlocked && !isSuspended && !isDeleted && !isUserBlockedLoading && (
        <Text className="text-sm text-gray-text">
          This post is from a blocked user account
        </Text>
      )}

      {isBlockedByUser &&
        !isSuspended &&
        !isDeleted &&
        !isBlockedByUserLoading && (
          <Text className="text-sm text-gray-text">
            You&apos;re unable to view this Post because this account owner
            limits who can view their Posts
          </Text>
        )}

      {!(isUserBlocked && !isUserBlockedLoading) &&
        !(isBlockedByUser && !isBlockedByUserLoading) && (
          <View className="flex flex-col gap-2">
            <View className="flex flex-row items-center">
              <Link
                href={`/${user?.twitterHandle}` as Href<string>}
                className="mr-2 flex-shrink-0"
              >
                <Avatar src={user?.twitterPicture} size={17} />
              </Link>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                className="text-sm font-semibold text-off-white flex-shrink"
              >
                {userName}
              </Text>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                className="text-sm text-gray-text flex-shrink"
              >
                ・@{userHandle}
              </Text>
              <Text className="text-sm text-gray-text flex-shrink-0">
                ・{formatTimeDistance(date)}
              </Text>
            </View>

            <View className="flex flex-col text-sm">
              <ThemedView className="w-full">
                <CustomRenderHtml html={finalContent} />
              </ThemedView>
              {isTruncated && (
                <Text className="text-brand-orange">Show more</Text>
              )}
            </View>

            {images && images.length > 0 && (
              <Image
                source={{ uri: images[0].url }}
                className="rounded"
                resizeMode="cover"
                height={180}
              />
            )}

            {videos && videos.length > 0 && (
              <Video
                source={{
                  uri: videos[0].url,
                }}
                className="rounded"
                useNativeControls={true}
                resizeMode={ResizeMode.COVER}
                style={{ height: 180 }}
                isLooping
                shouldPlay
                isMuted
              />
            )}

            {youtubeEmbedUrl && (
              <View className="aspect-video overflow-hidden rounded">
                <WebView
                  source={{ uri: youtubeEmbedUrl }}
                  javaScriptEnabled={true}
                  domStorageEnabled={true}
                />
              </View>
            )}
          </View>
        )}
    </View>
  );
};
