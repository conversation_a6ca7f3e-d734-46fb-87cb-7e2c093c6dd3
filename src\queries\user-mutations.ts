import {
    DefaultError,
    MutationOptions,
    useMutation,
  } from "@tanstack/react-query";
  
  import {
    blockUser,
    unblockUser,
  } from "@/api/client/user";
  
 
  type BlockUserMutationType = MutationOptions<
    unknown,
    DefaultError,
    {
      userId: string;
    },
    any
  >;
  
  export const useBlockUserMutation = (options?: BlockUserMutationType) => {
    return useMutation({
      mutationFn: blockUser,
      ...options,
    });
  };
  
  type UnblockUserMutationType = MutationOptions<
    unknown,
    DefaultError,
    {
      userId: string;
    },
    any
  >;
  
  export const useUnblockUserMutation = (options?: UnblockUserMutationType) => {
    return useMutation({
      mutationFn: unblockUser,
      ...options,
    });
  };
 