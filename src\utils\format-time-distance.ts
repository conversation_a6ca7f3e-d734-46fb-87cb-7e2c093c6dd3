import { compareDesc, format, formatDistanceToNowStrict, isThisWeek, isThisYear, isToday, isYesterday, sub } from "date-fns";

export function formatTimeDistance(date: Date | number | string) {
  if (!date) return "";

  if (typeof date === "string" && !isNaN(+date)) {
    date = +date;
  }

  if (!(date instanceof Date)) {
    date = new Date(date);
  }

  const now = new Date();
  const weekAgo = sub(now, {
    days: 7,
  });

  const comparedToLastWeek = compareDesc(weekAgo, date);

  // format distance to now if it's within the last week
  if (comparedToLastWeek === 1) {
    return formatDistanceToNowStrict(date)
      .replace("hours", "hrs")
      .replace("hour", "hr")
      .replace("minutes", "mins")
      .replace("minute", "min")
      .replace("seconds", "secs")
      .replace("second", "sec");
  }

  // otherwise, format the date
  return format(date, "MM/dd/yy");
}

export function formatChatTimeDistance(date: Date | number | string) {
  if (!date) return "";
  if (typeof date === "string" && !isNaN(+date)) {
    date = +date;
  }

  if (!(date instanceof Date)) {
    if (typeof date === "string") {
      const [month, day, year] = date.split('/').map(Number);
      date = new Date(year, month - 1, day); // Note: Month is 0-based in JavaScript Date
    } else {
      date = new Date(date);
    }
  }
  if (isToday(date)) {
    return "Today";
  }

  if (isYesterday(date)) {
    return "Yesterday";
  }

  if (isThisWeek(date)) {
    return format(date, "EEEE");
  }

  if (isThisYear(date)) {
    return format(date, "MMMM d");
  }

  return format(date, "MMMM d, yyyy");
}