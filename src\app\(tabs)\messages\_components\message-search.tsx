import { StyleProp, StyleSheet, TouchableOpacity, View, ViewStyle } from "react-native";
import React, { memo } from "react";
import { CustomTextInput } from "@/components/ui/text-input";
import { CloseOutlineIcon, SearchFilledIcon } from "@/components/icons";
import { Colors } from "@/constants/Colors";

interface MessageSearchProps {
  placeHolder: string;
  placeHolderColor: string;
  searchHandler: (text: string) => void;
  value: string;
  containerStyle?: StyleProp<ViewStyle>;
}

const SIZE = 16;
const PADDING = 3;

const MessageSearch: React.FC<MessageSearchProps> = memo(({
  placeHolder,
  placeHolderColor,
  searchHandler,
  value,
  containerStyle,
}) => {
  const Icon = (
    <SearchFilledIcon width={16} height={16} fill={Colors.dark.grey} />
  );

  const onClear = () => {
    searchHandler("");
  };

  return (
    <View style={[containerStyle]}>
      <CustomTextInput
        IconLeft={Icon}
        placeHolderColor={placeHolderColor}
        placeholder={placeHolder}
        inputStyle={styles.searchInput}
        editable
        onChangeText={searchHandler}
        value={value}
        IconRight={ 
          value.trim().length > 0 ? (
            <TouchableOpacity onPress={onClear} style={styles.closeIconContainer}>
              <CloseOutlineIcon width={SIZE} height={SIZE} color={Colors.dark.offWhite} />
            </TouchableOpacity>
          ): undefined
        }
      />
    </View>
  );
});

export default MessageSearch;

const styles = StyleSheet.create({
  searchInput: {
    borderRadius: 100,
    borderColor: Colors.dark.darkGrey,
  },
  closeIconContainer: {
    backgroundColor: 'transparent',
    borderRadius: (SIZE * 2 + PADDING * 2) / 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.dark.darkGrey,
    padding: PADDING
  }
});
