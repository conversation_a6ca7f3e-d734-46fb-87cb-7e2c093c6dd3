import React, { useMemo } from "react";
import { View, FlatList, ActivityIndicator } from "react-native";
import { useGlobalSearchParams } from "expo-router";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { UserListItem } from "../_components/user-list-item";
import { UserListItemSkeleton } from "../_components/user-list-item-skeleton";
import { useFollowersInfiniteQuery } from "@/queries/follow-queries";
import { useUserByHandleQuery } from "@/queries/user-queries";
import { Colors } from "@/constants/Colors";
import { Follower } from "@/queries/types/follow";
import { User } from "@/types";
import { useSearchString } from "./SearchStringContext";

export default function FollowersTab() {
  const searchString = useSearchString();
  const params = useGlobalSearchParams<{ userHandle: string }>();
  const { data: userData, isLoading: isUserDataLoading } = useUserByHandleQuery(params.userHandle as string);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isFetching,
  } = useFollowersInfiniteQuery({
    userId: userData?.user?.id,
    searchString,
  });

  const followers = useMemo(() => {
    if (!data) return [];
    return data?.pages.reduce((prev, current) => {
      return [...prev, ...(current?.followersWithFollowedByloggedInUser || [])];
    }, [] as Follower[]);
  }, [data]);

  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <View className="py-5 items-center">
          <ActivityIndicator size="small" color={Colors.dark.offWhite} />
        </View>
      );
    }
    return null;
  };

  const handleEndReached = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };

  if (isUserDataLoading || isLoading || isFetching ) {
    // Show a list of skeletons while loading or fetching new search results
    return (
      <ThemedView className="flex-1 bg-dark-background pt-2 mt-1">
        {Array.from({ length: 8 }).map((_, idx) => (
          <UserListItemSkeleton key={idx} />
        ))}
      </ThemedView>
    );
  }

  return (
    <ThemedView className="flex-1 bg-dark-background">
      {followers.length === 0 ? (
        <View className="flex-1 items-center justify-center pt-10 ">
          <ThemedText className="text-sm text-dark-greyText">
            No followers found
          </ThemedText>
        </View>
      ) : (
        <FlatList
          data={followers}
          renderItem={({ item }) => (
            <UserListItem
              user={{ ...item.follower, following: null }}
              isFollowing={item.followedByloggedInUser}
            /> 
          )}
          keyExtractor={(item) => item.id.toString()}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          className="pt-2 mt-1"
        />
      )}
    </ThemedView>
  );
}


