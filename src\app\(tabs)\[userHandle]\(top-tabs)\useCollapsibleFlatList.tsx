import React from "react";
import { Animated } from "react-native";
import { useScrollSync } from "./_layout";

export function useCollapsibleFlatList() {
  const scrollY = useScrollSync();

  // Return a component that wraps Animated.FlatList with scrollY wired up
  return React.useCallback(
    (props: any) => (
      <Animated.FlatList
        {...props}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false, ...props.onScroll }
        )}
        scrollEventThrottle={16}
      />
    ),
    [scrollY]
  );
} 