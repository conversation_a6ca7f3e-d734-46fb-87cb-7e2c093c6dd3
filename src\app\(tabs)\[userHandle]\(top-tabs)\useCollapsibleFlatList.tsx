import React from "react";
import Animated, { useAnimatedScrollHand<PERSON> } from "react-native-reanimated";
import { useScrollSync } from "./_layout";

export function useCollapsibleFlatList() {
  const { scrollY } = useScrollSync();

  // Create smooth scroll handler using reanimated
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
    },
  });

  // Return a component that wraps Animated.FlatList with smooth scrollY handling
  return React.useCallback(
    (props: any) => (
      <Animated.FlatList
        {...props}
        onScroll={scrollHandler}
        scrollEventThrottle={1} // Use 1 for smoothest animation with reanimated
      />
    ),
    [scrollHandler]
  );
}