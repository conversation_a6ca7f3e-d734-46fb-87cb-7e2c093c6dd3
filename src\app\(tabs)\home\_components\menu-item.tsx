import { Href, router } from "expo-router";
import { StyleSheet, TouchableOpacity, View } from "react-native";

import { Colors } from "@/constants/Colors";
import { ThemedText } from "@/components/ThemedText";

interface MenuItemProps {
  item: {
    name: string;
    navigateTo: string;
  };
}

export const MenuItem = ({ item }: MenuItemProps) => {
  const navigateTo = (link: Href<string>) => router.push(link);
    
  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={() => navigateTo(item.navigateTo as Href<string>)}>
        <ThemedText style={styles.itemText}>{item.name}</ThemedText>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderColor: "rgba(59, 59, 59, 1)",
  },
  itemText: {
    color: Colors.dark.greyText,
    fontWeight: '400',
    fontSize: 14
  }
});
