import { UndefinedInitialDataOptions, useQuery } from "@tanstack/react-query";

import { getAirdrop } from "@/api/client/airdrop";

import { AirdropResponse } from "./types/airdrop";

type AirdropQueryOptions = Omit<
  UndefinedInitialDataOptions<AirdropResponse>,
  "queryKey"
>;

export const useAirdropQuery = (options?: AirdropQueryOptions) => {
  return useQuery({
    queryKey: ["airdrop"],
    queryFn: getAirdrop,
    ...options,
  });
};
