import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useDeleteThreadMutation,
  useLikeThreadMutation,
  useRepostThreadMutation,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { Thread } from "@/types";
import PostUI from "@/components/post";
import { Href, useLocalSearchParams, useRouter } from "expo-router";

interface CommentPostProps {
  thread: Thread;
}

export const CommentPost = ({ thread }: CommentPostProps) => {
  const params = useLocalSearchParams() as { id: string };
  const router = useRouter();

  const isRepost = thread.threadType === "repost";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const queryClient = useQueryClient();

  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "answers", params.id],
      });

      const previousAnswers:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "threads",
        "answers",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "answers", params.id],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === thread.id) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === thread.id) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousAnswers };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "answers", params.id],
        context?.previousAnswers
      );
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "answers", params.id],
      });

      const previousAnswers:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "threads",
        "answers",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "answers", params.id],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === thread.id) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === thread.id) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousAnswers };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "answers", params.id],
        context?.previousAnswers
      );
    },
  });
  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "answers", params.id],
      });

      const previousAnswers:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "threads",
        "answers",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "answers", params.id],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousAnswers };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "answers", params.id],
        context?.previousAnswers
      );
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "answers", params.id],
      });

      const previousAnswers:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "threads",
        "answers",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "answers", params.id],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousAnswers };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "answers", params.id],
        context?.previousAnswers
      );
    },
  });
  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "answers", params.id],
      });

      const previousAnswers:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "threads",
        "answers",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "answers", params.id],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousAnswers };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "answers", params.id],
        context?.previousAnswers
      );
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["threads", "answers", params.id],
      });

      const previousAnswers:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "threads",
        "answers",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "answers", params.id],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousAnswers };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "answers", params.id],
        context?.previousAnswers
      );
    },
  });
  const { mutateAsync: deleteThread } = useDeleteThreadMutation({
    onMutate: async ({ threadId }) => {
      toast.red(`Comment deleted!`);
      await queryClient.cancelQueries({
        queryKey: ["threads", "answers", params.id],
      });

      const previousAnswers:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "threads",
        "answers",
        params.id,
      ]);

      queryClient.setQueryData(
        ["threads", "answers", params.id],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        }
      );

      return { previousAnswers };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["threads", "answers", params.id],
        context?.previousAnswers
      );
    },
  });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (activePost.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (activePost.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (activePost.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  const handleDelete = async ({ threadId }: { threadId: string }) => {
    await deleteThread({ threadId });
  };

  return (
    <PostUI
      key={thread.id}
      thread={thread}
      handleLike={handleLike}
      handleBookmark={handleBookmark}
      handleRepost={handleRepost}
      onClick={() => {
        router.push(`/${thread.user?.twitterHandle}/nested/${thread.id}` as Href<string>);
      }}
      handleDelete={handleDelete}
      comment
    />
  );
};
