import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { postBuyShare, postBuyTicker, postSellShare } from "@/api/client/trade";
import { minDelay } from "@/utils/min-delay";

type PostTradeShareMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    address: string;
    amount: string;
  },
  any
>;

type PostBuyTickerMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    srcToken: string;
    destToken: string;
    srcAmount: string;
    threadId: string;
  },
  any
>;

export const useBuyShareMutation = (options?: PostTradeShareMutationType) => {
  return useMutation({
    mutationFn: (variables) => minDelay(postBuyShare(variables)),
    ...options,
  });
};

export const useSellShareMutation = (options?: PostTradeShareMutationType) => {
  return useMutation({
    mutationFn: (variables) => minDelay(postSellShare(variables)),
    ...options,
  });
};

export const useBuyTickerMutation = (options?: PostBuyTickerMutationType) => {
  return useMutation({
    mutationFn: postBuyTicker,
    ...options,
  });
};
