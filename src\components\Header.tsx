import React, { useEffect } from "react";
import {
  View,
  Image,
  StyleSheet,
  ImageSourcePropType,
  TouchableOpacity,
} from "react-native";
import Logo from "@assets/icons/logo.png";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Avatar from "./ui/avatar";
import { useUser } from "@/stores";

const profileSize = 34;

interface HeaderProps {
  navigation: any;
}

const Header: React.FC<HeaderProps> = ({navigation}) => {
  const inset = useSafeAreaInsets();
  const {user} = useUser();

  const openSideDrawer = () => {
     navigation.openDrawer();
  }
  
  return (
    <View
      style={[styles.container, { paddingTop: inset.top + 20 }]}
    >
      <TouchableOpacity onPress={openSideDrawer}>
        <Avatar src={user?.twitterPicture} size={profileSize} style={styles.profile} />
      </TouchableOpacity>
      <Image source={Logo as ImageSourcePropType} style={styles.logo} />
      <Avatar
        src={user?.twitterPicture}
        size={profileSize}
        style={[styles.profile, styles.invisible]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#020202",
    padding: 20,
  },
  logo: {
    height: 34,
    width: 34,
  },
  profile: {
    borderColor: "white",
    borderWidth: 0.37,
  },
  invisible: {
    opacity: 0,
  },
});

export default Header;
