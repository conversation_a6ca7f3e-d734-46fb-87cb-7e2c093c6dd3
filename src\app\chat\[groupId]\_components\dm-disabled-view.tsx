import { ThemedText } from "@/components/ThemedText";
import { But<PERSON> } from "@/components/ui/button";
import { Colors } from "@/constants/Colors";
import { router } from "expo-router";
import { memo } from "react";
import { LayoutChangeEvent, StyleSheet, View } from "react-native";


type TDMDisabledView = {
    onLayout: (event: LayoutChangeEvent) => void;
}

export const DMDisabledView = memo(({ onLayout }: TDMDisabledView) => {
    return (
        <View onLayout={onLayout} style={styles.container}>
            <ThemedText style={styles.text}>
            This user does not accept Direct Messages for now
            </ThemedText>
            <Button
                variant="outline"
                onPress={() => {
                    router.back();
                }}
                style={styles.button}
            >
                <ThemedText type="bold">Back</ThemedText>
            </Button>
        </View>
    )
}, (prevProps, nextProps) => {
    return prevProps.onLayout === nextProps.onLayout
})

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderTopWidth: 1,
        borderTopColor: Colors.dark.darkGrey,
        marginTop: 10
    },
    text: {
        lineHeight: 24
    },
    button: {
        width: '100%',
        marginTop: 20
    }
});