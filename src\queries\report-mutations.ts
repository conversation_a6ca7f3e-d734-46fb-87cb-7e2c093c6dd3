import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { reportThread, reportUser } from "../api/client/report";

type ReportUserMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    userId: string;
    reason: string;
    details: string;
    blockUser?: boolean;
  },
  any
>;

export const useReportUserMutation = (options?: ReportUserMutationType) => {
  return useMutation({
    mutationFn: reportUser,
    ...options,
  });
};

type ReportThreadMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    threadId: string;
    reason: string;
    details: string;
    blockUser?: boolean;
  },
  any
>;

export const useReportThreadMutation = (options?: ReportThreadMutationType) => {
  return useMutation({
    mutationFn: reportThread,
    ...options,
  });
};
