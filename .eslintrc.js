module.exports = {
  env: {
    'jest/globals': true,
  },
  root: true,
  extends: ['@react-native'],
  plugins: ['jest'],
  rules: {
    semi: ['error', 'never', { beforeStatementContinuationChars: 'never' }],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
    'react/default-props-match-prop-types': ['error'],
    'react/sort-prop-types': ['error'],
    // Disabled default no-shadow rule since it reports all enums as already defined in upper scope
    // Read more details here: https://github.com/typescript-eslint/typescript-eslint/blob/main/packages/eslint-plugin/docs/rules/no-shadow.md#how-to-use
    'no-shadow': 'off',
    '@typescript-eslint/no-shadow': ['error'],
    // Defaults to max lines of 300 per file(ignoring blank lines and comments)
    'max-lines': ['error'],
  },
  globals: {
    JSX: true,
  },
}
