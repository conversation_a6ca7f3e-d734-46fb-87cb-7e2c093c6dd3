import Svg, { Path, SvgProps } from "react-native-svg";

export const HideRepostsIcon = (props: SvgProps) => (
  <Svg
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <Path
      d="M13.875 18.825C13.2569 18.9419 12.6291 19.0005 12 19C7.52203 19 3.73203 16.057 2.45703 12C2.8003 10.9081 3.32902 9.88346 4.02003 8.971M9.87803 9.879C10.4407 9.31634 11.2038 9.00025 11.9995 9.00025C12.7952 9.00025 13.5584 9.31634 14.121 9.879C14.6837 10.4417 14.9998 11.2048 14.9998 12.0005C14.9998 12.7962 14.6837 13.5593 14.121 14.122M9.87803 9.879L14.121 14.122M9.87803 9.879L14.12 14.12M14.121 14.122L17.412 17.412M9.88003 9.88L6.59003 6.59M6.59003 6.59L3.00003 3M6.59003 6.59C8.20239 5.54957 10.0811 4.9974 12 5C16.478 5 20.268 7.943 21.543 12C20.8391 14.2305 19.3774 16.1446 17.411 17.411M6.59003 6.59L17.411 17.411M17.411 17.411L21 21"
      stroke={props.stroke || "#B0B0B0"}
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
