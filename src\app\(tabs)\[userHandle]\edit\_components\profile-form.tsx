import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { CustomTextInput } from "@/components/ui/text-input";
import { useUpdateBioMutation } from "@/queries/profile-mutations";
import { useUserByHandleQuery } from "@/queries/user-queries";
import { useQueryClient } from "@tanstack/react-query";
import { Href, router, useGlobalSearchParams } from "expo-router";
import { useEffect, useId, useState } from "react";
import { View, StyleSheet } from "react-native";

export const ProfileForm = () => {
  const queryClient = useQueryClient();
  const params = useGlobalSearchParams() as { userHandle: string };

  const id = useId();
  const formItemId = `form-item-${id}`;
  const { data, isLoading } = useUserByHandleQuery(params.userHandle);
  const [bio, setBio] = useState("");

  const { mutateAsync: updateBio, isPending } = useUpdateBioMutation({
    onMutate: async ({ bio }) => {
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", params.userHandle],
      });

      const previousData = queryClient.getQueryData([
        "user",
        "handle",
        params.userHandle,
      ]);

      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (old: any) => {
          return {
            ...old,
            user: {
              ...old.user,
              twitterDescription: bio,
            },
          };
        }
      );

      return { previousData };
    },
    onError: (err, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(
          ["user", "handle", params.userHandle],
          context.previousData
        );
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["user", "handle", params.userHandle],
      });
      router.push(`/${params.userHandle}` as Href<string>);
    },
  });

  const handleSubmit = async () => {
    if (!bio) return;

    if (bio === data?.user.twitterDescription) {
      return toast.danger("No changes to save");
    }

    await updateBio({ bio });
  };

  useEffect(() => {
    if (data) {
      const regex = /<a\b[^>]*>(.*?)<\/a>/gi;
      const bioDescription = (data.user.twitterDescription ?? "")?.replace(
        regex,
        "$1"
      );
      setBio(bioDescription);
    }
  }, [data]);

  return (
    <View style={styles.profileForm}>
      <View className="my-4">
        <CustomTextInput
          onChangeText={(e) => setBio(e)}
          value={bio}
          maxLength={160}
          multiline={true}
          labelText="BIO"
          placeholder="Enter your bio"
          inputStyle={styles.inputStyle}
          labelStyle={styles.labelStyle}
        />
      </View>
      <Button
        onPress={handleSubmit}
        textStyle={styles.labelStyle}
        >
        Save
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  profileForm: {
    paddingHorizontal: 20,
    paddingVertical: 7,
    flex: 1,
    justifyContent: 'space-between'
  },
  inputStyle: {
    minHeight: 80,
    
  },
  labelStyle: {
    fontFamily: "InterSemiBold"
  }
});
