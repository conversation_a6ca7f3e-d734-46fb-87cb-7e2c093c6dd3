import { globalStyles } from "@/app/globalStyles";
import { KeyFilledIcon } from "@/components/icons";
import { TabBarIcon } from "@/components/navigation/TabBarIcon";
import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { CheckFilledIcon } from "@/components/icons";
import React, { useState, useRef, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  LayoutChangeEvent,
  Image,
  Pressable,
} from "react-native";

interface DropdownItem {
  id: string;
  walletName: string;
  walletDets: string;
}

interface CardWithDropdownProps {
  dropdownItems: DropdownItem[];
}

export const CardWithDropdown: React.FC<CardWithDropdownProps> = ({
  dropdownItems,
}) => {
  const [isDropdownVisible, setDropdownVisible] = useState(false);
  const [cardWidth, setCardWidth] = useState(0); // State to store card width
  const [cardHeight, setCardHeight] = useState(0); // State to store card height
  const cardRef = useRef<View>(null); // Ref to access the card view

  const toggleDropdown = () => {
    setDropdownVisible((prevState) => !prevState);
  };

  // Callback to handle card layout and store its dimensions
  const handleLayout = useCallback((event: LayoutChangeEvent) => {
    const { width, height } = event.nativeEvent.layout;
    setCardWidth(width);
    setCardHeight(height);
  }, []);

  const renderDropdownItem = ({ item }: { item: DropdownItem }) => (
    <TouchableOpacity style={styles.dropdownItem}>
      <View style={styles.itemCardChildLeft}>
        <View style={styles.itemHeader}>
          <KeyFilledIcon color={Colors.dark.offWhite} height={14} width={14} />
          <ThemedText style={globalStyles.textSmall}>
            {item.walletName}
          </ThemedText>
        </View>
        <ThemedText style={styles.cardItemText}>{item.walletDets}</ThemedText>
      </View>
      <TabBarIcon name="checkmark-circle-outline" color={"white"} size={20} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container} onLayout={handleLayout} ref={cardRef}>
      <View style={styles.card}>
        <View>
          <ThemedText style={styles.smallText}>Balance</ThemedText>
          <View style={styles.logoWrapper}>
            <Image
              style={styles.logo}
              source={require("@assets/coins/avax.png")}
            />
            <ThemedText style={styles.coinValueText}>12.92</ThemedText>
          </View>
        </View>
        <Pressable onPress={toggleDropdown} style={styles.inputView}>
          <ThemedText style={styles.inputText}>Current Wallet</ThemedText>
          <TabBarIcon
            name="chevron-down-outline"
            color={Colors.dark.greyText}
            size={20}
          />
        </Pressable>
      </View>

      {/* Dropdown List */}
      {isDropdownVisible && (
        <View
          style={[
            styles.dropdownContainer,
            { width: cardWidth, top: cardHeight },
          ]}
        >
          <FlatList
            data={dropdownItems}
            renderItem={renderDropdownItem}
            keyExtractor={(item, index) => item.id || index.toString()}
            style={styles.dropdownList}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dropdownContainer: {
    position: "absolute",
    left: 0,
    backgroundColor: Colors.dark.grayBg,
    borderRadius: 8,
    elevation: 10,
    zIndex: 999,
  },
  dropdownList: {
    maxHeight: 300,
    elevation: 10,
    zIndex: 999,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
  },
  logo: {
    width: 16,
    aspectRatio: 1,
  },
  logoWrapper: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  container: {
    zIndex: 8,
    position: "relative",
  },
  card: {
    borderWidth: 1,
    borderColor: Colors.dark.greyText,
    borderRadius: 10,
    padding: 16,
    marginTop: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    position: "relative",
    zIndex: 9,
  },
  smallText: {
    fontWeight: "600",
    fontSize: 14,
    color: Colors.dark.greyText,
    marginBottom: 3,
  },
  coinValueText: {
    fontSize: 15,
    fontWeight: "600",
    color: "rgba(244, 244, 244, 1)",
  },
  inputView: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.dark.greyText,
    paddingVertical: 6,
    paddingRight: 8,
    paddingLeft: 12,
    gap: 4,
  },
  inputText: {
    fontSize: 14,
    fontWeight: "500",
    color: "rgba(244, 244, 244, 1)",
  },
  itemHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  cardItemText: {
    ...globalStyles.textSmall,
    fontWeight: "400",
  },
  itemCardChildLeft: {
    gap: 8,
  },
});
