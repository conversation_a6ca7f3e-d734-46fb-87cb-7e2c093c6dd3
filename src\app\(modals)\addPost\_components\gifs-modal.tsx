import MessageSearch from "@/app/(tabs)/messages/_components/message-search";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { Colors } from "@/constants/Colors";
import useThrottle from "@/hooks/use-throttle";
import { useGIFsQuery } from "@/queries";
import { GIFType } from "@/queries/types/threads";
import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ModalProps,
  ActivityIndicator,
  Image,
  Pressable,
  FlatList,
} from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";

interface GIFsModalProps extends ModalProps {
  onSelect: (gif: GIFType) => void;
  onClose: () => void;
}

const GIFsModal: React.FC<GIFsModalProps> = ({
  visible,
  onClose,
  children,
  onSelect,
  ...modalProps
}) => {
  const [search, setSearch] = useState("");

  const searchHandler = (text: string) => {
    setSearch(text);
  };

  const throttledSearch = useThrottle(search);
  const { data, isLoading } = useGIFsQuery(throttledSearch);

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
      {...modalProps}
    >
      <SafeAreaProvider>
        <SafeAreaView style={[styles.modalContainer]}>
          <View style={styles.modalContent} className="bg-dark-bk">
            <View className="flex-row items-center w-full px-6 py-4 border-b border-dark-gray">
              <TouchableOpacity onPress={onClose}>
                <ArrowBackOutlineIcon
                  height={20}
                  width={20}
                  className="text-off-white mr-4"
                />
              </TouchableOpacity>
              <MessageSearch
                placeHolder="Search for GIFs"
                placeHolderColor={Colors.dark.grey}
                searchHandler={searchHandler}
                value={search}
                containerStyle={styles.searchContainer}
              />
            </View>
            {isLoading && (
              <View className="flex items-center justify-center py-10">
                <ActivityIndicator
                  size="large"
                  color={Colors.dark.brandOrange}
                />
              </View>
            )}
            {!isLoading && data && (
              <FlatList
                data={data?.results}
                numColumns={2}
                renderItem={({ item }) => (
                  <View key={item.id} style={styles.imageContainer}>
                    <Pressable
                      onPress={() => {
                        onSelect(item);
                        onClose();
                      }}
                    >
                      <Image
                        resizeMode="cover"
                        style={styles.gifImage}
                        source={{ uri: item.media_formats.gif.url }}
                      />
                    </Pressable>
                  </View>
                )}
                keyExtractor={(item, index) => item.id || index.toString()}
              />
            )}
          </View>
        </SafeAreaView>
      </SafeAreaProvider>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  modalContent: {
    width: "100%",
    height: "100%",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  searchContainer: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
    margin: 4,
    aspectRatio: 1,
  },
  gifImage: {
    height: "100%",
    width: "100%",
  },
});

export default GIFsModal;
