import { ThemedText } from '@/components/ThemedText';
import { memo } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';

const LoginWithEmail = ({ onPress } = { onPress: () => {} }) => {
  return (
    <View style={styles.row}>
      <ThemedText style={styles.beforelink}>
        Can't access your X account?{' '}
      </ThemedText>
      <Pressable onPress={onPress}>
        <Text style={styles.link}>Log-in using email</Text>
      </Pressable>
    </View>
  );
};
const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 2,
    marginBottom: 30,
  },
  beforelink: {
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 18,
    color: '#808080',
    textDecorationStyle: 'solid',
    textDecorationColor: '#808080',
  },
  link: {
    fontFamily: 'Inter',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 18,
    color: '#F4F4F4',
    textDecorationLine: 'underline',
    textDecorationStyle: 'solid',
    textDecorationColor: '#F4F4F4',
  },
});

export default memo(LoginWithEmail);
