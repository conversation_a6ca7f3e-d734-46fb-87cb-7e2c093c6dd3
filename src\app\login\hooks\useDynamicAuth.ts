import { loginDynamic } from '@/actions/login-dynamic';
import { toast } from '@/components/toast';
import { getCookie } from '@/cookies/secure-store';
import { dynamicClient } from '@/dynamicClient';
import { navigationRoutes } from '@/navigationRoutes';
import { useUser } from '@/stores';
import { useQuery } from '@tanstack/react-query';
import { router } from 'expo-router';
import { useEffect } from 'react';

export const useDynamicAuth = (
  shouldLogin: boolean,
  setIsLoading: (isLoading: boolean) => void,
) => {
  const token = dynamicClient.auth.token;
  const { setUser, setTwitterUser, setToken } = useUser();

  const { isError, isSuccess, error } = useQuery({
    queryKey: ['login', 'dynamic'],
    queryFn: () => loginDynamic({ token, ref: null }),
    enabled: !!token && shouldLogin,
  });

  useEffect(() => {
    async function populateUserData() {
      const tokenCookie = await getCookie('token');
      setToken(tokenCookie ? tokenCookie.value : null);

      const userCookie = await getCookie('user');
      setUser(userCookie ? JSON.parse(userCookie.value || '{}') : null);

      const twitterUserCookie = await getCookie('twitterUser');
      setTwitterUser(
        twitterUserCookie ? JSON.parse(twitterUserCookie.value || '{}') : null,
      );
      setIsLoading(false);
    }

    if (isSuccess && shouldLogin) {
      populateUserData();
      router.push(navigationRoutes.home);
    }
  }, [isSuccess, router]);

  useEffect(() => {
    if (isError && shouldLogin) {
      console.error('error', error);
      toast.danger('Something went wrong!');
    }
  }, [isError, error]);

  return {
    isError,
    isSuccess,
    error,
    token,
  };
};
