import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View } from "react-native"
import { ThemedText } from "@/components/ThemedText"
import { useMemo, useState, useCallback } from "react";
import { useDirectMessagesInfiniteQuery } from "@/queries/chat-queries";
import { useUser } from "@/stores";
import { Colors } from "@/constants/Colors";
import { globalStyles } from "@/app/globalStyles";
import DMRequestItem from "./_components/dm-request-item";
import { paddingHorizontal } from "@/app/(tabs)/messages/(top-tabs)/_layout";
import { Group } from "@/queries/types";
import { ListSkeleton } from "@/app/(tabs)/messages/_components/skeleton";

const MessageRequests = () => {
    const { user } = useUser();

    const {
        data: directMessagesData,
        isLoading: directMessagesIsLoading,
        refetch: refetchDirectMessages,
        isFetchingNextPage: directMessagesIsFetchingNextPage,
        fetchNextPage: fetchNextDirectMessagesPage,
        hasNextPage: directMessagesHasNextPage,
    } = useDirectMessagesInfiniteQuery();

    const [refreshing, setRefreshing] = useState(false);

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await refetchDirectMessages();
        } catch (error) {
        } finally {
            setRefreshing(false);
        }
    };

    const allDirectMessages = useMemo(() => {
        return directMessagesData?.pages.map((page) => page.groups).flat();
    }, [directMessagesData]);

    const filteredDirectMessages = useMemo(() => {
        return allDirectMessages?.filter((group) => !group.isTemporary && group.isDirect && group?.isRequest && group?.lastUserId !== user?.id);
    }, [allDirectMessages, user]);
    
    const renderItem = useCallback(({ item }: { item: Group }) => (
        <DMRequestItem
            groupId={item.id}
            groupName={item.name}
            groupImageUrl={item.profilePictureUrl}
            lastMessage={item.lastMessage}
            chatMateId={item.chatMateId || ""}
            ownerUserId={item.ownerUserId || ""}
            loggedInUserId={user?.id || ""}
            chatMateName={item.chatMateName || ""}
        />
    ), [user?.id]);

    return directMessagesIsLoading ? (
        <ListSkeleton />
    ) : filteredDirectMessages && filteredDirectMessages.length > 0 ? (
        <FlatList
            data={filteredDirectMessages}
            style={{ flex: 1, paddingHorizontal: paddingHorizontal }}
            renderItem={renderItem}
            onEndReached={() => {
                if (!directMessagesIsFetchingNextPage) {
                    fetchNextDirectMessagesPage();
                }
            }}
            onEndReachedThreshold={0.7}
            keyExtractor={(item) => item.id}
            refreshControl={
                <RefreshControl
                    refreshing={refreshing}
                    onRefresh={handleRefresh}
                    colors={[Colors.dark.brandOrange]}
                />
            }
            ListFooterComponent={
                directMessagesIsFetchingNextPage || directMessagesHasNextPage ? (
                    <ActivityIndicator size="large" color={Colors.dark.brandOrange} />
                ) : null
            }
        />
    ) : (
        <View style={styles.noRoomsContainer}>
            <ThemedText style={styles.defaultText}>
                No Message Requests found!
            </ThemedText>
        </View>
    )
}

export default MessageRequests;

const styles = StyleSheet.create({
    noRoomsContainer: {
        ...globalStyles.centerAligned,
        height: '100%',
    },
    defaultText: {
        fontSize: 15,
        marginHorizontal: "auto",
        marginVertical: 20,
        fontFamily: "InterSemiBold",
        fontWeight: "700",
    },
});
