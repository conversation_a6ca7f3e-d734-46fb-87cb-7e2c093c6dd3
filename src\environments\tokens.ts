import { BACKEND_FRIENDS_CONTRACT } from "@/environments/BACKEND_FRIENDS_CONTRACT";

export const AVAX = {
  name: "AVAX",
  icon: require("@assets/coins/avax.png"),
  native: true,
};

export const COQ = {
  name: "COQ",
  icon: require("@assets/coins/coq.png"),
  native: true,
};

export const GURS = {
  name: "GURS",
  icon: require("@assets/coins/gurs.png"),
  native: true,
};

export const NOCHILL = {
  name: "NOCHILL",
  icon: require("@assets/coins/nochill.png"),
  native: true,
};

export const MEAT = {
  name: "MEAT",
  icon: require("@assets/coins/meat.png"),
  native: true,
};

export const KIMBO = {
  name: "KIMBO",
  icon: require("@assets/coins/kimbo.png"),
  native: true,
};

export const SOL = {
  name: "SOL",
  icon: require("@assets/coins/solanaLogoMark.png"),
};

export const BONK = {
  name: "Bonk",
  icon: require("@assets/coins/bonk.jpeg"),
};

export const $WIF = {
  name: "$WIF",
  icon: require("@assets/coins/$WIF.jpeg"),
};

export const USEDCAR = {
  name: "USEDCAR",
  icon: require("@assets/coins/USEDCAR.png"),
};

export const MOUTAI = {
  name: "Moutai",
  icon: require("@assets/coins/Moutai.jpg"),
};

export const HARAMBE = {
  name: "HARAMBE",
  icon: require("@assets/coins/HARAMBE.jpg"),
};

export const JOE = {
  name: "JOE",
  icon: require("@assets/coins/joe.png"),
};

export const TECH = {
  name: "TECH",
  icon: require("@assets/coins/tech.png"),
};

export const ARENA = {
  name: "ARENA",
  icon: require("@assets/coins/arena.png"),
};

export const CHAMP = {
  name: "CHAMP",
  icon: require("@assets/coins/champ_logo.jpg"),
};

export const KET = {
  name: "KET",
  icon: require("@assets/coins/ket.png"),
  native: true,
};

export const WINK = {
  name: "WINK",
  icon: require("@assets/coins/wink.jpg"),
};

export const avax_tokens = [
  AVAX,
  ARENA,
  COQ,
  GURS,
  NOCHILL,
  MEAT,
  KIMBO,
  JOE,
  TECH,
  CHAMP,
  KET,
  WINK,
];
export const solana_tokens = [SOL, BONK, $WIF, USEDCAR, MOUTAI, HARAMBE];

export const tokens = [
  AVAX,
  ARENA,
  COQ,
  GURS,
  NOCHILL,
  MEAT,
  KIMBO,
  JOE,
  TECH,
  CHAMP,
  KET,
  SOL,
  BONK,
  $WIF,
  USEDCAR,
  MOUTAI,
  HARAMBE,
  WINK,
];

export const tokensWithoutSolana = [
  AVAX,
  ARENA,
  COQ,
  GURS,
  NOCHILL,
  MEAT,
  KIMBO,
  JOE,
  TECH,
  CHAMP,
  KET,
  WINK,
];

export const tempTokens = [
  AVAX,
  ARENA,
  COQ,
  GURS,
  NOCHILL,
  MEAT,
  KIMBO,
  JOE,
  TECH,
  CHAMP,
  KET,
  WINK,
  SOL,
  USEDCAR,
  HARAMBE,
];

export const tokensObject = {
  AVAX,
  ARENA,
  COQ,
  GURS,
  NOCHILL,
  MEAT,
  KIMBO,
  JOE,
  TECH,
  CHAMP,
  KET,
  WINK,
  SOL,
  BONK,
  $WIF,
  USEDCAR,
  MOUTAI,
  HARAMBE,
};

export interface MinTokenData {
  decimals: number;
  symbol: string;
  address: string;
  icon: string;
}

export const avaxToken = {
  decimals: 18,
  symbol: "AVAX",
  address: "0xEeeeeEeeeEeEeeEeEeEeeEEEeeeeEeeeeeeeEEeE",
  icon: "/assets/coins/avax.png",
};

export const swapTokens: Record<string, MinTokenData> = {
  ["ARENA"]: {
    decimals: 18,
    symbol: "ARENA",
    address: BACKEND_FRIENDS_CONTRACT.addressARENA,
    icon: require("@assets/coins/arena.png"),
  },
  ["COQ"]: {
    decimals: 18,
    symbol: "COQ",
    address: BACKEND_FRIENDS_CONTRACT.addressCOQ,
    icon: require("@assets/coins/coq.png"),
  },
  ["NOCHILL"]: {
    decimals: 18,
    symbol: "NOCHILL",
    address: BACKEND_FRIENDS_CONTRACT.addressNOCHILL,
    icon: require("@assets/coins/nochill.png"),
  },
  ["GURS"]: {
    decimals: 18,
    symbol: "GURS",
    address: BACKEND_FRIENDS_CONTRACT.addressGURS,
    icon: require("@assets/coins/gurs.png"),
  },
  ["JOE"]: {
    decimals: 18,
    symbol: "JOE",
    address: BACKEND_FRIENDS_CONTRACT.addressJOE,
    icon: require("@assets/coins/joe.png"),
  },
  ["TECH"]: {
    decimals: 18,
    symbol: "TECH",
    address: BACKEND_FRIENDS_CONTRACT.addressTECH,
    icon: require("@assets/coins/tech.png"),
  },
  ["CHAMP"]: {
    decimals: 18,
    symbol: "CHAMP",
    address: BACKEND_FRIENDS_CONTRACT.addressCHAMP,
    icon: require("@assets/coins/champ_logo.jpg"),
  },
  ["KET"]: {
    decimals: 18,
    symbol: "KET",
    address: BACKEND_FRIENDS_CONTRACT.addressKET,
    icon: require("@assets/coins/ket.png"),
  },
  ["WINK"]: {
    decimals: 18,
    symbol: "WINK",
    address: BACKEND_FRIENDS_CONTRACT.addressWINK,
    icon: require("@assets/coins/wink.jpg"),
  },
};
