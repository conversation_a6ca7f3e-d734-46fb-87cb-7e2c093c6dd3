import { GestureHandlerRootView } from "react-native-gesture-handler";
import { Drawer } from "expo-router/drawer";
import Header from "@/components/Header";
import { MenuModal } from "./_components/menu-modal";

export default function Layout() {
  return (
    <Drawer
      drawerContent={() => <MenuModal />}
      screenOptions={{
        drawerType: "front",
        header: ({ navigation }) => <Header navigation={navigation} />,
        drawerStyle: { flex: 1, height: "100%" },
      }}
    />
  );
}
