import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { postChainWithdraw } from "@/api/client/chain";

import { ChainWithdrawData } from "./types";

type ChainWithdrawMutationType = MutationOptions<
  unknown,
  DefaultError,
  ChainWithdrawData
>;

export const useChainWithdrawMutation = (
  options?: ChainWithdrawMutationType,
) => {
  return useMutation({
    mutationFn: postChainWithdraw,
    ...options,
  });
};
