import { ThemedText } from "@/components/ThemedText";
import ProfileHeaderCard from "./profile-header-card";
import { CustomTextInput } from "@/components/ui/text-input";
import { Image, StyleSheet, View } from "react-native";
import { Colors } from "@/constants/Colors";
import { Button } from "@/components/ui/button";
import { useQueryClient } from "@tanstack/react-query";
import {
  useGlobalSearchParams,
  useRouter,
} from "expo-router";
import { Dispatch, SetStateAction, useMemo, useState } from "react";
import { useUser } from "@/stores";
import { useUserByHandleQuery } from "@/queries/user-queries";
import {
  useSharesHoldingsQuery,
  useSharesStatsQuery,
} from "@/queries/shares-queries";
import { formatAvax, formatEther } from "@/utils";
import { abbreviateNumber } from "@/utils/abbreviate-number";
import { toast } from "@/components/toast";
import { divideBigInt } from "@/utils";
import { useBalancesQuery, usePriceQuery } from "@/queries/balance-queries";
import {
  useBuyShareMutation,
  useSellShareMutation,
} from "@/queries/trade-mutations";

export default function BuyTicketModal({
  userHandle,
  setIsTicketPurchased,
}: {
  userHandle?: string;
  setIsTicketPurchased?: Dispatch<SetStateAction<boolean>>;
}) {
  type showCoinProps = {
    typeText: String;
    valueText: String;
  };
  const queryClient = useQueryClient();

  const {} = useRouter();
  const params = useGlobalSearchParams() as { userHandle: string };

  if (!userHandle) {
    userHandle = params.userHandle;
  }
  const [open, setOpen] = useState(false);

  const { user } = useUser();

  const { data, isLoading: isUserDataLoading } =
    useUserByHandleQuery(userHandle);

  const { data: holdingsData, isLoading: isHoldingsLoading } =
    useSharesHoldingsQuery();

  const { data: price } = usePriceQuery({ address: data?.user?.address });
  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: data?.user?.id,
    });
  const { data: balances } = useBalancesQuery({
    address: user?.address,
  });

  const isHoldingUserTicket = useMemo(() => {
    if (isHoldingsLoading && !holdingsData && isUserDataLoading) return false;

    if (!data?.user?.id) return false;

    if (!holdingsData?.holdings) return false;

    try {
      const holding = holdingsData?.holdings.find((h) => {
        if (!h) return false;
        if (!h.subjectUser) return false;
        if (!data?.user?.id || !h?.subjectUser?.id) return false;
        return h?.subjectUser?.id === data?.user?.id;
      });

      return (
        Boolean(holding) &&
        Boolean(holding?.amount) &&
        parseInt(holding ? holding.amount : "0") > 0
      );
    } catch (error) {
      console.log("error", error);
      return false;
    }
  }, [data, holdingsData, isHoldingsLoading, isUserDataLoading]);

  const balance = useMemo(() => {
    if (!balances) return "0.00";

    return formatEther(balances.AVAX.toString());
  }, [balances]);

  const ticketPrice = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatAvax(
      statsData?.stats?.keyPrice ?? data?.user?.keyPrice ?? ""
    );

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [
    statsData?.stats?.keyPrice,
    data?.user?.keyPrice,
    isUserDataLoading,
    isStatsDataLoading,
  ]);

  const [isNegative, percentageIncrease] = useMemo(() => {
    const keyPrice = BigInt(statsData?.stats?.keyPrice || 0);
    const lastKeyPrice = BigInt(data?.user?.lastKeyPrice || 0);

    const percentage = lastKeyPrice
      ? 100 * (divideBigInt(keyPrice, lastKeyPrice) - 1)
      : keyPrice
      ? 100
      : 0;

    return [percentage < 0, abbreviateNumber(percentage, 2)];
  }, [statsData?.stats?.keyPrice, data?.user?.lastKeyPrice]);

  const [buyPrice, sellPrice] = useMemo(() => {
    if (!price) return [null, null];

    let formattedBuyEther = formatAvax(price[0]);
    let formattedSellEther = formatAvax(price[1]);

    if (parseFloat(formattedBuyEther) >= 1) {
      formattedBuyEther = parseFloat(formattedBuyEther).toFixed(2);
    }

    if (parseFloat(formattedSellEther) >= 1) {
      formattedSellEther = parseFloat(formattedSellEther).toFixed(2);
    }

    return [formattedBuyEther, formattedSellEther];
  }, [price]);

  const { mutateAsync: buyShare, isPending: isBuySharePending } =
    useBuyShareMutation({
      onSuccess: () => {
        toast.green("Successfully bought Ticket");
        if (setIsTicketPurchased) {
          setIsTicketPurchased(true);
        }
        queryClient.invalidateQueries({
          queryKey: ["chat", "group", "profile", data?.user.id],
        });
        queryClient.invalidateQueries({
          queryKey: ["shares", "holdings"],
        });
      },
    });
  const { mutateAsync: sellShare, isPending: isSellSharePending } =
    useSellShareMutation({
      onSuccess: () => {
        toast.green("Successfully sold Ticket");
        queryClient.invalidateQueries({
          queryKey: ["chat", "group", "profile", data?.user.id],
        });
        queryClient.invalidateQueries({
          queryKey: ["shares", "holdings"],
        });
      },
    });

  const handleBuy = async () => {
    if (!data) return;

    await buyShare({
      address: data.user.address,
      amount: data.user.keyPrice,
    });
    setOpen(false);
  };

  const handleSell = async () => {
    if (!data) return;

    await sellShare({
      address: data.user.address,
      amount: data.user.keyPrice,
    });
    setOpen(false);
  };

  const ShowCoinValue = ({ typeText, valueText }: showCoinProps) => {
    return (
      <View style={styles.showCoinValue}>
        <ThemedText type="defaultSemiBold" style={styles.text}>
          {typeText}
        </ThemedText>
        <Image
          style={styles.coinImage}
          source={require("@assets/coins/avax.png")}
        />
        <ThemedText type="defaultSemiBold" style={styles.text}>
          {valueText}
        </ThemedText>
      </View>
    );
  };

  return (
    <View>
      <ThemedText style={styles.buyTicketModalHeading}>
        Trade Tickets
      </ThemedText>
      <View style={styles.body}>
        <ProfileHeaderCard
          data={data}
          ticketPrice={ticketPrice}
          isNegative={isNegative}
          percentageIncrease={percentageIncrease}
          showTicketPriceAndFollowers={true}
        />
        <CustomTextInput
          labelText="AVAILABLE BALANCE"
          labelStyle={styles.labelStyle}
          inputStyle={styles.inputStyle}
          IconLeft={
            <Image
              style={styles.avaxInputCoin}
              source={require("@assets/coins/avax.png")}
            />
          }
          value={balance}
        />
        <View style={styles.buttonWrapper}>
          <Button
            style={[styles.button]}
            onPress={handleBuy}
            >
            {<ShowCoinValue typeText={"Buy"} valueText={buyPrice || ""} />}
          </Button>
          <Button
            variant="outline"
            style={[styles.button, styles.sellButton]}
            onPress={handleSell}
            >
            {<ShowCoinValue typeText={"Sell"} valueText={sellPrice || ""} />}
          </Button>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  buyTicketModalHeading: {
    fontWeight: "700",
    marginBottom: 15,
  },
  body: {
    gap: 14,
  },
  labelStyle: {
    fontWeight: "700",
    fontSize: 11,
  },
  inputStyle: {
    borderRadius: 50,
    borderColor: Colors.dark.brandOrange,
  },
  showCoinValue: {
    flexDirection: "row",
    alignItems: "center",
    gap: 5,
  },
  coinImage: {
    width: 15,
    aspectRatio: 1,
  },
  buttonWrapper: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 10,
  },
  button: {
    flex: 1,
    gap: 10,
  },
  sellButton: {
    backgroundColor: "transparent",
    borderColor: Colors.dark.lightgrey,
    borderWidth: 1,
  },
  text: {
    fontSize: 13,
  },
  avaxInputCoin: {
    height: 17,
    aspectRatio: 1,
  },
});
