import React, { useState } from "react";
import { NestedPosts } from "./_components/nested-posts";
import { Comments } from "../../_components/comments";
import { ReplyEditor } from "@/components/editor/reply-editor";
import { ScrollView, View } from "react-native";

function NestedPostPage() {
  const [replyEditorHeight, setReplyEditorHeight] = useState(60);
  return (
    <>
      <ScrollView style={{ marginBottom: replyEditorHeight }}>
        <NestedPosts />
        <Comments />
      </ScrollView>
      <View
        className="absolute bottom-0 left-0 right-0 z-[100]"
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout;
          setReplyEditorHeight(height);
        }}
      >
        <ReplyEditor />
      </View>
    </>
  );
}

export default NestedPostPage;
