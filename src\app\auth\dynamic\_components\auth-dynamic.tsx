import { useEffect } from "react";

import { useQuery } from "@tanstack/react-query";

import { loginDynamic } from "@/actions/login-dynamic";
import { Href, router } from "expo-router";
import { useUser } from "@/stores";
import { getCookie } from "@/cookies/secure-store";
import { dynamicClient } from "@/dynamicClient";

interface AuthProps {
  token: string;
}

export const AuthDynamic = () => {
  const token = dynamicClient.auth.token || "";
  const { isError, isSuccess, error } = useQuery({
    queryKey: ["login", "dynamic"],
    queryFn: () => loginDynamic({ token, ref: null }),
    enabled: !!token,
  });

  const { setUser, setTwitterUser, setToken } = useUser();

  useEffect(() => {
    async function populateUserData() {
      const tokenCookie = await getCookie("token");
      setToken(tokenCookie ? tokenCookie.value : null);

      const userCookie = await getCookie("user");
      setUser(userCookie ? JSON.parse(userCookie.value || "{}") : null);

      const twitterUserCookie = await getCookie("twitterUser");
      setTwitterUser(
        twitterUserCookie ? JSON.parse(twitterUserCookie.value || "{}") : null
      );
    }
    if (isSuccess) {
      populateUserData();
      router.push("home/(top-tabs)" as Href<string>);
    }
  }, [isSuccess, router]);

  useEffect(() => {
    if (isError) {
      console.error("error", error);
      router.push("/?error=Something went wrong!" as Href<string>);
    }
  }, [isError, error, router]);

  useEffect(() => {
    if (!token) {
      router.push("/?error=Invalid request!" as Href<string>);
    }
  }, [router, token]);

  return null;
};
