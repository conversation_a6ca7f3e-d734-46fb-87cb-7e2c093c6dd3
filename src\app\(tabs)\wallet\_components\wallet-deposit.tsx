import { CopyOutlineIcon, SearchFilledIcon } from "@/components/icons";
import { TabBarIcon } from "@/components/navigation/TabBarIcon";
import { ThemedText } from "@/components/ThemedText";
import { Button } from "@/components/ui/button";
import Dropdown from "@/components/ui/select";
import { CustomTextInput } from "@/components/ui/text-input";
import { Colors } from "@/constants/Colors";
import { tokens } from "@/environments/tokens";
import { useUser } from "@/stores";
import { Text, View, StyleSheet, Image, Dimensions } from "react-native";
import { styled } from "nativewind";
import QRCode from "react-qr-code";
import * as Clipboard from 'expo-clipboard';
import { toast } from "@/components/toast";

const MAX_WIDTH = 350; // Max width for truncation
const ELLIPSIS = "...";

const { width: screenWidth } = Dimensions.get("window");

export const WalletDepostit = () => {
  const { user } = useUser();

  const address = user?.address ?? "";

  const StyledView = styled(View);

  const maxLength =
    Math.floor((MAX_WIDTH / screenWidth) * address.length) -
    ELLIPSIS.length;

  const truncatePlaceholder = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + ELLIPSIS;
  };

  const copyToClipboard = async () => {
    await Clipboard.setStringAsync(address);
    return toast.green("Copied to clipboard")
  };

  return (
    <View style={styles.container}>
      <ThemedText style={styles.sheetHeader}>Deposit</ThemedText>
      <ThemedText type="greyText">
        Transfer AVAX to the address below to fund your wallet for ticket buys
        and paying gas fees.
      </ThemedText>
      <StyledView className="mx-auto mt-1 mb-3 size-[200px] rounded-[20px] bg-white p-4">
        <QRCode
          size={200}
          value={address}
          viewBox={`0 0 200 200`}
          className="h-full w-full"
        />
      </StyledView>
      <CustomTextInput
        editable={false} //For disabling the input
        placeholder={truncatePlaceholder(address, maxLength)}
        labelText="WALLET ADDRESS"
        inputStyle={styles.disabledInput}
        IconRight={
          <CopyOutlineIcon
            width={20}
            height={20}
            color={Colors.dark.lightGreyText}
            onPress={copyToClipboard}
          />
        }
        iconColor={Colors.dark.lightgrey}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: 12,
  },
  sheetHeader: {
    marginBottom: 10,
  },
  barCodeImage: {
    alignSelf: "center",
  },
  disabledInput: {
    backgroundColor: Colors.dark.disableInputColor,
    borderWidth: 0,
  },
});
