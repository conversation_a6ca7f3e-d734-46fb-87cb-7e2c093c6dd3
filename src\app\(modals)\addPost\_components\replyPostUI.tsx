import React from "react";
import { View, Text, Image } from "react-native";
import { Video, ResizeMode } from "expo-av";
import { WebView } from "react-native-webview";
import { parseISO } from "date-fns";

import { checkContent, formatTimeDistance, insertSwapLinks } from "@/utils";
import { useExchangeCurrenciesQuery } from "@/queries/exchange-currencies-query";
import { Thread } from "@/types";
import Avatar from "@/components/ui/avatar";
import { ThemedView } from "@/components/ThemedView";
import CustomRenderHtml from "@/components/custom-render-html";

interface ReplyPostUIProps {
  thread: Thread;
  usersHandles?: string[] | null;
}

export const ReplyPostUI: React.FC<ReplyPostUIProps> = ({
  thread,
  usersHandles,
}) => {
  const { data: currencies = [] } = useExchangeCurrenciesQuery();

  const currencySymbols = currencies.map((currency) => currency.symbol);
  const date = parseISO(thread.createdDate);

  const [content, _isTrucated, youtubeEmbedUrl] = checkContent({
    content: thread.content,
    truncate: false,
  });

  const [contentWithSwapLinks] = insertSwapLinks(
    content,
    thread.id,
    currencySymbols
  );

  return (
    <View className="p-6 pb-1">
      <View className="flex flex-row space-x-3">
        <View className="relative flex-shrink-0">
          <Avatar src={thread.user.twitterPicture} size={42} />
          <View className="absolute left-1/2 top-0 -z-10 h-full w-px -translate-x-1/2 bg-gray-text" />
        </View>

        <View className="flex flex-col flex-1 space-y-2">
          <View className="flex flex-row min-w-0 text-sm text-gray-text">
            <Text className="flex-shrink-0 font-semibold text-off-white">
              {thread.userName}
            </Text>
            <Text className="text-gray-text">・</Text>
            <Text
              className="truncate text-gray-text min-w-0 flex-shrink"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              @{thread.user.twitterHandle}
            </Text>
            <Text className="text-gray-text truncate">・</Text>
            <Text className="flex-shrink-0 text-gray-text">
              {formatTimeDistance(date)}
            </Text>
          </View>

          <ThemedView className="w-full">
            <CustomRenderHtml html={contentWithSwapLinks} />
          </ThemedView>
          {thread.images && thread.images.length > 0 && (
            <Image
              source={{ uri: thread.images[0].url }}
              className="w-full rounded-2xl"
              resizeMode="cover"
              height={180}
            />
          )}
          {thread.videos && thread.videos.length > 0 && (
            <Video
              source={{
                uri: thread.videos[0].url,
              }}
              className="w-full rounded-2xl"
              useNativeControls={true}
              resizeMode={ResizeMode.COVER}
              style={{ height: 180 }}
              isLooping
              shouldPlay
              isMuted
            />
          )}

          {youtubeEmbedUrl && (
            <View className="w-full aspect-video overflow-hidden rounded-2xl">
              <WebView
                source={{ uri: youtubeEmbedUrl }}
                javaScriptEnabled={true}
                domStorageEnabled={true}
              />
            </View>
          )}

          <View className="flex flex-row flex-wrap">
            <Text className="text-sm text-gray-text">Replying to </Text>
            {usersHandles && usersHandles.length > 0 ? (
              <Text className="text-brand-orange">
                {usersHandles.length <= 3
                  ? usersHandles
                      .map(
                        (userHandle, index) =>
                          `@${userHandle}${
                            index !== usersHandles.length - 1 ? ", " : ""
                          }`
                      )
                      .join("")
                  : `${usersHandles
                      .slice(0, 2)
                      .map(
                        (userHandle, index) =>
                          `@${userHandle}${index !== 1 ? ", " : " "}`
                      )
                      .join("")} and ${usersHandles.length - 2} others`}
              </Text>
            ) : (
              <Text className="text-brand-orange">
                @{thread.user?.twitterHandle}
              </Text>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};
