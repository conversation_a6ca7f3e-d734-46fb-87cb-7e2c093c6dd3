import React, { useState } from "react";
import { Pressable, StyleSheet, Switch, Text, TouchableOpacity, View } from "react-native";
import * as ImagePicker from "expo-image-picker";
import * as FileSystem from "expo-file-system";
import { v4 } from "uuid";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import { ThemedView } from "@/components/ThemedView";
import { toast } from "@/components/toast";
import { AddCircleOutlineIcon, ArrowBackOutlineIcon, GIFOutlineIcon, ImageOutlineIcon, TwitterIcon } from "@/components/icons";
import { TicketOutlineIconV2 } from "@/components/icons-v2/ticket-outline-v2";

import { Colors } from "@/constants/Colors";

import { useTotalUploadedQuery } from "@/queries";
import { usePostStore } from "@/stores";

import { FileType } from "@/types";

import { getFileFromUrl, upload, uploadFileToXAPI } from "@/utils";

import GIFsModal from "../(modals)/addPost/_components/gifs-modal";

interface Props {
  handlePost: () => void;
  handleReply: () => void;
  handleQuote: () => void;
  isPostPending: boolean;
  isReplyPending: boolean;
  isQuotePending: boolean;
  isUploading: boolean;
  isQuoteRepost?: boolean;
  content?: string;
  setIsUploading: React.Dispatch<React.SetStateAction<boolean>>;
  files: FileType[];
  setFiles: React.Dispatch<React.SetStateAction<FileType[]>>;
  setPreview: React.Dispatch<
    React.SetStateAction<{
      url: string;
      type: "video" | "image";
    } | null>
  >;
  isPrivate: boolean;
  setIsPrivate: React.Dispatch<React.SetStateAction<boolean>>;
  setProgress: React.Dispatch<React.SetStateAction<number>>;
  isPostToX: boolean;
  setIsPostToX: React.Dispatch<React.SetStateAction<boolean>>;
  isUploadingToX: boolean;
  setIsUploadingToX: React.Dispatch<React.SetStateAction<boolean>>;
  xMediaIds: string[];
  setXMediaIds: React.Dispatch<React.SetStateAction<string[]>>;
}

const AddPostActionBox: React.FC<Props> = ({
  handlePost,
  handleReply,
  handleQuote,
  isPostPending,
  isReplyPending,
  isQuotePending,
  isUploading,
  isQuoteRepost,
  content,
  setIsUploading,
  files,
  setFiles,
  setPreview,
  isPrivate,
  setIsPrivate,
  setProgress,
  isPostToX,
  setIsPostToX,
  xMediaIds,
  setXMediaIds,
  isUploadingToX,
  setIsUploadingToX,
}) => {
  const type = usePostStore((state) => state.type);

  const { data: totalUploaded, isLoading: isUploadDataLoading } =
    useTotalUploadedQuery();

  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [isBottomSheetOpen, setBottomSheetOpen] = useState(false);

  const toggleModal = () => {
    setModalVisible((prevState) => !prevState);
  };

  const handleSheetClose = () => {
    setBottomSheetOpen(false);
  };

  const handleSheetOpen = () => {
    setBottomSheetOpen(true);
  };

  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
    setPreview(null);
  };

  const handleChange = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 0.8,
      base64: true,
    });

    if (!result.assets?.length) return;

    const file = result.assets[0];

    if (file) {
      if (file?.mimeType?.includes("video/ogg")) {
        toast.danger("Please upload video in either mp4 or webm format");
        return;
      }
      if (
        file?.mimeType?.includes("image") &&
        !(
          file.mimeType.includes("image/jpeg") ||
          file.mimeType.includes("image/gif") ||
          file.mimeType.includes("image/png")
        )
      ) {
        toast.danger("Please upload image in JPEG, JPG, PNG or GIF format");
        return;
      }
      if (!file.fileSize) {
        const fileInfo = await FileSystem.getInfoAsync(file.uri);
        if (!fileInfo.exists) {
          toast.danger("Please upload a file");
          return;
        }
        file.fileSize = fileInfo.size;
      }

      if (file.mimeType?.includes("image") && file.fileSize > 10 * 1024 * 1024) {
        toast.danger("Uploaded image file cannot exceed 10 MB");
        return;
      }
      if (file.mimeType?.includes("video") && file.fileSize > 300 * 1024 * 1024) {
        toast.danger("Uploaded video file cannot exceed 300 MB");
        return;
      }
      if (
        file.mimeType?.includes("video") &&
        file.fileSize + (totalUploaded || 0) > 600 * 1024 * 1024
      ) {
        toast.danger("You have reached your daily video upload limit of 600 MB.");
        resetUploading();
        return;
      }
      if (!file.fileName) {
        const fileName = file.uri.split("/").pop() || '';
        file.fileName = fileName;
      }

      setPreview({
        url: file.uri,
        type: file?.mimeType?.includes("image") ? "image" : "video",
      });
      setIsUploading(true);
      setProgress(0);

      try {
        let gcpProgress = 0;
        let xProgress = 0;
  
        const updateCombinedProgress = () => {
          const combinedProgress = isPostToX
            ? gcpProgress * 0.75 + xProgress * 0.25
            : gcpProgress;
          setProgress(combinedProgress);
        };
  
        const gcpUploadPromise = upload({
          file,
          onProgressChange: (progress) => {
            gcpProgress = progress;
            updateCombinedProgress();
          },
        });
  
        let xApiUploadPromise: Promise<any> | undefined;
        if (isPostToX) {
          xApiUploadPromise = uploadFileToXAPI(file, (progress) => {
            xProgress = progress;
            updateCombinedProgress();
          });
        }
  
        const [gcpResult, xApiResult] = await Promise.all([
          gcpUploadPromise,
          xApiUploadPromise ?? Promise.resolve(null),
        ]);
  
        if (isPostToX && !xApiResult?.success) {
          toast.danger(xApiResult?.error || "Failed to upload on X");
          setIsPostToX(false);
        }
  
        setFiles([
          {
            id: gcpResult.id,
            isLoading: false,
            previewUrl: gcpResult.previewUrl,
            url: gcpResult.url,
            fileType: file.mimeType?.includes("image") ? "image" : "video",
            size: file.fileSize,
          },
        ]);
  
        if (isPostToX && xApiResult?.mediaId) setXMediaIds([xApiResult.mediaId]);
      } catch (error) {
        console.error(error);
        toast.danger("File upload failed");
        resetUploading();
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleGifSelect = (gif: any) => {
    setFiles([
      {
        id: v4(),
        isLoading: false,
        previewUrl: gif.media_formats.gifpreview.url,
        url: gif.media_formats.gif.url,
        fileType: "image",
        size: 0,
      },
    ]);
    if (isPostToX)
      handleGifUploadToX(gif.media_formats.gif.url);
  };

  const handlePostToXToggle = async (checked: boolean) => {
    if (isUploading || isUploadingToX) return;

    setIsPostToX(checked);
    if (checked) {
      setIsPrivate(false);

      if (files.length > 0 && !xMediaIds.length) {
        try {
          setIsUploadingToX(true);
          const fileToUpload = await getFileFromUrl(files[0].url);

          const res = await uploadFileToXAPI(fileToUpload, (progress) => {
            setProgress(progress);
          });

          if (res.success) {
            setXMediaIds([res.mediaId]);
          } else {
            toast.danger(res.error || "Failed to upload media on X");
            setIsPostToX(false);
          }
        } catch (error) {
          console.error(error);
          toast.danger("Failed to upload media on X");
          setIsUploadingToX(false);
          setIsPostToX(false);
        } finally {
          setIsUploadingToX(false);
        }
      }
    }
  };

  const handleGifUploadToX = async (url: string) => {

    setIsUploadingToX(true);
    try {
      const fileToUpload = await getFileFromUrl(url);

      if (fileToUpload.fileSize > 15 * 1024 * 1024) {
        toast.danger("X upload error: Uploaded gif cannot exceed 15 MB");
        setIsPostToX(false);
        return;
      }

      const res = await uploadFileToXAPI(fileToUpload, (progress) => {
        setProgress(progress);
      });

      if (res.success) {
        setXMediaIds([res.mediaId]);
      } else {
        toast.danger(res.error || "Failed to upload on X");
        setIsPostToX(false);
      }
    } catch (error) {
      toast.danger("Failed to upload media on X");
      setIsUploadingToX(false);
      setIsPostToX(false);
      return;
    } finally {
      setIsUploadingToX(false);
    }
  };

  const handlePrivateToggle = () => {
    setIsPrivate((prev) => {
      const next = !prev;
      if (next) setIsPostToX(false);
      return next;
    });
  };
  
  const handlePostToXToggleWrapper = () => {
    if (!(isUploading || isUploadingToX)) {
      handlePostToXToggle(!isPostToX);
    }
  };

  const sheetContent = (
    <View className="space-y-4 px-4">
      <View className="mb-4 flex-row items-center space-x-2">
        <Pressable onPress={handleSheetClose}>
          <ArrowBackOutlineIcon
            width={23}
            height={23}
            color={Colors.dark.offWhite}
          />
        </Pressable>
        <Text className="font-semibold text-[#f3f3f3]">Posting Options</Text>
      </View>

      <TouchableOpacity
        className="flex-row items-center justify-between rounded-[10px] border border-[#636363] p-4"
        activeOpacity={0.8}
        onPress={handlePrivateToggle}
      >
        <View className="flex-row items-center space-x-1">
          <TicketOutlineIconV2 width={24} height={24} color={Colors.dark.offWhite} />
          <Text className="text-sm font-semibold text-off-white">
            Make post private
          </Text>
        </View>
        <Switch
          trackColor={{ false: "#767577", true: "#EB540A" }}
          thumbColor={isPrivate ? "#ffffff" : "#f4f3f4"}
          ios_backgroundColor="#3e3e3e"
          value={isPrivate}
          disabled
        />
      </TouchableOpacity>

      <TouchableOpacity
        className="flex-row items-center justify-between rounded-[10px] border border-[#636363] p-4"
        activeOpacity={0.8}
        disabled={isUploading || isUploadingToX}
        onPress={handlePostToXToggleWrapper}
        style={{ opacity: isUploading || isUploadingToX ? 0.5 : 1 }}
      >
        <View className="flex-row items-center space-x-1">
          <TwitterIcon width={24} height={24} color={Colors.dark.offWhite} />
          <Text className="text-sm font-semibold text-off-white">
            Cross post on X
          </Text>
        </View>
        <Switch
          trackColor={{ false: "#767577", true: "#EB540A" }}
          thumbColor={isPostToX ? "#ffffff" : "#f4f3f4"}
          ios_backgroundColor="#3e3e3e"
          value={isPostToX}
          disabled
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <>
    <ThemedView style={styles.actionsWrapper}>
      <ThemedView style={styles.actionButtonWrapper}>
        <ThemedView style={styles.actionButtonWrapper}>
          <ThemedView style={styles.actionButtons}>
            <TouchableOpacity
              onPress={handleChange}
              disabled={files.length > 0 || isUploadDataLoading}
            >
              <ImageOutlineIcon
                height={30}
                width={30}
                color={Colors.dark.offWhite}
                style={{ opacity: files.length > 0 ? 0.5 : 1 }}
              />
            </TouchableOpacity>
            <GIFsModal
              onSelect={handleGifSelect}
              visible={modalVisible}
              onClose={toggleModal}
            />
            <TouchableOpacity onPress={toggleModal} disabled={files.length > 0}>
              <GIFOutlineIcon
                height={30}
                width={30}
                color={Colors.dark.offWhite}
                style={{ opacity: files.length > 0 ? 0.5 : 1 }}
              />
            </TouchableOpacity>
          </ThemedView>
        </ThemedView>
        {type === null && !isQuoteRepost && (
          <ThemedView className="flex-row items-center space-x-2">
            {isPrivate && (
              <TicketOutlineIconV2 width={32} height={32} color={Colors.dark.brandOrange} />
            )}
            {isPostToX && <TwitterIcon width={32} height={32} color={Colors.dark.brandOrange}/>}
            <TouchableOpacity onPress={handleSheetOpen}>
              <AddCircleOutlineIcon width={32} height={32} className="size-8 text-[#E0E0E0]" />
            </TouchableOpacity>
          </ThemedView>
        )}
      </ThemedView>
      {type && type === 'reply' && (
        <Button
          onPress={handleReply}
          disabled={
            isReplyPending ||
            isUploading ||
            ((!content || content.trim() === '') && files.length === 0)
          }
        >
        Reply
        </Button>
      )}

      {type && type === 'quote' && (
        <Button
          onPress={handleQuote}
          disabled={
            isQuotePending ||
            isUploading ||
            ((!content || content.trim() === '') && files.length === 0)
          }
        >
          Reply
        </Button>
      )}

      {!isQuoteRepost && type === null && (
        <Button
          onPress={handlePost}
          disabled={
            isPostPending ||
            isUploading ||
            ((!content || content.trim() === '') && files.length === 0) ||
            isUploadingToX
          }
        >
        Post Now
        </Button>
      )}
    </ThemedView>
    <BottomSheet open={isBottomSheetOpen} setOpen={setBottomSheetOpen} height={250}>
        { sheetContent }
    </BottomSheet>
    </>
  );
};

export default AddPostActionBox;

const styles = StyleSheet.create({
  actionsWrapper: {
    gap: 24,
    padding: 24,
    borderTopColor: Colors.dark.grey,
    borderTopWidth: 1,
    paddingTop: 16,
  },
  actionButtonWrapper: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 16,
  },
  switchWrapper: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 8,
  },
  switchText: {
    fontSize: 12,
    fontWeight: "700",
    color: Colors.dark.offWhite,
  },
});
