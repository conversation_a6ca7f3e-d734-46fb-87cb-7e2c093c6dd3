import { useMemo, useState } from "react";

import { z } from "zod";
import { StyleSheet, View } from "react-native";
import { Controller, useForm } from "react-hook-form";

import { useUser } from "@/stores";
import { toast } from "@/components/toast";
import { Colors } from "@/constants/Colors";
import Dropdown from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { formatEther, formatUnits } from "@/utils";
import { ThemedText } from "@/components/ThemedText";
import { AVAX, tokens } from "@/environments/tokens";
import { useChainWithdrawMutation } from "@/queries";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { CustomTextInput } from "@/components/ui/text-input";
import { useSolanaAddressQuery } from "@/queries/chain-queries";
import { useBalancesQuery, useSolanaBalanceQuery } from "@/queries/balance-queries";

export const WalletWithdraw = () => {
  const [token, setToken] = useState<{
    name: string;
    icon: string;
    native?: boolean;
  }>(AVAX);
  const { user } = useUser();

  const queryClient = useQueryClient();
  const { data: solanaAddressData } = useSolanaAddressQuery();
  const { data: balances } = useBalancesQuery({
    address: user?.address,
  });
  const { data: solanaBalanceData } = useSolanaBalanceQuery({
    address: solanaAddressData?.response.solanaAddress,
  });

  const balance = useMemo(() => {
    return {
      AVAX: balances ? formatEther(balances.AVAX.toString()) ?? "0.00" : "0.00",
      COQ: balances ? formatEther(balances.COQ.toString()) ?? "0.00" : "0.00",
      GURS: balances ? formatEther(balances.GURS.toString()) ?? "0.00" : "0.00",
      NOCHILL: balances
        ? formatEther(balances.NOCHILL.toString()) ?? "0.00"
        : "0.00",
      MEAT: balances
        ? formatUnits(balances.MEAT.toString(), 6) ?? "0.00"
        : "0.00",
      KIMBO: balances
        ? formatEther(balances.KIMBO.toString()) ?? "0.00"
        : "0.00",
      SOL:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.SOL.toString() ?? "0.00"
          : "0.00",
      Bonk:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.Bonk?.toString() ?? "0.00"
          : "0.00",
      $WIF:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.$WIF?.toString() ?? "0.00"
          : "0.00",
      USEDCAR:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.USEDCAR?.toString() ?? "0.00"
          : "0.00",
      Moutai:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.Moutai?.toString() ?? "0.00"
          : "0.00",
      HARAMBE:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.HARAMBE?.toString() ?? "0.00"
          : "0.00",
      JOE: balances ? formatEther(balances.JOE.toString()) ?? "0.00" : "0.00",
      TECH: balances ? formatEther(balances.TECH.toString()) ?? "0.00" : "0.00",
    };
  }, [balances, solanaBalanceData, solanaAddressData]);

  const { mutateAsync: withdraw, isPending } = useChainWithdrawMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["shares"],
      });
      queryClient.invalidateQueries({
        queryKey: ["wallet"],
      });
      toast.green("Withdrawal successful!");
      form.reset();
    },
  });

  const isSolanaCurrency = [
    "SOL",
    "Bonk",
    "$WIF",
    "USEDCAR",
    "Moutai",
    "HARAMBE",
  ].includes(token.name);

  const WithdrawInput = z.object({
    currency: z.string(),
    withdrawAddress: z.string().min(1, {
      message: "Withdraw address is required",
    }),
    withdrawAmount: z
      .string()
      .min(1, {
        message: "Withdraw amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Withdraw amount must be a number",
      })
      .refine(
        (v) =>
          isSolanaCurrency
            ? true
            : parseFloat(v.replace(/,/g, "")) <=
              parseFloat(
                balance[token.name as keyof typeof balance].replace(/,/g, "")
              ),
        {
          message: "Insufficient balance",
        }
      ),
  });
  type WithdrawInputType = z.infer<typeof WithdrawInput>;

  const form = useForm<WithdrawInputType>({
    defaultValues: {
      currency: AVAX.name,
    },
    resolver: zodResolver(WithdrawInput),
    reValidateMode: "onChange",
  });

  const onSubmit = async (data: WithdrawInputType) => {
    await withdraw({
      currency: data.currency,
      withdrawAddress: data.withdrawAddress,
      withdrawAmount: data.withdrawAmount.replace(/,/g, ""),
    });
  };

  const defaultValue = {
    name: "AVAX",
    icon: require("@assets/coins/avax.png"),
  };

  return (
    <View style={styles.container}>
      <ThemedText style={styles.sheetHeader}>Withdraw</ThemedText>
      <View>
        <ThemedText style={styles.inputLabel}>
          BALANCE: {balance[token.name as keyof typeof balance]}
        </ThemedText>
        <Controller
          name="currency"
          control={form.control}
          render={({ field: { name, onChange, value } }) => {
            return (
              <Dropdown
                onChange={({ name }) => {
                  setToken(tokens.find((token) => token.name === name) ?? AVAX);
                  onChange(value);
                }}
                placeholder="AVAX"
                value={value}
                data={tokens}
                defaultValue={defaultValue}
              />
            );
          }}
        />
      </View>

      <Controller
        name="withdrawAddress"
        control={form.control}
        render={({ field: { name, onChange, value, onBlur } }) => {
          return (
            <CustomTextInput
              placeholder="Enter wallet address"
              labelText="WALLET ADDRESS"
              onChangeText={(text) => {
                onChange(text);
              }}
              errorMessage={form.formState.errors.withdrawAddress?.message}
            />
          );
        }}
      />
      <Controller
        name="withdrawAmount"
        control={form.control}
        render={({ field: { name, onChange, value, onBlur } }) => {
          return (
            <CustomTextInput
              value={value}
              onChangeText={(text) => {
                let value: string | number = text;
                if (value === "") {
                  onChange(value);
                  return;
                }
                if (!value.includes(".")) {
                  value = parseFloat(value.replace(/,/g, ""));
                  if (isNaN(value)) return;
                  value = numberFormatter.format(value);
                }
                onChange(value);
              }}
              onBlur={onBlur}
              errorMessage={form.formState.errors.withdrawAmount?.message}
              placeholder="Enter Amount"
              labelText="AMOUNT TO WITHDRAW"
            />
          );
        }}
      />
      <Button onPress={form.handleSubmit(onSubmit)} >Withdraw</Button>
    </View>
  );
};

const numberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 20,
});

const styles = StyleSheet.create({
  container: {
    gap: 17,
  },
  sheetHeader: {
    marginBottom: 10,
  },
  inputLabel: {
    color: Colors.dark.lightgrey,
    fontSize: 12,
    marginBottom: 10,
  },
});
