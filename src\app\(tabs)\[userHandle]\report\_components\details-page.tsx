import { View, Text, TextInput } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { reportTypes } from "./reason-page";

export const DetailsPage = ({
  reason,
  description,
  setDescription,
  maxDescriptionLength,
}: {
  reason: string | null;
  description: string;
  setDescription: (value: string) => void;
  maxDescriptionLength: number;
}) => (
  <View className="px-6">
    <View className="flex flex-col gap-2 rounded-2xl bg-gray-bg p-4">
      <ThemedText type="bold" className="text-lg text-off-white">
        {reason}
      </ThemedText>
      <ThemedText className="text-base  text-gray-text">
        {reportTypes.find((t) => t.reason === reason)?.description}
      </ThemedText>
    </View>
    <View className="mt-8">
      <ThemedText type="bold" className="mb-4 text-sm text-white">DESCRIPTION</ThemedText>
      <TextInput
        value={description}
        onChangeText={setDescription}
        placeholder="Do you want to add more context?"
        placeholderTextColor="#666"
        maxLength={maxDescriptionLength}
        multiline
        className="bg-chat-bubble rounded-lg border border-gray-text p-4 text-white text-base"
      />
    </View>
  </View>
);
