import { View, Pressable } from "react-native";
import { ThemedText } from "@/components/ThemedText";

export const Footer = ({
  page,
  reason,
  description,
  descriptionLength,
  maxDescriptionLength,
  isReportUserPending,
  isReportThreadPending,
  setPage,
  handleReport,
}: {
  page: string;
  reason: string | null;
  description: string;
  descriptionLength: number;
  maxDescriptionLength: number;
  isReportUserPending: boolean;
  isReportThreadPending: boolean;
  setPage: (page: "reason" | "details" | "submit") => void;
  handleReport: (blockUser: boolean) => void;
}) => (
  <View className="bg-dark-bk px-6 py-4">
    {page === "reason" ? (
      <Pressable
        className={`w-full rounded-full border border-gray-text py-3 ${!reason ? 'opacity-50' : ''}`}
        disabled={!reason}
        onPress={() => setPage("details")}
      >
        <ThemedText type="bold" className="text-lg text-center text-white ">Next</ThemedText>
      </Pressable>
    ) : page === "details" ? (
      <Pressable
        className={`w-full rounded-full border border-gray-text py-3 ${
          !reason ||
          (reason === "Other" && !description) ||
          descriptionLength > maxDescriptionLength
            ? 'opacity-50'
            : ''
        }`}
        disabled={
          !reason ||
          (reason === "Other" && !description) ||
          descriptionLength > maxDescriptionLength
        }
        onPress={() => setPage("submit")}
      >
        <ThemedText type="bold" className="text-lg text-center text-white">Next</ThemedText>
      </Pressable>
    ) : (
      <Pressable
        className={`w-full rounded-full bg-brand-orange border py-3 ${
          !reason ||
          (reason === "Other" && !description) ||
          isReportUserPending ||
          isReportThreadPending
            ? 'opacity-50'
            : ''
        }`}
        disabled={
          !reason ||
          (reason === "Other" && !description) ||
          isReportUserPending ||
          isReportThreadPending
        }
        onPress={() => handleReport(false)}
      >
        <ThemedText type="bold" className="text-lg text-center text-white">Done</ThemedText>
      </Pressable>
    )}
  </View>
);
