import * as SecureStore from 'expo-secure-store';

interface CookieData {
  value: string;
  expiry: string;
}

export async function setCookie(key: string, value: string, expiryDate: Date) {
  const expiry = expiryDate.toISOString(); 
  const data: CookieData = { value, expiry };
  try {
    await SecureStore.setItemAsync(key, JSON.stringify(data));
  } catch (error) {
    console.error('Error setting cookie', error);
  }
}

export async function getCookie(key: string) {
  try {
    const result = await SecureStore.getItemAsync(key);
    if (result) {
      const data: CookieData = JSON.parse(result);
      const expiryDate = new Date(data.expiry);
      if (Date.now() < expiryDate.getTime()) {
        return data;
      } else {
        await SecureStore.deleteItemAsync(key);
        return null;
      }
    }
    return null;
  } catch (error) {
    console.error('Error getting cookie', error);
  }
}

export async function removeCookie(key: string) {
  try {
    await SecureStore.deleteItemAsync(key);
  } catch (error) {
    console.error('Error removing cookie', error);
  }
}
