import React from "react";
import { View, Modal, Pressable } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { useBlockUserMutation, useUnblockUserMutation } from "@/queries/user-mutations";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "@/components/toast";

interface BlockUnblockConfirmSheetProps {
  open: boolean;
  mode: "block" | "unblock";
  username: string;
  userId: string;
  onClose: () => void;
}

const BlockUnblockConfirmSheet = ({
  open,
  mode,
  username,
  userId,
  onClose,
}: BlockUnblockConfirmSheetProps) => {
  const isBlock = mode === "block";
  const queryClient = useQueryClient();
  const blockMutation = useBlockUserMutation({
    onSuccess: () => {
      toast.green("User blocked");
      queryClient.invalidateQueries({ queryKey: ["user", "isBlocked", userId] });
      onClose();
    },
    onError: () => {
      toast.danger("Failed to block user");
    }
  });
  const unblockMutation = useUnblockUserMutation({
    onSuccess: () => {
      toast.green("User unblocked");
      queryClient.invalidateQueries({ queryKey: ["user", "isBlocked", userId] });
      onClose();
    },
    onError: () => {
      toast.danger("Failed to unblock user");
    }
  });
  const handleConfirm = () => {
    if (isBlock) {
      blockMutation.mutate({ userId });
    } else {
      unblockMutation.mutate({ userId });
    }
  };
  return (
    <Modal
      visible={open}
      animationType="slide"
      transparent
      onRequestClose={onClose}
    >
      <Pressable 
        className="flex-1 bg-black/50" 
        onPress={onClose}
      >
        <View className="flex-1 justify-end">
          <Pressable>
            <View className="bg-gray-bg rounded-t-xl px-6 pb-9 pt-6 border-t border-x border-[#444]">
              <ThemedText type="bold" className="text-lg text-center text-white mb-3">
                {isBlock ? `Block @${username}?` : `Unblock @${username}?`}
              </ThemedText>
              <ThemedText className="text-sm text-gray-text text-center mb-7">
                {isBlock
                  ? `@${username} will not be able to follow you or view your posts, and you will not see posts or notifications from @${username}. Are you sure you want to proceed?`
                  : `@${username} will be able to follow you and view your posts. Are you sure you want to proceed?`}
              </ThemedText>
              <View className="flex-row gap-3">
                <Pressable
                  className="flex-1 bg-[#232323] border border-[#444] rounded-full py-3 items-center"
                  onPress={onClose}
                >
                  <ThemedText type="bold" className="text-white">Cancel</ThemedText>
                </Pressable>
                <Pressable
                  className={`flex-1 rounded-full py-3 items-center ${isBlock ? 'bg-brand-orange' : 'bg-white'}`}
                  onPress={handleConfirm}
                >
                  <ThemedText type="bold" className={isBlock ? "text-white" : "text-dark-bk"}>
                    {isBlock ? "Block" : "Unblock"}
                  </ThemedText>
                </Pressable>
              </View>
            </View>
          </Pressable>
        </View>
      </Pressable>
    </Modal>
  );
};

export default BlockUnblockConfirmSheet;
