import { Colors } from "@/constants/Colors";
import { View, Modal, StyleSheet, TouchableOpacity } from "react-native";

type TCenterModalProps = {
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    children: React.ReactNode;
}

export const CenterModal = ({ isOpen, setIsOpen, children }: TCenterModalProps) => {
    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={isOpen}
            onRequestClose={() => {
                setIsOpen(!isOpen);
            }}>
            <TouchableOpacity activeOpacity={1} style={styles.overlay} onPress={() => setIsOpen(false)}>
                <View style={styles.customModalContainer}>
                    {children}
                </View>
            </TouchableOpacity>
        </Modal>
    )
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
    },
    customModalContainer: {
        backgroundColor: Colors.dark.secondaryBackground,
        paddingVertical: 20,
        paddingHorizontal: 20,
        elevation: 5,
        width: '92%',
        borderRadius: 16,
        borderColor: 'rgba(59,59,59,0.30)', // border color
        borderWidth: 1, // border width (adjust as necessary)
        shadowColor: 'rgba(255,255,255,0.75)', // shadow color for iOS
        shadowOffset: { width: 0, height: 0 }, // shadow offset for iOS
        shadowOpacity: 0.25, // shadow opacity for iOS
        shadowRadius: 10, // shadow radius for iOS
    }
})