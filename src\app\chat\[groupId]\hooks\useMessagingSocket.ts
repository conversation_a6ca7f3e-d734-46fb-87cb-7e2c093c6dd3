import { useEffect } from "react";
import { useSocket } from "@/stores/socket";
import { Undefined } from "@/types/common";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { ChatMessagesResponse, MessageType, Reaction } from "@/queries/types/chats";
import { InfiniteData } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

export const useMessagingSocket = (
  groupId: Undefined<string>, 
  messageId: string, 
  isAtStart: boolean, 
  onScrollToBottom: () => void
) => {
    const { socket, setSeen } = useSocket();
    const queryClient = useQueryClient();

    useEffect(() => {
        if (socket && groupId) {
            socket.emit("subscribe", `group-${groupId}`);
        }

        return () => {
            if (socket && groupId) {
                socket.emit("unsubscribe", `group-${groupId}`);
            }
        };
    }, [socket, groupId]);

    useEffect(() => {
        socket?.on(
            SOCKET_MESSAGE.CHAT_MESSAGE,
            async (data: { message: MessageType }) => {
                if (!data.message) return;

                if (data.message.groupId !== groupId) return;

                queryClient.setQueryData(
                    [
                        "chat",
                        "group-infinite-messages",
                        {
                            groupId: groupId,
                            messageId,
                        },
                    ],
                    (
                        old: InfiniteData<{
                            messages: MessageType[];
                        }>,
                    ) => {
                        if (!old) return old;

                        return {
                            ...old,
                            pages: old.pages.map((page, index) => {
                                if (index === 0) {
                                    //check if the message is same message that we optimistically updated
                                    const indexToReplace = page.messages.findIndex((m) =>
                                        m.id.includes("remove"),
                                    );
                                    if (indexToReplace !== -1) {
                                        //if yes, replace the message with the new updated message
                                        page.messages[indexToReplace] = { ...data.message, id: data.message.id.replace("remove-", "") };
                                        return {
                                            ...page,
                                            messages: [...page.messages],   
                                        };
                                    }
                                    //if not, add the new message to the start of the page
                                    return {
                                        ...page,
                                        messages: [data.message, ...page.messages],
                                    };
                                }
                                return page;
                            }),
                        };
                    },
                );
                if (isAtStart) {
                  onScrollToBottom();
                }
                setSeen(groupId, Date.now());

                queryClient.invalidateQueries({
                    queryKey: ["chat", "conversations"],
                });
                queryClient.invalidateQueries({
                    queryKey: ["chat", "direct-messages"],
                });
            },
        );

        socket?.on(SOCKET_MESSAGE.CHAT_MESSAGE_REACTION, async (data: Reaction) => {
            queryClient.setQueryData(
              [
                "chat",
                "group-infinite-messages",
                { groupId, messageId },
              ],
              (old: InfiniteData<ChatMessagesResponse>) => {
                if (!old) return old;
      
                return {
                  ...old,
                  pages: old.pages.map((page, index) => {
                    if (
                      page.messages.some((message) => message.id === data.messageId)
                    ) {
                      return {
                        ...page,
                        messages: page.messages.map((message) => {
                          if (message.id === data.messageId) {
                            const filteredReactions = message.reactions?.filter(
                              (reaction) => reaction.userId !== data.userId,
                            );
                            return {
                              ...message,
                              reactions: [...filteredReactions, data],
                            };
                          }
                          return message;
                        }),
                      };
                    }
      
                    return page;
                  }),
                };
              },
            );
          });

          socket?.on(SOCKET_MESSAGE.CHAT_MESSAGE_REACTION_ADD, async (data: Reaction) => {
            queryClient.setQueryData(
              [
                "chat",
                "group-infinite-messages",
                { groupId, messageId },
              ],
              (old: InfiniteData<ChatMessagesResponse>) => {
                if (!old) return old;
      
                return {
                  ...old,
                  pages: old.pages.map((page, index) => {
                    if (
                      page.messages.some((message) => message.id === data.messageId)
                    ) {
                      return {
                        ...page,
                        messages: page.messages.map((message) => {
                          if (message.id === data.messageId) {
                            const filteredReactions = message.reactions?.filter(
                              (reaction) => reaction.userId !== data.userId,
                            );
                            return {
                              ...message,
                              reactions: [...filteredReactions, data],
                            };
                          }
                          return message;
                        }),
                      };
                    }
      
                    return page;
                  }),
                };
              },
            );
          });
          socket?.on(SOCKET_MESSAGE.CHAT_MESSAGE_REACTION_DELETE, async (data: Reaction) => {
            queryClient.setQueryData(
              [
                "chat",
                "group-infinite-messages",
                { groupId, messageId },
              ],
              (old: InfiniteData<ChatMessagesResponse>) => {
                if (!old) return old;
      
                return {
                  ...old,
                  pages: old.pages.map((page, index) => {
                    if (
                      page.messages.some((message) => message.id === data.messageId)
                    ) {
                      return {
                        ...page,
                        messages: page.messages.map((message) => {
                          if (message.id === data.messageId) {
                            const filteredReactions = message.reactions?.filter(
                              (reaction) => reaction.userId !== data.userId,
                            );
                            return {
                              ...message,
                              reactions: filteredReactions,
                            };
                          }
                          return message;
                        }),
                      };
                    }
      
                    return page;
                  }),
                };
              },
            );
          });

        return () => {
            if(groupId) setSeen(groupId, Date.now());
            socket?.off(SOCKET_MESSAGE.CHAT_MESSAGE);
        };
    }, [
        groupId,
        queryClient,
        socket,
        setSeen,
        messageId
    ]);
}   