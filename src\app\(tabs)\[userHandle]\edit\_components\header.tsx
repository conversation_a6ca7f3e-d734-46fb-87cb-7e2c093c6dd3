import { ArrowBackOutlineIcon } from "@/components/icons";
import { Pressable, View, Text, StyleSheet } from "react-native";

export const Header = () => {
  const handleBack = () => {};

  return (
    <View style={styles.header}>
      <View>
        <Pressable onPress={handleBack}>
          <ArrowBackOutlineIcon className="size-5 text-off-white" />
        </Pressable>
      </View>
      <Text>Edit Profile</Text>
      <View />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: 'red'
  }
})