import React from "react";
import { useLayoutEffect, useState } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AxiosError } from "axios";

import { axios } from "@/lib/axios";
import { Me, TwitterUser } from "@/types";
import { UserProvider } from "@/stores/user";
import { toast } from "@/components/toast";
import { SocketProvider } from "@/stores/socket";
import { DarkTheme, ThemeProvider } from "@react-navigation/native";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { BottomSheetProvider } from "@/components/ui/BottomSheetContext";
import { RootSiblingParent } from "react-native-root-siblings";
import { PortalHost, PortalProvider } from "@gorhom/portal";
import { View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";

interface ProvidersProps {
  children: React.ReactNode;
  token: string | null;
  user: Me | null;
  twitterUser: TwitterUser | null;
  setUser: React.Dispatch<React.SetStateAction<any>>;
  setTwitterUser: React.Dispatch<React.SetStateAction<any>>;
  setToken: React.Dispatch<React.SetStateAction<any>>;
}

export function Providers({
  children,
  token,
  user,
  twitterUser,
  setUser,
  setTwitterUser,
  setToken,
}: ProvidersProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnMount: true,
            staleTime: 0,
          },
          mutations: {
            onError: (error) => {
              if (error instanceof AxiosError) {
                console.error(error.response?.data);
                toast.danger(
                  error.response?.data.message ||
                    "An unexpected error occurred. Please try again later."
                );
              }
            },
          },
        },
      })
  );

  useLayoutEffect(() => {
    if (token) {
      axios.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    } else {
      delete axios.defaults.headers.common["Authorization"];
    }
  }, [token]);

  return (
    <QueryClientProvider client={queryClient}>
      <UserProvider
        user={user}
        twitterUser={twitterUser}
        setUser={setUser}
        setTwitterUser={setTwitterUser}
        setToken={setToken}
        token={token}
      >
        <SocketProvider>
          <ThemeProvider value={DarkTheme}>
            <SafeAreaProvider>
              <GestureHandlerRootView style={{ flex: 1 }}>
                <BottomSheetModalProvider>
                  <BottomSheetProvider>
                    <PortalProvider>
                      <View style={{ flex: 1 }}>
                        <PortalHost name="menu" />
                        <RootSiblingParent>{children}</RootSiblingParent>
                      </View>
                    </PortalProvider>
                  </BottomSheetProvider>
                </BottomSheetModalProvider>
              </GestureHandlerRootView>
            </SafeAreaProvider>
          </ThemeProvider>
        </SocketProvider>
      </UserProvider>
    </QueryClientProvider>
  );
}
