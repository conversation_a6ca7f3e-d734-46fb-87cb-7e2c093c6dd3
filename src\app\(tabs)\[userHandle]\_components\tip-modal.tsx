import React from "react";
import { useMemo, useState } from "react";

import { z } from "zod";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { useLocalSearchParams } from "expo-router";
import { Controller, useForm } from "react-hook-form";

import { Thread } from "@/types";
import { useUser } from "@/stores";
import { toast } from "@/components/toast";
import { Colors } from "@/constants/Colors";
import Dropdown from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { formatEther, formatUnits } from "@/utils";
import { ThemedView } from "@components/ThemedView";
import { useTheme } from "@react-navigation/native";
import { ThemedText } from "@/components/ThemedText";
import { AVAX, tokens } from "@/environments/tokens";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQueryClient } from "@tanstack/react-query";
import { useTipMutation } from "@/queries/tip-mutation";
import { XCircleOutlineIcon } from "@/components/icons";
import { CustomTextInput } from "@/components/ui/text-input";
import { useSharesStatsQuery } from "@/queries/shares-queries";
import { useSolanaAddressQuery } from "@/queries/chain-queries";
import {
  useBalancesQuery,
  useSolanaBalanceQuery,
} from "@/queries/balance-queries";
import {
  useIsUserBlockedQuery,
  useUserByHandleQuery,
} from "@/queries/user-queries";
import { InformationCircleOutlineIcon } from "@/components/icons";
import { TippingInfoPopup } from "./TippingInfoPopup";

import ProfileHeaderCard from "./profile-header-card";
import TipTicketsForm from "@components/tip-tickets-form";
import BackButton from "@/components/navigation/BackButton";

interface TipModalProps {
  thread: Thread;
}

// { thread }: TipModalProps =  props for TipModal
const wallets = {
  ARENA: "The Arena",
  PHANTOM: "Phantom",
};

enum TipOption {
  TipTokens = "TipTokens",
  TipTickets = "TipTickets",
}

export const TipModal = () => {
  const { colors, dark } = useTheme();

  let ColorScheme = dark ? "dark" : "light";

  const [selectedValue, setSelectedValue] = useState<string | undefined>();

  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [token, setToken] = useState<{
    name: string;
    icon: string;
    native?: boolean;
  }>(AVAX);
  const [wallet, setWallet] = useState(wallets.ARENA);
  const { user } = useUser();

  const params = useLocalSearchParams() as { userHandle: string };

  const { data } = useUserByHandleQuery(params.userHandle);
  const { data: isBlocked } = useIsUserBlockedQuery(data?.user?.id || "");

  const { data: statsData } = useSharesStatsQuery({
    userId: data?.user?.id,
  });

  const { data: balances } = useBalancesQuery({
    address: user?.address,
  });
  const { data: solanaAddressData } = useSolanaAddressQuery();
  const { data: solanaBalanceData } = useSolanaBalanceQuery({
    address: solanaAddressData?.response.solanaAddress,
  });

  const provider =
    typeof window !== "undefined" && (window as any).phantom?.solana;

  const balance = useMemo(() => {
    return {
      AVAX: balances ? formatEther(balances.AVAX.toString()) ?? "0.00" : "0.00",
      COQ: balances ? formatEther(balances.COQ.toString()) ?? "0.00" : "0.00",
      GURS: balances ? formatEther(balances.GURS.toString()) ?? "0.00" : "0.00",
      NOCHILL: balances
        ? formatEther(balances.NOCHILL.toString()) ?? "0.00"
        : "0.00",
      MEAT: balances
        ? formatUnits(balances.MEAT.toString(), 6) ?? "0.00"
        : "0.00",
      KIMBO: balances
        ? formatEther(balances.KIMBO.toString()) ?? "0.00"
        : "0.00",
      JOE: balances ? formatEther(balances.JOE.toString()) ?? "0.00" : "0.00",
      TECH: balances ? formatEther(balances.TECH.toString()) ?? "0.00" : "0.00",
      SOL:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.SOL.toString() ?? "0.00"
          : "0.00",
      Bonk:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.Bonk?.toString() ?? "0.00"
          : "0.00",
      $WIF:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.$WIF?.toString() ?? "0.00"
          : "0.00",
      USEDCAR:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.USEDCAR?.toString() ?? "0.00"
          : "0.00",
      Moutai:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.Moutai?.toString() ?? "0.00"
          : "0.00",
      HARAMBE:
        solanaAddressData?.response.solanaAddress && solanaBalanceData
          ? solanaBalanceData.splTokens.HARAMBE?.toString() ?? "0.00"
          : "0.00",
    };
  }, [balances, solanaBalanceData, solanaAddressData]);

  const isSolanaCurrency = [
    "SOL",
    "Bonk",
    "$WIF",
    "USEDCAR",
    "Moutai",
    "HARAMBE",
  ].includes(token.name);

  const showWarning =
    isSolanaCurrency && !provider && wallet === wallets.PHANTOM;

  const TipInput = z.object({
    currency: z.string(),
    tipAmount: z
      .string()
      .min(1, {
        message: "Tip amount is required",
      })
      .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
        message: "Tip amount must be a number",
      })
      .refine(
        (v) =>
          isSolanaCurrency
            ? true
            : parseFloat(v.replace(/,/g, "")) <=
              parseFloat(
                balance[token.name as keyof typeof balance].replace(/,/g, "")
              ),
        {
          message: "Insufficient balance",
        }
      ),
  });
  type TipInputType = z.infer<typeof TipInput>;

  const form = useForm<TipInputType>({
    defaultValues: {
      currency: AVAX.name,
      tipAmount: "",
    },
    resolver: zodResolver(TipInput),
    reValidateMode: "onChange",
  });

  const { mutateAsync: tip, isPending } = useTipMutation({
    onSuccess: () => {
      toast.green("Tip sent!");
      setOpen(false);
      form.reset();
      setWallet(wallets.ARENA);
      setToken(AVAX);
      queryClient.invalidateQueries({
        queryKey: ["wallet", "balances", user?.address],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "wallet",
          "solana_balance",
          solanaAddressData?.response.solanaAddress,
        ],
      });
    },
  });

  const onSubmit = async (values: TipInputType) => {
    if (!data) return;
    const tipAmount = values.tipAmount.replace(/,/g, "");

    await tip({
      currency: values.currency,
      tipAmount,
      userId: data?.user.id,
      wallet: isSolanaCurrency ? wallet : wallets.ARENA,
    });
  };

  const defaultCoinValue = {
    name: "AVAX",
    icon: require("@assets/coins/avax.png"),
    native: true,
  };

  const defaultWalletValue = {
    name: wallets.ARENA,
  };

  const walletsDropdownData = [
    {
      name: wallets.ARENA,
    },
    {
      name: wallets.PHANTOM,
    },
  ];

  const [isTippingInfoOpen, setIsTippingInfoOpen] = useState(false);
  const [option, setOption] = useState<TipOption>(TipOption.TipTokens);

  return (
    <ThemedView className="px-5 justify-center h-full gap-y-4">
      <BackButton />
      <ThemedView className="mb-4">
        <ThemedText className="text-brand-lightgrey text-lg font-extrabold">Tip</ThemedText>
        <ThemedText className="font-bold text-3xl" type="subtitle">
          {data?.user?.twitterName}
        </ThemedText>
      </ThemedView>
      <ProfileHeaderCard
        totalTicketHolders={statsData?.totalHolders}
        data={data}
      />

      <View className="flex-row mb-2">
        <TouchableOpacity
          className={`flex-1 h-20 p-3 rounded-lg mr-1 justify-center ${option === TipOption.TipTokens ? 'bg-[#F36A27]' : 'bg-dark-bk border border-[#B5B5B5]'}`}
          onPress={() => setOption(TipOption.TipTokens)}
        >
          <Text className="text-white text-center ">Tip Tokens</Text>
        </TouchableOpacity>
        <TouchableOpacity
          className={`flex-1 h-20 p-3 rounded-lg ml-1 justify-center ${option === TipOption.TipTickets ? 'bg-[#F36A27]' : 'bg-black border border-[#B5B5B5]'}`}
          onPress={() => setOption(TipOption.TipTickets)}
        >
          <Text className="text-white text-center">Tip Tickets</Text>
        </TouchableOpacity>
      </View>

      {option === TipOption.TipTokens ? (
        <View className="gap-y-2">
          <Controller
            name="currency"
            control={form.control}
            render={({ field: { name, onChange, value } }) => {
              return (
                <ThemedView className="mb-2">
                  <ThemedText className="text-xs text-brand-lightgrey mb-1">
                    BALANCE: {balance[token.name as keyof typeof balance]}
                  </ThemedText>
                  <Dropdown
                    data={tokens}
                    onChange={(value) => {
                      setToken(
                        tokens.find((token) => token.name === value.name) ?? AVAX
                      );
                      onChange(value);
                    }}
                    value={value}
                    placeholder="AVAX"
                    defaultValue={defaultCoinValue}
                    balance={balance}
                  />
                </ThemedView>
              );
            }}
          />
          {isSolanaCurrency && (
            <ThemedView className="mb-2">
              <ThemedText className="text-xs text-brand-lightgrey mb-1">Wallet</ThemedText>
              <Dropdown
                data={walletsDropdownData}
                onChange={(e) => {
                  setWallet(e.name);
                }}
                placeholder="AVAX"
                defaultValue={defaultWalletValue}
              />
            </ThemedView>
          )}
          <ThemedView className="mb-2">
            <Controller
              name="tipAmount"
              control={form.control}
              render={({ field: { name, onChange, value, onBlur, ref } }) => {
                return (
                  <CustomTextInput
                    labelText="Tip amount"
                    placeholder="How much do you want to tip?"
                    value={value}
                    onChangeText={(text) => {
                      let value: string | number = text;
                      if (value === "") {
                        onChange(value);
                        return;
                      }

                      if (!value.includes(".")) {
                        value = parseFloat(value.replace(/,/g, ""));
                        if (isNaN(value)) return;
                        value = numberFormatter.format(value);
                      }

                      onChange(value);
                    }}
                    onBlur={onBlur}
                    errorMessage={form.formState.errors.tipAmount?.message}
                    inputStyle={{ paddingRight: 64 }}
                    IconRight={
                      <TouchableOpacity
                        onPress={() => {
                          const max = balance[token.name as keyof typeof balance] || "0";
                          form.setValue("tipAmount", max);
                        }}
                        className="bg-black rounded px-2 py-0.5 mr-1"
                      >
                        <Text className="text-white underline font-bold">Max</Text>
                      </TouchableOpacity>
                    }
                  />
                );
              }}
            />
          </ThemedView>
          {!showWarning && (
            <Button onPress={form.handleSubmit(onSubmit)}  disabled={!form.watch('tipAmount') } style={{ marginTop: 20 }}  >Send tip</Button>
          )}
          {showWarning && (
            <View className="flex w-full items-center gap-2.5 rounded-lg border border-[#BE5D5D] bg-[#D14848] px-4 py-3">
              <XCircleOutlineIcon
                height={6}
                width={6}
                fill={Colors.dark.offWhite}
              />
              <Text className="text-xs leading-5 text-off-white">
                No Phantom wallet detected. Please install the Phantom wallet
                extension or use Phantom wallet browser on mobile
              </Text>
            </View>
          )}
        </View>
      ) : (
        <TipTicketsForm />
      )}
      <TouchableOpacity className="-bottom-20 w-full items-center" activeOpacity={0.8} onPress={() => setIsTippingInfoOpen(true)}>
        <View className="flex-row items-center justify-center">
          <InformationCircleOutlineIcon color={Colors.dark.offWhite} height={25} width={25} className="mr-1" />
          <ThemedText type="bold" className="text-off-white text-base underline">
            Tipping Information
          </ThemedText>
        </View>
      </TouchableOpacity>
      <TippingInfoPopup isOpen={isTippingInfoOpen} setIsOpen={setIsTippingInfoOpen} />
    </ThemedView>
  );
};

const numberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 20,
});
