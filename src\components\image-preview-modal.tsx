import React, { useState } from "react";
import { View, Modal, TouchableOpacity, StyleSheet } from "react-native";
import { CloseOutlineIcon } from "./icons";

// Props: ( url: string; children: React.ReactNode )
import { ReactNode } from "react";

interface ImagePreviewModalProps {
  children: ReactNode;
  url: string;
}

export const ImagePreviewModal = ({
  url,
  children,
}: ImagePreviewModalProps) => {
  const [open, setOpen] = useState(false);

  return (
    <>
      {/* Trigger to open the modal */}
      <TouchableOpacity onPress={() => setOpen(true)} activeOpacity={0.8}>
        {children}
      </TouchableOpacity>

      {/* Full screen modal */}
      <Modal
        transparent
        visible={open}
        animationType="fade"
        onRequestClose={() => setOpen(false)}
      >
        {/* Overlay to close if user taps outside */}
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={() => setOpen(false)}
        >
          {/* Inner container to prevent closing when the image area is tapped */}
          <TouchableOpacity
            style={styles.contentContainer}
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
          >
            {/*             <ImagePreview url={url} />
             */}
            {/* Close button in the top-left corner */}
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setOpen(false)}
            >
              <CloseOutlineIcon style={styles.closeIcon} />
            </TouchableOpacity>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.75)",
    // The user taps anywhere on this overlay to close the modal
    justifyContent: "center",
    alignItems: "center",
  },
  contentContainer: {
    // content container for the image
    // tapping here won't close the modal
    position: "relative",
  },
  closeButton: {
    position: "absolute",
    top: 16,
    left: 16,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.65)",
    alignItems: "center",
    justifyContent: "center",
  },
  closeIcon: {
    // If needed, style the icon color, size, etc.
  },
});
