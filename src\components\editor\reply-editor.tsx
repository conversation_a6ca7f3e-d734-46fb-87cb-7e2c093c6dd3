import React, { useMemo, useState } from "react";
import { View, Text, TextInput, Image, TouchableOpacity } from "react-native";
import { Video, ResizeMode } from "expo-av";
import * as ImagePicker from "expo-image-picker";
import { useLocalSearchParams } from "expo-router";
import { v4 } from "uuid";
import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { Button } from "../ui/button";
import { ThemedView } from "../ThemedView";
import { toast } from "../toast";
import { GIFOutlineIcon, ImageOutlineIcon, XCircleOutlineIcon } from "../icons";
import GIFsModal from "@/app/(modals)/addPost/_components/gifs-modal";
import { Colors } from "@/constants/Colors";
import { upload } from "@/utils";
import {
  usePostThreadAnswerMutation,
  useThreadByIdQuery,
  useThreadNestedAnswersInfiniteQuery,
  useTotalUploadedQuery,
} from "@/queries";
import { Thread, ThreadResponse } from "@/types";
import { ThreadsResponse } from "@/queries/types";
import { router } from "expo-router";
import * as FileSystem from "expo-file-system";
import ProgressBar from "../ui/progress-bar";

export function ReplyEditor() {
  const queryClient = useQueryClient();
  const [isFocused, setIsFocused] = useState(false);
  const [files, setFiles] = useState<any[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [preview, setPreview] = useState<{
    url: string;
    type: "video" | "image";
  } | null>(null);
  const [progress, setProgress] = useState(0);
  const [content, setContent] = useState("");
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  const params = useLocalSearchParams() as { userHandle: string; id: string };

  const { data: nestedAnswersData, isLoading: isAnswersLoading } =
    useThreadNestedAnswersInfiniteQuery(params.id);

  const { data } = useThreadByIdQuery(params.id);

  const { data: totalUploaded, isLoading: isUploadDataLoading } =
    useTotalUploadedQuery();

  const usersHandles = useMemo(() => {
    if (!nestedAnswersData || isAnswersLoading) return [];
    return Array.from(
      new Set(
        nestedAnswersData.pages
          .reduce(
            (prev, current) => [...prev, ...(current?.threads ?? [])],
            [] as Thread[]
          )
          .reverse()
          .map((thread) => thread?.user?.twitterHandle)
      )
    );
  }, [nestedAnswersData, isAnswersLoading]);

  const { mutateAsync: postReply, isPending: isReplyPending } =
    usePostThreadAnswerMutation({
      onSuccess: (data) => {
        toast.green("Your post was sent");
        queryClient.setQueryData(
          ["threads", data.thread.id],
          (old: ThreadResponse) => {
            if (!old) return old;
            return {
              ...old,
              thread: {
                ...old.thread,
                answerCount: old.thread.answerCount + 1,
              },
            };
          }
        );
        queryClient.setQueryData(
          ["threads", "answers", data?.thread.id],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) {
              queryClient.invalidateQueries({
                queryKey: ["threads", "answers", data.thread.id],
              });
              return old;
            }
            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return { ...page, threads: [data.thread, ...page.threads] };
                }
                return page;
              }),
            };
          }
        );
        router.push("/(tabs)/home/<USER>");
        setFiles([]);
        setContent("");
        setIsFocused(false);
        resetUploading();
      },
    });

  const resetUploading = () => {
    setIsUploading(false);
    setProgress(0);
    setPreview(null);
  };

  const toggleModal = () => setModalVisible((prevState) => !prevState);

  const handleChange = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 0.8,
      base64: true,
    });

    if (!result.assets?.length) return;
    const file = result.assets[0];

    if (file) {
      if (file.mimeType?.includes("video/ogg")) {
        toast.danger("Please upload video in either mp4 or webm format");
        return;
      }
      if (
        file.mimeType?.includes("image") &&
        !(
          file.mimeType.includes("image/jpeg") ||
          file.mimeType.includes("image/gif") ||
          file.mimeType.includes("image/png")
        )
      ) {
        toast.danger("Please upload image in JPEG, JPG, PNG or GIF format");
        return;
      }
      if (!file.fileSize) {
        const fileInfo = await FileSystem.getInfoAsync(file.uri);
        if (!fileInfo.exists) {
          toast.danger("Please upload a file");
          return;
        }
        file.fileSize = fileInfo.size;
      }
      if (
        file.mimeType?.includes("image") &&
        file.fileSize > 10 * 1024 * 1024
      ) {
        toast.danger("Uploaded image file cannot exceed 10 MB");
        return;
      }
      if (
        file.mimeType?.includes("video") &&
        file.fileSize > 300 * 1024 * 1024
      ) {
        toast.danger("Uploaded video file cannot exceed 300 MB");
        return;
      }
      if (
        file.mimeType?.includes("video") &&
        file.fileSize + (totalUploaded || 0) > 600 * 1024 * 1024
      ) {
        toast.danger(
          "You have reached your daily video upload limit of 600 MB."
        );
        resetUploading();
        return;
      }
      if (!file.fileName) {
        const fileName = file.uri.split("/").pop() || "";
        file.fileName = fileName;
      }

      setPreview({
        url: file.uri,
        type: file?.mimeType?.includes("image") ? "image" : "video",
      });
      setIsUploading(true);
      setProgress(0);

      try {
        const res = await upload({
          file,
          onProgressChange: (progress) => setProgress(progress),
        });
        setFiles([
          {
            id: res.id,
            isLoading: false,
            previewUrl: res.previewUrl,
            url: res.url,
            fileType: file?.mimeType?.includes("image") ? "image" : "video",
            size: file.fileSize,
          },
        ]);
      } catch (error) {
        console.error(error);
        toast.danger("File upload failed");
        resetUploading();
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleGifSelect = (gif: any) => {
    setFiles([
      {
        id: v4(),
        isLoading: false,
        previewUrl: gif.media_formats.gifpreview.url,
        url: gif.media_formats.gif.url,
        fileType: "image",
        size: 0,
      },
    ]);
  };

  const handleReply = async () => {
    if (!data) return;
    if (content.trim() === "" && files.length === 0) return;
    await postReply({
      content,
      threadId: data.thread.id,
      userId: data.thread.userId,
      files,
    });
  };

  const mediaPreview = useMemo(() => {
    if (!(preview || files.length > 0)) return null;

    return (
      <View className="relative w-20 overflow-hidden rounded-md">
        {preview && preview.type === "video" && (
          <Video
            source={{
              uri: isUploading && !files[0]?.url ? preview.url : files[0]?.url,
            }}
            useNativeControls={true}
            resizeMode={ResizeMode.COVER}
            className="w-full h-20"
            isLooping
            shouldPlay
            isMuted
          />
        )}
        {preview && preview.type === "image" && (
          <Image
            className="w-full h-20"
            source={{
              uri: isUploading && !files[0]?.url ? preview.url : files[0]?.url,
            }}
          />
        )}
        {!preview && files.length > 0 && (
          <Image className="w-full h-20" source={{ uri: files[0].url }} />
        )}

        <TouchableOpacity
          onPress={() => {
            setFiles([]);
            resetUploading();
          }}
          className="absolute right-0 top-0 z-10"
        >
          <XCircleOutlineIcon
            fill="#020202BF"
            stroke="#fff"
            height={23}
            width={23}
          />
        </TouchableOpacity>

        {isUploading && (
          <View className="w-full absolute overflow-hidden bottom-0 left-0">
            <ProgressBar progressValue={progress} />
          </View>
        )}
      </View>
    );
  }, [preview, files, isUploading, progress]);

  return (
    <View className="flex flex-col flex-1 border-t border-[#333] bg-black pb-4 pt-2">
      <View className="flex flex-col flex-1 min-w-0 space-y-3 px-6">
        {isFocused && (
          <Text className="truncate text-sm text-[#777]">
            Replying to{" "}
            <Text className="text-[#EB540A]">
              {usersHandles.length <= 3
                ? usersHandles.map(
                    (u, i) => `@${u}${i < usersHandles.length - 1 ? ", " : ""}`
                  )
                : `@${usersHandles[0]}, @${usersHandles[1]} and ${
                    usersHandles.length - 2
                  } others`}
            </Text>
          </Text>
        )}
        {mediaPreview}
        <View className="border border-dashed border-transparent rounded-lg">
          <TextInput
            className="min-h-10 px-2 text-off-white text-base"
            multiline
            onFocus={() => setIsFocused(true)}
            onChangeText={setContent}
            value={content}
            placeholder={isFocused ? "" : "Tap to reply"}
            placeholderTextColor="#888"
          />
        </View>

        {isFocused && (
          <View className="flex flex-row justify-between items-center">
            <ThemedView className="flex flex-row justify-between">
              <ThemedView className="flex flex-row justify-between items-center space-x-4">
                <TouchableOpacity
                  onPress={handleChange}
                  disabled={files.length > 0 || isUploadDataLoading}
                >
                  <ImageOutlineIcon
                    height={30}
                    width={30}
                    color={Colors.dark.offWhite}
                    style={{ opacity: files.length > 0 ? 0.5 : 1 }}
                  />
                </TouchableOpacity>
                <GIFsModal
                  onSelect={handleGifSelect}
                  visible={modalVisible}
                  onClose={toggleModal}
                />
                <TouchableOpacity
                  onPress={toggleModal}
                  disabled={files.length > 0}
                >
                  <GIFOutlineIcon
                    height={30}
                    width={30}
                    color={Colors.dark.offWhite}
                    style={{ opacity: files.length > 0 ? 0.5 : 1 }}
                  />
                </TouchableOpacity>
              </ThemedView>
            </ThemedView>
            <Button
              onPress={handleReply}
              disabled={
                isReplyPending ||
                isUploading ||
                !data ||
                (content.trim() === "" && files.length === 0)
              }
              style={{paddingHorizontal: 32, paddingVertical: 8}}
              >
              Reply
            </Button>
          </View>
        )}
      </View>
    </View>
  );
}
