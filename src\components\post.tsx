import React, { useC<PERSON>back, useMemo, useState } from "react";
import { parseISO } from "date-fns";
import { ResizeMode, Video } from "expo-av";
import * as Clipboard from "expo-clipboard";
import {
  Image,
  Pressable,
  Text,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from "react-native";
import WebView from "react-native-webview";
import { decode } from "html-entities";

import { Href, Link, router, usePathname } from "expo-router";

import { Thread, UserFlaggedEnum } from "@/types";
import { Colors } from "@/constants/Colors";
import { BASE_URL } from "@/constants/constants"
import { usePostStore, useUser } from "@/stores";
import { abbreviateNumber } from "@/utils/abbreviate-number";
import { checkContent, formatTimeDistance, insertSwapLinks } from "@/utils";
import { useIsBlockedByUserQuery, useIsUserBlockedQuery } from "@/queries";
import { useExchangeCurrenciesQuery } from "@/queries/exchange-currencies-query";

import Avatar from "./ui/avatar";
import { Button } from "./ui/button";
import { useBottomSheet } from "./ui/BottomSheetContext";
import { ThemedView } from "./ThemedView";
import { ThemedText } from "./ThemedText";
import { TipModal } from "./tip-modal";
import { ConfirmationSheet } from "./confirmation-sheet";
import { QuotePost } from "@/app/(modals)/addPost/_components/quotePostUI";

import {
  BookmarkFilledIcon,
  BookmarkOutlineIcon,
  ChatBubblesOutlineIcon,
  CopyOutlineIcon,
  EllipsisHorizontalFilledIcon,
  HeartFilledIcon,
  HeartOutlineIcon,
  LinkOutlineIcon,
  MenuAlt2OutlineIcon,
  PinOutlineIcon,
  RepostOutlineIcon,
  TipOutlineIcon,
  TrashOutlineIcon,
} from "./icons";
import { ChartSquareBarOutlineIcon } from "./icons/chart-square-bar-outline";

import { toast } from "./toast";
import CustomRenderHtml from "./custom-render-html";

const postActionIconSize = 20;

type FeedContext = "profile" | "global" | "trending" | null;

interface PostUIProps {
  thread: Thread;
  handleLike: ({ threadId }: { threadId: string }) => void;
  handleBookmark: ({ threadId }: { threadId: string }) => void;
  handleRepost: ({ threadId }: { threadId: string }) => void;
  handleDelete?: ({ threadId }: { threadId: string }) => void;
  handlePin?: ({
    threadId,
    isPinned,
  }: {
    threadId: string;
    isPinned: boolean;
  }) => void;
  onClick?: () => void;
  pinnable?: boolean;
  comment?: boolean;
  feedContext?: FeedContext;
}

const PostUI: React.FC<PostUIProps> = ({
  thread,
  handleBookmark,
  handleLike,
  handleRepost,
  onClick,
  handleDelete,
  handlePin,
  pinnable = false,
  comment = false,
  feedContext,
}) => {
  const { user } = useUser();
  const { data: currencies = [] } = useExchangeCurrenciesQuery();
  const currencySymbols = currencies.map((c) => c.symbol);
  const { width } = useWindowDimensions();

  const isRepost = thread.threadType === "repost";
  const isRepostDeleted = isRepost && !thread.repost;
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const repost = activePost.repost;
  const isQuote = activePost.threadType === "quote" && repost;
  const isQuoteDeleted = activePost.threadType === "quote" && !repost;
  const isAnswer = isRepost && activePost.answerId && activePost.answer;
  const shouldShowPinned =
    pinnable && feedContext === "profile" && thread.isPinned;
  const date = parseISO(activePost.createdDate);

  const [sanitizedContent, isTruncated, youtubeEmbedUrl] = checkContent({
    content: activePost.content,
  });
  const [finalContent] = insertSwapLinks(
    sanitizedContent,
    activePost.id,
    currencySymbols
  );

  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(activePost.repost?.userId);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(activePost.repost?.userId);
  const isSuspended =
    activePost.repost?.user?.flag === UserFlaggedEnum.SUSPENDED;

  const video = React.useRef(null);
  const [vidHeight, setVidHeight] = useState(1);
  const [containerWidth, setContainerWidth] = useState(1);

  const handlePostPress = () => {
    if (onClick) return onClick();
    const path = isAnswer
      ? `/${activePost.user?.twitterHandle}/nested/${activePost.id}`
      : `/${activePost.user?.twitterHandle}/status/${activePost.id}`;
    router.push(path as Href<string>);
  };

  if (isRepostDeleted) {
    return (
      <ThemedView className="flex flex-col border-b border-dark-gray p-6">
        <Link
          href={`/${thread.user?.twitterHandle}` as Href<string>}
          className="mb-3"
        >
          <View className="flex-row items-center space-x-[10px]">
            <RepostOutlineIcon width={20} height={20} color="#878787" />
            <Text className="text-xs text-[#878787]">
              {" "}
              {thread.user?.twitterHandle === user?.twitterHandle
                ? "You"
                : thread.userName}{" "}
              reposted
            </Text>
          </View>
        </Link>
        <DeletedPost />
      </ThemedView>
    );
  }

  return (
    <>
      <Pressable onPress={handlePostPress} style={{ flex: 1 }}>
        <ThemedView className={`flex flex-col border-b border-dark-gray p-6`}>
          {isRepost && (
            <Link
              href={`/${thread.user?.twitterHandle}` as Href<string>}
              className="mb-3"
            >
              <View className="flex-row items-center space-x-[10px]">
                <RepostOutlineIcon width={20} height={20} color="#878787" />
                <Text className="text-xs text-[#878787]">
                  {" "}
                  {thread.user?.twitterHandle === user?.twitterHandle
                    ? "You"
                    : thread.userName}{" "}
                  reposted
                </Text>
              </View>
            </Link>
          )}
          {shouldShowPinned && (
            <View className="mb-3 flex-row items-center space-x-[10px]">
              <PinOutlineIcon width={20} height={20} color="#878787" />
              <Text className="text-xs text-[#878787]">Pinned post</Text>
            </View>
          )}
          <ThemedView className="flex-row space-x-3">
            <View className="flex-shrink-0">
              <Link href={`/${activePost.user?.twitterHandle}/`}>
                <Avatar src={activePost.user.twitterPicture} size={42} />
              </Link>
            </View>
            <ThemedView
              className="flex-1 flex-col space-y-2"
              onLayout={(e) => setContainerWidth(e.nativeEvent.layout.width)}
            >
              <ThemedView className="">
                <ThemedView className="w-full flex-row items-center justify-between space-x-4">
                  <ThemedView className="flex-1 flex-row items-center">
                    <Link
                      href={
                        `/${activePost.user?.twitterHandle}` as Href<string>
                      }
                      onPress={(e) => {
                        e.stopPropagation();
                      }}
                      className="font-semibold text-[#F4F4F4] flex-shrink"
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      <Text>{activePost.userName}</Text>
                    </Link>
                    <ThemedText className="text-sm text-gray-text">
                      ・
                    </ThemedText>
                    <Link
                      href={
                        `/${activePost.user?.twitterHandle}` as Href<string>
                      }
                      onPress={(e) => {
                        e.stopPropagation();
                      }}
                      className="text-[#878787] flex-shrink"
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      <Text>@{activePost.user?.twitterHandle}</Text>
                    </Link>
                    <ThemedText className="text-sm text-gray-text">
                      ・
                    </ThemedText>
                    <ThemedText className="text-sm text-gray-text">
                      {formatTimeDistance(date)}
                    </ThemedText>
                  </ThemedView>
                  <View>
                    {!isRepost &&
                    (user?.id === activePost.userId || user?.isMod) ? (
                      <PostMenuActionsUI
                        thread={activePost}
                        handleDelete={handleDelete}
                        handlePin={handlePin}
                      />
                    ) : (
                      <GeneralPostMenuActionsUI
                        thread={activePost}
                        comment={comment}
                        handleDelete={handleDelete}
                      />
                    )}
                  </View>
                </ThemedView>
                {isAnswer && (
                  <ThemedText className="text-sm leading-4 text-gray-text">
                    Replying to{" "}
                    <Link
                      href={
                        `/${activePost.answer?.user?.twitterHandle}` as Href<string>
                      }
                      className="text-brand-orange flex-shrink"
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      @{activePost.answer?.user?.twitterHandle}
                    </Link>
                  </ThemedText>
                )}
              </ThemedView>
              <ThemedView>
                <CustomRenderHtml html={finalContent} />
                {isTruncated && (
                  <Text className="text-brand-orange">Show more</Text>
                )}
              </ThemedView>

              {activePost.images && activePost.images.length > 0 && (
                <Pressable className="overflow-hidden rounded-2xl">
                  <Image
                    className="w-full"
                    source={{
                      uri: activePost.images[0].url,
                    }}
                    height={180}
                    resizeMode="cover"
                  />
                </Pressable>
              )}

              {activePost.videos && activePost.videos.length > 0 && (
                <Pressable>
                  <Video
                    ref={video}
                    source={{ uri: activePost.videos[0].url }}
                    style={{
                      width: "100%",
                      height: vidHeight * containerWidth,
                      borderRadius: 16,
                    }}
                    useNativeControls={true}
                    resizeMode={ResizeMode.CONTAIN}
                    isLooping
                    shouldPlay
                    isMuted
                    onReadyForDisplay={(e) =>
                      setVidHeight(e.naturalSize.height / e.naturalSize.width)
                    }
                  />
                </Pressable>
              )}

              {youtubeEmbedUrl && (
                <Pressable
                  onPress={() => {}}
                  className="w-full aspect-video rounded-2xl overflow-hidden"
                >
                  <WebView
                    source={{ uri: youtubeEmbedUrl }}
                    javaScriptEnabled
                    domStorageEnabled
                  />
                </Pressable>
              )}

              {isQuote &&
                repost &&
                !isUserBlockedLoading &&
                !isBlockedByUserLoading &&
                (isUserBlocked || isBlockedByUser || isSuspended ? (
                  <View>
                    <QuotePost {...repost} />
                  </View>
                ) : (
                  <Pressable
                    onPress={(e) => {
                      e.stopPropagation();
                      router.push(
                        `/${repost.user?.twitterHandle}/status/${repost.id}` as Href<string>
                      );
                    }}
                  >
                    <QuotePost {...repost} />
                  </Pressable>
                ))}
              {isQuoteDeleted && <DeletedPost />}
              <ThemedView className="mt-1">
                <PostActionsUI
                  thread={activePost}
                  handleLike={handleLike}
                  handleBookmark={handleBookmark}
                  handleRepost={handleRepost}
                  isDisabled={
                    isUserBlocked ||
                    isBlockedByUser ||
                    isUserBlockedLoading ||
                    isBlockedByUserLoading ||
                    isSuspended ||
                    false
                  }
                />
              </ThemedView>
            </ThemedView>
          </ThemedView>
        </ThemedView>
      </Pressable>
    </>
  );
};

export const numberFormatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 2,
});

type MainPostUIProps = PostUIProps & {
  linkType?: "main" | "nested";
};

export const MainPostUI = ({
  thread,
  handleBookmark,
  handleLike,
  handleRepost,
  handleDelete,
  linkType,
  onClick,
}: MainPostUIProps) => {
  const { data: currencies = [] } = useExchangeCurrenciesQuery();
  const currencySymbols = currencies.map((c) => c.symbol);
  const { width } = useWindowDimensions();

  const isRepost = thread.threadType === "repost";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const repost = activePost.repost;
  const isQuote = activePost.threadType === "quote" && repost;
  const isQuoteDeleted = activePost.threadType === "quote" && !repost;
  const date = parseISO(activePost.createdDate);

  const [content, , youtubeEmbedUrl] = checkContent({
    content: activePost.content,
    truncate: false,
  });
  const [contentWithSwapLinks, swapToken] = insertSwapLinks(
    content,
    activePost.id,
    currencySymbols
  );

  const { data: isUserBlocked, isLoading: isUserBlockedLoading } =
    useIsUserBlockedQuery(activePost.repost?.userId);
  const { data: isBlockedByUser, isLoading: isBlockedByUserLoading } =
    useIsBlockedByUserQuery(activePost.repost?.userId);
  const isSuspended =
    activePost.repost?.user?.flag === UserFlaggedEnum.SUSPENDED;

  const handlePress = () => {
    if (!linkType) return;
    if (onClick) return onClick();
    const path =
      linkType === "main"
        ? `/${thread.user?.twitterHandle}/status/${thread.id}`
        : `/${thread.user?.twitterHandle}/nested/${thread.id}`;
    router.push(path as Href<string>);
  };

  return (
    <Pressable
      onPress={handlePress}
      className={`border-b border-dark-gray p-6 ${
        !isRepost ? "flex-row space-x-3" : ""
      }`}
    >
      {isRepost && (
        <View className="mb-3 flex-row items-center gap-[10px] text-xs text-[#878787]">
          <RepostOutlineIcon className="h-5 w-5" />
          <Text className="text-xs text-[#878787]">
            {thread.userName} reposted
          </Text>
        </View>
      )}

      {!activePost.isDeleted ? (
        <View className="flex w-full flex-col space-y-3">
          <View className="w-full flex-row space-x-3">
            <Link
              href={`/${activePost?.user?.twitterHandle}` as Href<string>}
              className="flex-shrink-0"
            >
              <Avatar src={activePost?.user?.twitterPicture} size={42} />
            </Link>
            <View className="flex-1 flex-row items-center justify-between space-x-4">
              <View className="flex-1 flex-row items-center">
                <Link
                  href={`/${activePost.user?.twitterHandle}` as Href<string>}
                  onPress={(e) => {
                    e.stopPropagation();
                  }}
                  className="font-semibold text-[#F4F4F4] flex-shrink"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  <Text>{activePost.userName}</Text>
                </Link>
                <Text className="text-[#878787]">・</Text>
                <Link
                  href={`/${activePost.user?.twitterHandle}` as Href<string>}
                  onPress={(e) => {
                    e.stopPropagation();
                  }}
                  className="text-[#878787] flex-shrink"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  <Text>@{activePost.user?.twitterHandle}</Text>
                </Link>
                <Text className="text-[#878787]">・</Text>
                <Text className="flex-shrink-0 text-[#878787]">
                  {formatTimeDistance(date)}
                </Text>
              </View>
              <View>
                <GeneralPostMenuActionsUI
                  thread={activePost}
                  comment={false}
                  handleDelete={handleDelete}
                />
              </View>
            </View>
          </View>

          <ThemedView>
            <CustomRenderHtml html={contentWithSwapLinks} />
          </ThemedView>

          {activePost.images && activePost.images.length > 0 && (
            <View className="overflow-hidden rounded-2xl">
              <Image
                source={{ uri: activePost.images[0].url }}
                className="w-full"
                resizeMode="cover"
                height={180}
              />
            </View>
          )}

          {activePost.videos && activePost.videos.length > 0 && (
            <Video
              source={{
                uri: activePost.videos[0].url,
              }}
              className="rounded-2xl"
              useNativeControls={true}
              resizeMode={ResizeMode.COVER}
              style={{ height: 180 }}
              isLooping
              shouldPlay
              isMuted
            />
          )}

          {youtubeEmbedUrl && (
            <View className="w-full aspect-video rounded-2xl overflow-hidden">
              <WebView
                source={{ uri: youtubeEmbedUrl }}
                javaScriptEnabled={true}
                domStorageEnabled={true}
              />
            </View>
          )}

          {isQuote &&
            repost &&
            !isUserBlockedLoading &&
            !isBlockedByUserLoading &&
            (isUserBlocked || isBlockedByUser || isSuspended ? (
              <View>
                <QuotePost {...repost} />
              </View>
            ) : (
              <Pressable
                onPress={(e) => {
                  e.stopPropagation();
                  router.push(
                    `/${repost.user?.twitterHandle}/status/${repost.id}` as Href<string>
                  );
                }}
              >
                <QuotePost {...repost} />
              </Pressable>
            ))}

          {isQuoteDeleted && <DeletedPost />}

          <View className="mt-1">
            <PostActionsUI
              thread={activePost}
              handleLike={handleLike}
              handleBookmark={handleBookmark}
              handleRepost={handleRepost}
              isDisabled={
                isUserBlocked ||
                isBlockedByUser ||
                isUserBlockedLoading ||
                isBlockedByUserLoading ||
                isSuspended ||
                false
              }
            />
          </View>

          {swapToken && (
            <TouchableOpacity
              onPress={() => {
                router.push(swapToken.tokenURL as Href<string>);
              }}
            >
              <Button>{"BUY $" + swapToken.tokenName}</Button>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <View className="flex flex-col">
          <Text className="text-gray-text">
            Post unavailable. This post has violated The Arena's{" "}
          </Text>
          <Link
            href={"/terms-of-use" as Href<string>}
            className="font-semibold text-white"
          >
            <Text>terms of use.</Text>
          </Link>
        </View>
      )}
    </Pressable>
  );
};

interface PostActionsProps {
  thread: Thread;
  handleLike: ({ threadId }: { threadId: string }) => void;
  handleBookmark: ({ threadId }: { threadId: string }) => void;
  handleRepost: ({ threadId }: { threadId: string }) => void;
  isDisabled: boolean;
}

export const PostActionsUI = ({
  thread,
  handleLike,
  handleBookmark,
  handleRepost,
  isDisabled,
}: PostActionsProps) => {
  const setReply = usePostStore((s) => s.setReply);
  const setQuote = usePostStore((s) => s.setQuote);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();

  const memoizedThread = useMemo(() => thread, [thread]);

  const memoizedHandleLike = useCallback(() => {
    handleLike({ threadId: thread.id });
  }, [handleLike, thread.id]);

  const memoizedHandleBookmark = useCallback(() => {
    handleBookmark({ threadId: thread.id });
  }, [handleBookmark, thread.id]);

  const memoizedHandleRepost = useCallback(() => {
    handleRepost({ threadId: thread.id });
  }, [handleRepost, thread.id, closeBottomSheet]);

  const handleReplyPress = () => {
    setReply(thread);
    router.push("/(modals)/addPost");
  };

  const handleQuoteRepost = () => {
    closeBottomSheet();
    setTimeout(() => {
      setQuote(thread);
      router.push("/(modals)/addPost/");
    }, 50);
  };

  const toggleModal = () => {
    setModalVisible((prevState) => !prevState);
  };

  const sheetContent = (
    <View className="space-y-2">
      <TouchableOpacity
        key={`repost-${thread.id}`}
        className="flex-row items-center space-x-2"
        onPress={() => {
          memoizedHandleRepost();
          closeBottomSheet();
        }}
        disabled={isDisabled}
      >
        <RepostOutlineIcon
          width={postActionIconSize}
          height={postActionIconSize}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-sm text-off-white">
          {thread.reposted ? "Undo repost" : "Repost"}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        key={`quote-${thread.id}`}
        className="flex-row items-center space-x-2"
        onPress={handleQuoteRepost}
        disabled={isDisabled}
      >
        <MenuAlt2OutlineIcon
          width={postActionIconSize}
          height={postActionIconSize}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-sm text-off-white">Quote</Text>
      </TouchableOpacity>
    </View>
  );

  const bottomSheet = () => {
    openBottomSheet(sheetContent, 150);
  };

  return (
    <>
      <ThemedView className="flex-row items-center justify-between space-x-4">
        <ThemedView className="flex-row items-center space-x-4">
          <Pressable
            className="flex-row items-center space-x-1"
            style={({ pressed }) => [pressed && { opacity: 0.5 }]}
            onPress={handleReplyPress}
            disabled={isDisabled}
          >
            <ChatBubblesOutlineIcon
              width={postActionIconSize}
              height={postActionIconSize}
              color={Colors.dark?.grey || "grey"}
            />
            <ThemedText className="text-xs text-off-white">
              {abbreviateNumber(
                thread.threadType === "repost" && thread.repost?.answerCount
                  ? thread.repost.answerCount
                  : thread.answerCount
              )}
            </ThemedText>
          </Pressable>
          <Pressable
            className="flex-row items-center space-x-1"
            onPress={bottomSheet}
            key={`like-${thread.id}`}
            disabled={isDisabled}
          >
            <RepostOutlineIcon
              width={postActionIconSize}
              height={postActionIconSize}
              color={thread.reposted ? Colors.dark.green : Colors.dark?.grey}
            />
            <ThemedText className="text-xs text-off-white">
              {thread.threadType === "repost" && thread.repost?.answerCount
                ? thread.repost.repostCount
                : thread.repostCount}
            </ThemedText>
          </Pressable>
          <Pressable
            className="flex-row items-center space-x-1"
            onPress={memoizedHandleLike}
            disabled={isDisabled}
          >
            {thread.like ? (
              <HeartFilledIcon
                width={postActionIconSize}
                height={postActionIconSize}
                fill={Colors.dark.brandOrange}
              />
            ) : (
              <HeartOutlineIcon
                width={postActionIconSize}
                height={postActionIconSize}
                color={Colors.dark?.grey || "grey"}
              />
            )}
            <ThemedText className="text-xs text-off-white">
              {abbreviateNumber(
                thread.threadType === "repost" && thread.repost?.likeCount
                  ? thread.repost.likeCount
                  : thread.likeCount
              )}
            </ThemedText>
          </Pressable>
          <Pressable
            className="flex-row items-center space-x-1"
            onPress={memoizedHandleBookmark}
            disabled={isDisabled}
          >
            {thread.bookmark ? (
              <BookmarkFilledIcon
                width={postActionIconSize}
                height={postActionIconSize}
                fill={Colors.dark?.offWhite}
              />
            ) : (
              <BookmarkOutlineIcon
                width={postActionIconSize}
                height={postActionIconSize}
                color={Colors.dark?.grey || "grey"}
              />
            )}
            <ThemedText className="text-xs text-off-white">
              {abbreviateNumber(
                thread.threadType === "repost" && thread.repost?.bookmarkCount
                  ? thread.repost.bookmarkCount
                  : thread.bookmarkCount
              )}
            </ThemedText>
          </Pressable>
        </ThemedView>
        <ThemedView>
          <Pressable
            className="flex-row items-center space-x-1"
            onPress={toggleModal}
          >
            <TipOutlineIcon
              width={postActionIconSize}
              height={postActionIconSize}
              color={Colors.dark?.grey || "grey"}
            />
            {thread.tipAmount &&
            numberFormatter.format(thread.tipAmount) !== "0" ? (
              <ThemedText className="text-xs text-off-white">
                {abbreviateNumber(thread.tipAmount)}$
              </ThemedText>
            ) : null}
          </Pressable>
        </ThemedView>
      </ThemedView>
      <TipModal
        thread={memoizedThread}
        visible={modalVisible}
        onClose={toggleModal}
        children={undefined}
      />
    </>
  );
};

interface PostMenuActionsUIProps {
  thread: Thread;
  handleDelete?: ({ threadId }: { threadId: string }) => void;
  handlePin?: ({
    threadId,
    isPinned,
  }: {
    threadId: string;
    isPinned: boolean;
  }) => void;
}

export const PostMenuActionsUI = ({
  thread,
  handleDelete,
  handlePin,
}: PostMenuActionsUIProps) => {
  const { user } = useUser();
  const pathname = usePathname();
  const sanitizedContent = stripHtmlTags(thread.content || "");
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();
  const isInProfilePage = user ? pathname === `/${user?.twitterHandle}` : false;
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isUnpinOpen, setIsUnpinOpen] = useState(false);

  const handleUnpinConfirm = () => {
    handlePin?.({ threadId: thread.id, isPinned: false });
    setIsUnpinOpen(false);
  };

  const handleDeleteConfirm = () => {
    handleDelete?.({ threadId: thread.id });
    setIsDeleteOpen(false);
  };

  const sheetContent = (
    <View>
      <TouchableOpacity
        onPress={() => {
          closeBottomSheet();
          router.push(`/${thread.user?.twitterHandle}/status/${thread.id}/postreactions/likes`);
        }}
        className="flex-row items-center space-x-2 p-2"
      >
        <ChartSquareBarOutlineIcon
          width={20}
          height={20}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-off-white">Post Reactions</Text>
      </TouchableOpacity>
      {isInProfilePage ? (
        thread.isPinned ? (
          <TouchableOpacity
            className="flex-row items-center space-x-2 p-2"
            onPress={() => {
              closeBottomSheet();
              setIsUnpinOpen(true);
            }}
          >
            <PinOutlineIcon
              width={20}
              height={20}
              color={Colors.dark?.grey || "grey"}
            />
            <Text className="text-off-white">Unpin</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            className="flex-row items-center space-x-2 p-2"
            onPress={() => {
              handlePin && handlePin({ threadId: thread.id, isPinned: true });
              closeBottomSheet();
            }}
          >
            <PinOutlineIcon
              width={20}
              height={20}
              color={Colors.dark?.grey || "grey"}
            />
            <Text className="text-off-white">Pin</Text>
          </TouchableOpacity>
        )
      ) : null}

      <TouchableOpacity
        className="flex-row items-center space-x-2 p-2"
        onPress={() => {
          closeBottomSheet();
          setIsDeleteOpen(true);
        }}
      >
        <TrashOutlineIcon
          width={20}
          height={20}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-off-white">Delete</Text>
      </TouchableOpacity>

      <TouchableOpacity
        className="flex-row items-center space-x-2 p-2"
        onPress={async () => {
          try {
            const link = `${BASE_URL}/${thread.user?.twitterHandle}/status/${thread.id}`;
            await Clipboard.setStringAsync(link);
            toast.green("Copied the link to the clipboard");
          } catch {}
          closeBottomSheet();
        }}
      >
        <LinkOutlineIcon
          width={20}
          height={20}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-off-white">Copy link</Text>
      </TouchableOpacity>

      <TouchableOpacity
        className="flex-row items-center space-x-2 p-2"
        onPress={async () => {
          try {
            await Clipboard.setStringAsync(sanitizedContent);
            toast.green("Copied the text to the clipboard");
          } catch {}
          closeBottomSheet();
        }}
      >
        <CopyOutlineIcon
          width={20}
          height={20}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-off-white">Copy Text</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <>
      <TouchableOpacity
        className="flex items-center justify-center"
        onPress={() => openBottomSheet(sheetContent, 220)}
      >
        <EllipsisHorizontalFilledIcon
          width={20}
          height={20}
          fill={Colors.dark?.offWhite}
        />
      </TouchableOpacity>
      <ConfirmationSheet
        open={isUnpinOpen}
        setOpen={setIsUnpinOpen}
        title="Unpin post from profile?"
        confirmButtonLabel="Unpin"
        onConfirm={handleUnpinConfirm}
      >
        This post will no longer appear automatically at the top of your
        profile.
      </ConfirmationSheet>
      <ConfirmationSheet
        open={isDeleteOpen}
        setOpen={setIsDeleteOpen}
        title="Delete post?"
        confirmButtonLabel="Delete"
        destructive
        onConfirm={handleDeleteConfirm}
      >
        This can&apos;t be undone and it will be removed from your profile, the
        timeline of any accounts that follow you.
      </ConfirmationSheet>
    </>
  );
};

export const GeneralPostMenuActionsUI = ({
  thread,
  comment,
  handleDelete,
}: {
  thread: Thread;
  comment: boolean;
  handleDelete?: ({ threadId }: { threadId: string }) => void;
}) => {
  const { user } = useUser();
  const sanitizedContent = stripHtmlTags(thread.content || "");
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();

  const handleDeleteConfirm = useCallback(() => {
    handleDelete && handleDelete({ threadId: thread.id });
    setIsDeleteOpen(false);
  }, [handleDelete, thread.id]);

  const sheetContent = (
    <View>
      <TouchableOpacity
        onPress={() => {
          closeBottomSheet();
          router.push(`/${thread.user?.twitterHandle}/status/${thread.id}/postreactions/likes`);
        }}
        className="flex-row items-center space-x-2 p-2"
      >
        <ChartSquareBarOutlineIcon
          width={20}
          height={20}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-off-white">Post Reactions</Text>
      </TouchableOpacity>

      {thread.threadType !== "repost" &&
      (user?.id === thread.userId || user?.isMod) ? (
        <TouchableOpacity
          className="flex-row items-center space-x-2 p-2"
          onPress={() => {
            setIsDeleteOpen(true);
          }}
        >
          <TrashOutlineIcon
            width={20}
            height={20}
            color={Colors.dark?.grey || "grey"}
          />
          <Text className="text-off-white">Delete</Text>
        </TouchableOpacity>
      ) : null}

      <TouchableOpacity
        className="flex-row items-center space-x-2 p-2"
        onPress={async () => {
          try {
            const link = comment
              ? `${BASE_URL}/${thread.user?.twitterHandle}/nested/${thread.id}`
              : `${BASE_URL}/${thread.user?.twitterHandle}/status/${thread.id}`;
            await Clipboard.setStringAsync(link);
            toast.green("Copied the link to the clipboard");
          } catch {}
          closeBottomSheet();
        }}
      >
        <LinkOutlineIcon
          width={20}
          height={20}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-off-white">Copy link</Text>
      </TouchableOpacity>

      <TouchableOpacity
        className="flex-row items-center space-x-2 p-2"
        onPress={async () => {
          try {
            await Clipboard.setStringAsync(sanitizedContent);
            toast.green("Copied the text to the clipboard");
          } catch {}
          closeBottomSheet();
        }}
      >
        <CopyOutlineIcon
          width={20}
          height={20}
          color={Colors.dark?.grey || "grey"}
        />
        <Text className="text-off-white">Copy Text</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <>
      <TouchableOpacity
        className="flex items-center justify-center"
        onPress={() => openBottomSheet(sheetContent, 220)}
      >
        <EllipsisHorizontalFilledIcon
          width={20}
          height={20}
          fill={Colors.dark?.offWhite}
        />
      </TouchableOpacity>
      <ConfirmationSheet
        open={isDeleteOpen}
        setOpen={setIsDeleteOpen}
        title="Delete post?"
        confirmButtonLabel="Delete"
        destructive
        onConfirm={handleDeleteConfirm}
      >
        This can&apos;t be undone and it will be removed from your profile, the
        timeline of any accounts that follow you.
      </ConfirmationSheet>
    </>
  );
};

export const DeletedPost = () => {
  return (
    <Text className="flex w-full items-center justify-center rounded-lg border border-dark-gray bg-dark-bk px-3 py-4 text-sm text-gray-text">
      The original post was deleted
    </Text>
  );
};

/**
 * Replaces <br> tags with newlines and then strips out all other HTML tags.
 * @param {string} message
 * @returns {string}
 */
export const stripHtmlTags = (message: string): string => {
  if (typeof message !== "string" || !message) {
    return "";
  }

  let output = message.replace(/<br\s*\/?>/gi, "\n");

  output = output.replace(/<[^>]+>/g, "");
  const decoded = decode(output);
  return decoded.trim();
};

export default PostUI;
