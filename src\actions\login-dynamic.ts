import { env } from "@/env";
import { axios } from "@/lib/axios";
import { setCookie } from "@/cookies/secure-store";

interface LoginDynamicProps {
  token: string | null;
  ref: string | null;
}

export async function loginDynamic({ token, ref }: LoginDynamicProps) {
  try {
    if (!token) {
      throw new Error("No token provided");
    }

    const { data } = await axios.post(
      `${env.EXPO_PUBLIC_API_URL}/auth/dynamic-jwt-exchange`,
      { token, ref }
    );

    if (data.errorCode) {
      throw data;
    }

    await axios.post(
      `${env.EXPO_PUBLIC_API_URL}/term-of-use/approve`,
      undefined,
      {
        headers: {
          Authorization: `Bearer ${data.token}`,
        },
      }
    );

    // 30 days
    const expires = new Date(Date.now() + 30 * 1000 * 60 * 60 * 24);
    setCookie("token", data.token, expires);
    set<PERSON>ookie(
      "user",
      JSON.stringify({
        ...data.user,
        loggedInAt: new Date(),
      }),
      expires
    );
    setCookie("twitterUser", JSON.stringify(data.twitterUser), expires);

    return { data };
  } catch (error: any) {
    return { error: error.message };
  }
}
