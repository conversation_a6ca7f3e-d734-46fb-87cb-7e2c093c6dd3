import { router, Stack } from "expo-router";
import { Pressable, StyleSheet, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { ProfileFloatingHeader } from "./_components/floating-header";
import {
  ArrowBackOutlineIcon,
  EllipsisHorizontalFilledIcon,
} from "@/components/icons";
import { Colors } from "@/constants/Colors";
import { useThreedotsModalStore } from "@/stores/threedotsmodal";
import React from "react";
import { ShowRepostsProvider } from "./showRepostsContext";

export default function ProfileStackLayout() {
  const openModal = useThreedotsModalStore((state) => state.openModal);

  return (
    <ShowRepostsProvider>
      <SafeAreaView style={styles.stackStyles}>
        <Stack
          screenOptions={{
            headerShown: false,
          }}
        >
          <Stack.Screen
            name="(top-tabs)"
            options={{
              headerShown: true,
              header: () => <ProfileFloatingHeader />,
            }}
          />
          <Stack.Screen
            name="edit/index"
            options={{
              headerShown: true,
              title: "Edit Profile",
              headerBackTitleVisible: false,
              headerLeft: () => (
                <Pressable onPress={() => router.back()}>
                  <ArrowBackOutlineIcon
                    color={Colors.dark.offWhite}
                    height={20}
                    width={20}
                  />
                </Pressable>
              ),
              headerStyle: styles.editHeader,
              headerTitleAlign: "center",
            }}
          />
          <Stack.Screen
            name="status/[id]/index"
            options={{
              headerShown: true,
              title: "Post",
              headerBackTitleVisible: false,
              headerLeft: () => (
                <Pressable onPress={() => router.back()}>
                  <ArrowBackOutlineIcon
                    color={Colors.dark.offWhite}
                    height={20}
                    width={20}
                  />
                </Pressable>
              ),
              headerRight: () => (
                <TouchableOpacity onPress={openModal}>
                  <EllipsisHorizontalFilledIcon
                    width={20}
                    height={20}
                    fill="#fff"
                  />
                </TouchableOpacity>
              ),
              headerStyle: styles.editHeader,
              headerTitleAlign: "center",
            }}
          />
        </Stack>
      </SafeAreaView>
    </ShowRepostsProvider>
  );
}

const styles = StyleSheet.create({
  stackStyles: {
    flex: 1,
  },
  editHeader: {
    backgroundColor: "transparent",
  },
});
