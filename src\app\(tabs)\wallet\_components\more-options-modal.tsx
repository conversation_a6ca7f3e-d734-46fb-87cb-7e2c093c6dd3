import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { StyleSheet, View } from "react-native";
import { MoreOptionsCard } from "./more-options-card";
import { VisibilityOffIcon } from "@/components/icons/visibility-off-outline";
import { Colors } from "@/constants/Colors";
import CustomSwitch from "@/components/ui/switch";
import { useState } from "react";
import { KeyFilledIcon, ShieldCheckFilledIcon } from "@/components/icons";
import { useBottomSheet } from "@/components/ui/BottomSheetContext";
import { ExportPrivateKeys } from "./export-private-keys";
import { globalStyles } from "@/app/globalStyles";
import { router } from "expo-router";

const ICON_WIDTH = 18;
const ICON_HEIGHT = 18;

export const MoreOptionsModal = () => {
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();
  const [switchEnabled, setSwitchEnabled] = useState(false);

  const exportPrivateKeys = () => {
    openBottomSheet(<ExportPrivateKeys />, 727);
  };

  const claimNftBadge = () => {
    router.push("/wallet/claim-nft-badge");
    closeBottomSheet();
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={globalStyles.bottomSheetHeader}>
        More Options
      </ThemedText>
      <ThemedView style={styles.cardsWrapper}>
        <MoreOptionsCard
          leftComponent={
            <VisibilityOffIcon
              height={ICON_HEIGHT}
              width={ICON_WIDTH}
              fill={Colors.dark.offWhite}
            />
          }
          rightComponent={
            <CustomSwitch
              isEnabled={switchEnabled}
              setIsEnabled={setSwitchEnabled}
            />
          }
        >
          <ThemedText style={styles.cardText}>
            Hide tokens with zero balance
          </ThemedText>
        </MoreOptionsCard>
        <MoreOptionsCard
          leftComponent={
            <KeyFilledIcon
              height={ICON_HEIGHT}
              width={ICON_WIDTH}
              fill={Colors.dark.offWhite}
            />
          }
          useLinearGradient={true}
          linearGradientOnPress={exportPrivateKeys}
          containerStyle={styles.nullBorder}
        >
          <View style={styles.contentWrapper}>
            <ThemedText style={styles.cardText}>
              Export your private keys
            </ThemedText>
            <ThemedText style={styles.subText}>
              You can export your private keys to {"\n"}use this wallet in
              another device.
            </ThemedText>
          </View>
        </MoreOptionsCard>
        <MoreOptionsCard
          onPress={claimNftBadge}
          leftComponent={
            <ShieldCheckFilledIcon
              height={ICON_HEIGHT}
              width={ICON_WIDTH}
              fill={Colors.dark.offWhite}
            />
          }
        >
          <View style={styles.contentWrapper}>
            <ThemedText style={styles.cardText}>
              Claim Your NFT Badge
            </ThemedText>
            <ThemedText style={styles.subText}>
              Get a your NFT badge for being an {"\n"}active member of The
              Arena.
            </ThemedText>
          </View>
        </MoreOptionsCard>
      </ThemedView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.dark.greyBackground,
  },
  cardsWrapper: {
    gap: 16,
    backgroundColor: Colors.dark.greyBackground,
  },
  contentWrapper: {
    gap: 2,
  },
  cardText: {
    fontSize: 14,
    fontWeight: "600",
  },
  subText: {
    color: "rgba(244, 244, 244, 0.8)",
    fontSize: 14,
    fontWeight: "400",
  },
  nullBorder: {
    borderWidth: 0,
  },
});
