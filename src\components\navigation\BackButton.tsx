import { StyleSheet, TouchableOpacity } from "react-native";
import React, { memo } from "react";
import { ArrowBackOutlineIcon } from "../icons";
import { Colors } from "@/constants/Colors";
import { router } from "expo-router";
import { usePostStore } from "@/stores";

type TBackButtonProps = {
  onPress?: () => void;
}

const BackButton = memo(({ onPress }: TBackButtonProps) => {
  const reset = usePostStore((store) => store.reset);

  const backHandler = () => {
    if (typeof onPress === 'function') {
      onPress();
    } else {
      reset();
      router.back();
    }
  };

  return (
    <TouchableOpacity onPress={backHandler}>
      <ArrowBackOutlineIcon
        width={23}
        height={23}
        color={Colors.dark.offWhite}
      />
    </TouchableOpacity>
  );
}, (prevProps, nextProps) => {
  return prevProps.onPress === nextProps.onPress
});

export default BackButton;

const styles = StyleSheet.create({});
