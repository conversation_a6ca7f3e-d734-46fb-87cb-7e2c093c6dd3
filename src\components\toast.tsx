import Toast from "react-native-root-toast";
import { StyleSheet } from "react-native";
import { screenWidth } from "@/constants/Device";

const green = (message: string) => {
  return Toast.show(message, {
    duration: Toast.durations.LONG,
    position: Toast.positions.TOP,
    shadow: true,
    animation: true,
    hideOnPress: true,
    delay: 0,
    containerStyle: [styles.toastContainer, styles.toastContainerSuccess],
    textStyle: styles.textStyle,
    opacity: 1,
  });
};

const red = (message: string) => {
  return Toast.show(message, {
    duration: Toast.durations.LONG,
    position: Toast.positions.TOP,
    shadow: true,
    animation: true,
    hideOnPress: true,
    delay: 0,
    containerStyle: [styles.toastContainer, styles.toastContainerRed],
    textStyle: styles.textStyle,
    opacity: 1
  });
};

const danger = (message: string) => {
  return Toast.show(message, {
    duration: Toast.durations.LONG,
    position: Toast.positions.TOP,
    shadow: true,
    animation: true,
    hideOnPress: true,
    delay: 0,
    containerStyle: [styles.toastContainer, styles.toastContainerDanger],
    textStyle: styles.textStyle,
    opacity: 1
  });
};

const styles = StyleSheet.create({
  toastContainer: {
    borderRadius: 10,
    top: 20,
    width: screenWidth / 1.2,
    paddingVertical: 12,
  },
  toastContainerSuccess: {
    backgroundColor: "#6FB672",
  },
  toastContainerRed: {
    backgroundColor: "#D14848",
  },
  toastContainerDanger: {
    backgroundColor: "#D14848",
  },
  textStyle: {
    fontSize: 12,
    textAlign: "left",
  },
});

export const toast = {
  green,
  red,
  danger,
};
