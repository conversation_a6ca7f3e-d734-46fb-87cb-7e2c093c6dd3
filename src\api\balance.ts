import {
  createAssociatedTokenAccountInstruction,
  createTransferInstruction,
  getAccount,
  getAssociatedTokenAddress,
} from '@solana/spl-token';
import {
  Commitment,
  Connection,
  LAMPORTS_PER_SOL,
  PublicKey,
  SystemProgram,
  Transaction,
} from '@solana/web3.js';
import { ethers, JsonRpcProvider } from 'ethers';

import { env } from '@/env';
import {
  BACKEND_FRIENDS_CONTRACT,
  BACKEND_FRIENDS_CONTRACT_ABI,
  ERC20_CONTRACT_ABI,
} from '@/environments/BACKEND_FRIENDS_CONTRACT';

import axios from 'axios';
import { getOrCreateSolanaWallet } from './client/chain';

const provider = new JsonRpcProvider(env.EXPO_PUBLIC_MAINNET_RPC_URL);
const contract = new ethers.Contract(
  BACKEND_FRIENDS_CONTRACT.addressMainnet,
  BACKEND_FRIENDS_CONTRACT_ABI,
  provider,
);

const SOLANA_RPC_NODE =
  'https://rpc.hellomoon.io/93c67b6d-228d-4520-bcee-74fd41c4fa3b';
export const SOLANA_SPL_TOKENS: {
  [token: string]: { publicKey: PublicKey; decimalsMultiplier: number };
} = {
  Bonk: {
    publicKey: new PublicKey('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263'),
    decimalsMultiplier: 100_000,
  },
  $WIF: {
    publicKey: new PublicKey('EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm'),
    decimalsMultiplier: 1_000_000,
  },
  USEDCAR: {
    publicKey: new PublicKey('9gwTegFJJErDpWJKjPfLr2g2zrE3nL1v5zpwbtsk3c6P'),
    decimalsMultiplier: 1_000_000_000,
  },
  Moutai: {
    publicKey: new PublicKey('45EgCwcPXYagBC7KqBin4nCFgEZWN7f3Y6nACwxqMCWX'),
    decimalsMultiplier: 1_000_000,
  },
  HARAMBE: {
    publicKey: new PublicKey('Fch1oixTPri8zxBnmdCEADoJW2toyFHxqDZacQkwdvSP'),
    decimalsMultiplier: 1_000_000_000,
  },
};

export const getBalance = async (address: string) => {
  return provider.getBalance(address);
};

export const refreshBalance = async (address: string) => {
  try {
    const bnBalance = await getBalance(address);
    const coqBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressCOQ,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const gursBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressGURS,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const nochillBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressNOCHILL,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const meatBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressMEAT,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const kimboBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressKIMBO,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const joeBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressJOE,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const techBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressTECH,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const arenaBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressARENA,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const champBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressCHAMP,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const ketBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressKET,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);
    const winkBalance = await new ethers.Contract(
      BACKEND_FRIENDS_CONTRACT.addressWINK,
      ERC20_CONTRACT_ABI,
      provider,
    ).balanceOf(address);

    const balances = {
      AVAX: bnBalance,
      COQ: coqBalance,
      GURS: gursBalance,
      NOCHILL: nochillBalance,
      MEAT: meatBalance,
      KIMBO: kimboBalance,
      JOE: joeBalance,
      TECH: techBalance,
      ARENA: arenaBalance,
      CHAMP: champBalance,
      KET: ketBalance,
      WINK: winkBalance,
    };
    return balances;
  } catch (error: any) {
    throw new Error(`Error fetching balance: ${error.message}`);
  }
};

export const getSolanaBalance = async (
  address: string,
): Promise<{
  SOL: number;
  splTokens: {
    Bonk?: number;
    $WIF?: number;
    USEDCAR?: number;
    Moutai?: number;
    HARAMBE?: number;
  };
}> => {
  // Create a new connection to the Solana blockchain
  const connection = new Connection(SOLANA_RPC_NODE, 'confirmed');

  // Convert the provided Solana address into a PublicKey
  const publicKey = new PublicKey(address);

  // Get the balance
  const balance = await connection.getBalance(publicKey);

  // The balance is returned in lamports (the smallest unit of SOL), so you might want to convert it to SOL
  const SOL = balance / 1e9; // 1 SOL = 1,000,000,000 lamports

  const splTokens: any = {};
  const splTokensList = Object.keys(SOLANA_SPL_TOKENS);
  for (let i = 0, l = splTokensList.length; i < l; i++) {
    const splToken = splTokensList[i];
    const associatedTokenAddress = await getAssociatedTokenAddress(
      SOLANA_SPL_TOKENS[splToken].publicKey,
      new PublicKey(address),
    );

    try {
      const tokenAccountInfo = await getAccount(
        connection,
        associatedTokenAddress,
      );

      // The balance is returned in the smallest unit of the token, which is usually equivalent to lamports for SOL
      const tokenBalance = tokenAccountInfo.amount;
      const tokenBalanceNumber =
        parseInt(tokenBalance.toString()) /
        SOLANA_SPL_TOKENS[splToken].decimalsMultiplier;
      splTokens[splToken] = tokenBalanceNumber;
    } catch (e) {
      console.log("No token account found for this user's address");
      console.log(e);
    }
  }

  return { SOL, splTokens };
};

export const depositSolana = async (
  provider: any,
  amount: number,
  solanaCurrency = 'SOL',
  commitment: Commitment = 'confirmed',
): Promise<any> => {
  const { response } = await getOrCreateSolanaWallet();
  const solanaAddress = response.solanaAddress;

  const providerConnection = await provider.connect();
  const { publicKey } = providerConnection;
  const connection = new Connection(SOLANA_RPC_NODE, commitment);
  const blockhash = await connection.getLatestBlockhash();

  let transaction = new Transaction();
  if (solanaCurrency === 'SOL') {
    transaction.add(
      SystemProgram.transfer({
        fromPubkey: publicKey,
        toPubkey: new PublicKey(solanaAddress),
        lamports: LAMPORTS_PER_SOL * amount,
      }),
    );
  } else if (Object.keys(SOLANA_SPL_TOKENS).includes(solanaCurrency)) {
    const destinationAssociatedTokenAddress = await getAssociatedTokenAddress(
      SOLANA_SPL_TOKENS[solanaCurrency].publicKey,
      new PublicKey(solanaAddress),
    );
    const accountInfo = await connection.getAccountInfo(
      destinationAssociatedTokenAddress,
    );
    if (!accountInfo) {
      transaction.add(
        createAssociatedTokenAccountInstruction(
          publicKey,
          destinationAssociatedTokenAddress,
          new PublicKey(solanaAddress),
          SOLANA_SPL_TOKENS[solanaCurrency].publicKey,
        ),
      );
    }
    transaction.add(
      createTransferInstruction(
        await getAssociatedTokenAddress(
          SOLANA_SPL_TOKENS[solanaCurrency].publicKey,
          publicKey,
        ),
        destinationAssociatedTokenAddress,
        publicKey,
        SOLANA_SPL_TOKENS[solanaCurrency].decimalsMultiplier * amount,
      ),
    );
  } else {
    throw new Error('Invalid currency');
  }

  transaction.recentBlockhash = blockhash.blockhash;
  transaction.feePayer = publicKey;

  const resp = await provider.signAndSendTransaction(transaction);
  return resp;
};

export const getPrice = async (address: string) => {
  return Promise.all([
    contract.getBuyPriceAfterFee(address, BigInt(1)),
    contract.getSellPriceAfterFee(address, BigInt(1)),
  ]);
};

export const getTokenPrice = async () => {
  try {
    const response = await axios.get(env.EXPO_PUBLIC_COINGECKO_API);
    return response.data;
  } catch (error) {
    return null;
  }
};
