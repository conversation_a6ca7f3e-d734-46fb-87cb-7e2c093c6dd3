import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import React, { memo } from 'react';

import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface OTPBoxProps {
  handleSendOTP: () => void;
  handleResendOTP: () => void;
  handleOTPVerification: (otp: string) => void;
}

const OTPBox: React.FC<OTPBoxProps> = ({
  handleSendOTP,
  handleResendOTP,
  handleOTPVerification,
}) => {
  const [otp, setOtp] = React.useState<string>('');
  React.useEffect(() => {
    handleSendOTP();
  }, []);

  const isValidOTP = (otp: string): boolean => {
    const otpRegex = /^[0-9]{6}$/;
    return otpRegex.test(otp);
  };

  const handleVerify = () => {
    if (isValidOTP(otp)) {
      handleOTPVerification(otp);
    } else {
      console.error('Invalid OTP');
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Verify your email address</ThemedText>
      <ThemedText style={styles.description}>
        Verification code sent to your email.
      </ThemedText>
      <TextInput
        style={styles.input}
        placeholder="123456"
        placeholderTextColor="#B5B5B5"
        value={otp}
        onChangeText={setOtp}
        keyboardType="numeric"
      />
      <View style={styles.description}>
        <ThemedText>Didn't receive the code? </ThemedText>
        <TouchableOpacity onPress={handleResendOTP}>
          <Text style={styles.resendText}>Resend</Text>
        </TouchableOpacity>
      </View>
      <TouchableOpacity
        style={[styles.button, { opacity: isValidOTP(otp) ? 1 : 0.5 }]}
        disabled={!isValidOTP(otp)}
        onPress={handleVerify}
      >
        <Text style={styles.buttonText}>Verify</Text>
      </TouchableOpacity>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    backgroundColor: '#0F0F0FE6',
    width: '90%',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#F4F4F4',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: '#b5b5b5',
    marginBottom: 4,
    alignItems: 'center',
    marginTop: 4,
    flexDirection: 'row',
  },
  input: {
    height: 44,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#808080',
    backgroundColor: '#1A1A1A',
    paddingHorizontal: 24,
    color: '#F4F4F4',
    fontSize: 14,
    marginTop: 24,
    marginBottom: 20,
  },
  button: {
    height: 44,
    borderRadius: 40,
    backgroundColor: '#D64C05',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 60,
    marginVertical: 8,
  },
  buttonText: {
    color: '#F4F4F4',
    fontSize: 14,
    fontWeight: '600',
  },
  resendText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default memo(OTPBox);
