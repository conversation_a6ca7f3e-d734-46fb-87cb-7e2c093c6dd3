import { axios } from "@/lib/axios";

export const getTrades = async () => {
  const response = await axios.get("/trade/trades");
  return response.data;
};

export const getRecentTrades = async () => {
  const response = await axios.get("/trade/recent");
  return response.data;
};

export const getTrendingUsers = async () => {
  const response = await axios.get("/trade/users/trending");
  return response.data;
};

export const postBuyShare = async ({
  address,
  amount,
}: {
  address: string;
  amount: string;
}) => {
  const response = await axios.post("/trade/buy", {
    address,
    amount,
  });
  return response.data;
};

export const postSellShare = async ({
  address,
  amount,
}: {
  address: string;
  amount: string;
}) => {
  const response = await axios.post("/trade/sell", {
    address,
    amount,
  });
  return response.data;
};

export const postBuyTicker = async ({
  srcToken,
  destToken,
  srcAmount,
  threadId,
}: {
  srcToken: string;
  destToken: string;
  srcAmount: string;
  threadId: string;
}) => {
  const response = await axios.post("/trade/ticker/buy", {
    srcToken,
    destToken,
    srcAmount,
    threadId,
  });
  return response.data;
};
