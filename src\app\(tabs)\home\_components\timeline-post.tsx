import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import {
  useBookmarkThreadMutation,
  useDeleteRepostMutation,
  useDeleteThreadMutation,
  useLikeThreadMutation,
  useRepostThreadMutation,
  useUnbookmarkThreadMutation,
  useUnlikeThreadMutation,
} from "@/queries";
import { ThreadsResponse } from "@/queries/types";
import { Thread } from "@/types";
import PostUI from "@/components/post";

interface TimelinePostProps {
  thread: Thread;
}

export const TimelinePost = ({ thread }: TimelinePostProps) => {
  const isRepost = thread.threadType === "repost";
  const activePost = isRepost && thread.repost ? thread.repost : thread;
  const queryClient = useQueryClient();

  // ilk olarak cache i iptal et
  const { mutateAsync: repost } = useRepostThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      // mevcut cache verilerini al feed ve trending icin
      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      //cache verilerini guncelle
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount + 1,
                          reposted: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount + 1,
                      reposted: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    //error durumlari belirlenir
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed
      );
    },
  });
  const { mutateAsync: deleteRepost } = useDeleteRepostMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.reposted) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          repostCount: t.repost.repostCount - 1,
                          reposted: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId) {
                    return {
                      ...t,
                      repostCount: t.repostCount - 1,
                      reposted: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed
      );
    },
  });
  const { mutateAsync: like } = useLikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount + 1,
                          like: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount + 1,
                      like: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed
      );
    },
  });
  const { mutateAsync: unlike } = useUnlikeThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);

      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.like) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          likeCount: t.repost.likeCount - 1,
                          like: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.like) {
                    return {
                      ...t,
                      likeCount: t.likeCount - 1,
                      like: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed
      );
    },
  });
  const { mutateAsync: bookmark } = useBookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && !t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount + 1,
                          bookmark: true,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && !t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount + 1,
                      bookmark: true,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed
      );
    },
  });
  const { mutateAsync: unbookmark } = useUnbookmarkThreadMutation({
    onMutate: async ({ threadId }) => {
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.map((t) => {
                  const isRepost = t.threadType === "repost";

                  if (isRepost && t.repost && t.repost.bookmark) {
                    if (t.repost.id === threadId) {
                      return {
                        ...t,
                        repost: {
                          ...t.repost,
                          bookmarkCount: t.repost.bookmarkCount - 1,
                          bookmark: false,
                        },
                      };
                    }
                  }

                  if (t.id === threadId && t.bookmark) {
                    return {
                      ...t,
                      bookmarkCount: t.bookmarkCount - 1,
                      bookmark: false,
                    };
                  }

                  return t;
                }),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
    },
  });
  const { mutateAsync: deleteThread } = useDeleteThreadMutation({
    onMutate: async ({ threadId }) => {
      toast.red("Post deleted");
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "my-feed"],
      });
      await queryClient.cancelQueries({
        queryKey: ["home", "threads", "trending-feed"],
      });

      const previousMyFeed: InfiniteData<ThreadsResponse, unknown> | undefined =
        queryClient.getQueryData(["home", "threads", "my-feed"]);
      const previousTrendingFeed:
        | InfiniteData<ThreadsResponse, unknown>
        | undefined = queryClient.getQueryData([
        "home",
        "threads",
        "trending-feed",
      ]);

      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        }
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        (old: InfiniteData<ThreadsResponse, unknown>) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                threads: page.threads.filter((t) => t.id !== threadId),
              };
            }),
          };
        }
      );

      return { previousMyFeed, previousTrendingFeed };
    },
    onError(err, variables, context) {
      queryClient.setQueryData(
        ["home", "threads", "my-feed"],
        context?.previousMyFeed
      );
      queryClient.setQueryData(
        ["home", "threads", "trending-feed"],
        context?.previousTrendingFeed
      );
    },
  });

  const handleLike = async ({ threadId }: { threadId: string }) => {
    if (activePost.like) {
      await unlike({ threadId });
    } else {
      await like({ threadId });
    }
  };

  const handleBookmark = async ({ threadId }: { threadId: string }) => {
    if (activePost.bookmark) {
      await unbookmark({ threadId });
    } else {
      await bookmark({ threadId });
    }
  };

  const handleRepost = async ({ threadId }: { threadId: string }) => {
    if (activePost.reposted) {
      await deleteRepost({ threadId });
    } else {
      await repost({ threadId });
    }
  };

  const handleDelete = async ({ threadId }: { threadId: string }) => {
    await deleteThread({ threadId });
  };

  return (
    <PostUI
      key={thread.id}
      thread={thread}
      handleLike={handleLike}
      handleBookmark={handleBookmark}
      handleRepost={handleRepost}
      handleDelete={handleDelete}
    />
  );
};
