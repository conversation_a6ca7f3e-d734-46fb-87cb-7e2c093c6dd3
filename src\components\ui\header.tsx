import React from "react";
import {
  StyleSheet,
  View,
  StyleProp,
  ViewStyle,
  TextStyle,
} from "react-native";
import { ThemedView } from "../ThemedView";
import BackButton from "../navigation/BackButton";
import { ThemedText } from "../ThemedText";

interface HeaderProps {
  title: string;
  containerStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
}

const Header: React.FC<HeaderProps> = ({
  title,
  containerStyle,
  titleStyle,
}) => {
  return (
    <ThemedView style={[styles.headerBox, containerStyle]}>
      <BackButton />
      <ThemedText style={titleStyle}>{title}</ThemedText>
      <View style={styles.headerRight}>
        <BackButton />
      </View>
    </ThemedView>
  );
};

export default Header;

const styles = StyleSheet.create({
  headerBox: {
    padding: 20,
    paddingTop: 50,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  headerRight: {
    opacity: 0,
  },
});
