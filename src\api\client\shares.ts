import { axios } from "@/lib/axios";
import { SharesHoldersResponse } from "@/queries/types";

export const getSharesStats = async ({ userId }: { userId: string }) => {
  const searchParams = new URLSearchParams({
    userId,
  });

  const response = await axios.get(`/shares/stats?${searchParams.toString()}`);
  return response.data;
};

export const getSharesHolders = async (userId?: string) => {
  const searchParams = userId
    ? new URLSearchParams({
        userId,
      })
    : null;

  const response = await axios.get<SharesHoldersResponse>(
    "/shares/holders" + (searchParams ? `?${searchParams.toString()}` : ""),
  );

  return response.data;
};

export const getSharesHoldings = async () => {
  const response = await axios.get("/shares/holdings");
  return response.data;
};
