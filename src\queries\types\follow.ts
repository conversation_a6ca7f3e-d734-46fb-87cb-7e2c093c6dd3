export interface Follower {
  id: number;
  createdOn: string;
  modifiedOn: string;
  followerId: string;
  followingId: string;
  followingActionTs: string | null;
  confirmed: string | null;
  follower: {
    threadCount: number;
    followerCount: number;
    followingsCount: number;
    twitterFollowers: number;
    id: string;
    createdOn: string;
    twitterId: string;
    twitterHandle: string;
    twitterName: string;
    twitterPicture: string;
    bannerUrl: string;
    address: string;
    ethereumAddress: string | null;
    solanaAddress: string;
    prevAddress: string | null;
    addressConfirmed: boolean;
    twitterDescription: string;
    signedUp: boolean;
    subscriptionCurrency: string;
    subscriptionCurrencyAddress: string | null;
    subscriptionPrice: string;
    keyPrice: string;
    lastKeyPrice: string;
    subscriptionsEnabled: boolean;
    userConfirmed: boolean;
    twitterConfirmed: boolean;
    flag: number;
    ixHandle: string;
    handle: string;
  };
  followedByloggedInUser: boolean;
}

export interface FollowersResponse {
  followersWithFollowedByloggedInUser: Follower[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: string;
}

export interface Following {
  id: number;
  createdOn: string;
  modifiedOn: string;
  followerId: string;
  followingId: string;
  followingActionTs: string | null;
  confirmed: string | null;
  following: {
    threadCount: number;
    followerCount: number;
    followingsCount: number;
    twitterFollowers: number;
    id: string;
    createdOn: string;
    twitterId: string;
    twitterHandle: string;
    twitterName: string;
    twitterPicture: string;
    bannerUrl: string;
    address: string;
    ethereumAddress: string | null;
    solanaAddress: string;
    prevAddress: string | null;
    addressConfirmed: boolean;
    twitterDescription: string;
    signedUp: boolean;
    subscriptionCurrency: string;
    subscriptionCurrencyAddress: string | null;
    subscriptionPrice: string;
    keyPrice: string;
    lastKeyPrice: string;
    subscriptionsEnabled: boolean;
    userConfirmed: boolean;
    twitterConfirmed: boolean;
    flag: number;
    ixHandle: string;
    handle: string;
  };
  followedByloggedInUser: boolean;
}

export interface FollowingResponse {
  followingsWithFollowedByloggedInUser: Following[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: string;
}
