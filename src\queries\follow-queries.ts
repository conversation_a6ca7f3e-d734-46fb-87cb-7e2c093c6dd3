import { useInfiniteQuery } from "@tanstack/react-query";

import { getFollowersList, getFollowingList } from "@/api/client/follow";

export const useFollowersInfiniteQuery = ({
  userId,
  searchString,
}: {
  userId?: string;
  searchString: string;
}) => {
  return useInfiniteQuery({
    queryKey: ["followers", "list", userId, searchString],
    queryFn: ({ pageParam }) => {
      if (!userId) return null;

      return getFollowersList({ userId, searchString, ...pageParam });
    },
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const totalPages = lastPage?.numberOfPages || 0;

      if (totalPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    enabled: !!userId,
  });
};

export const useFollowingInfiniteQuery = ({
  userId,
  searchString,
}: {
  userId?: string;
  searchString: string;
}) => {
  return useInfiniteQuery({
    queryKey: ["following", "list", userId, searchString],
    queryFn: ({ pageParam }) => {
      if (!userId) return null;

      return getFollowingList({ userId, searchString, ...pageParam });
    },
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const totalPages = lastPage?.numberOfPages || 0;

      if (totalPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    enabled: !!userId,
  });
};
