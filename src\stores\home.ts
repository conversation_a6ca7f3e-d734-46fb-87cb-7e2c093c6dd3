import { create } from "zustand";
import { StateSnapshot, VirtuosoHandle } from "react-virtuoso";

interface Store {
  followingSnapshot?: StateSnapshot;
  trendingSnapshot?: StateSnapshot;
  followingTimelineRef: { current: VirtuosoHandle | null };
  trendingTimelineRef: { current: VirtuosoHandle | null };
}

interface Actions {
  setFollowingSnapshot: (followingSnapshot?: StateSnapshot) => void;
  setTrendingSnapshot: (trendingSnapshot?: StateSnapshot) => void;
}

export const useHomeStore = create<Store & Actions>((set) => ({
  followingTimelineRef: {
    current: null,
  },
  trendingTimelineRef: {
    current: null,
  },
  followingSnapshot: undefined,
  trendingSnapshot: undefined,
  setFollowingSnapshot: (followingSnapshot) => set({ followingSnapshot }),
  setTrendingSnapshot: (trendingSnapshot) => set({ trendingSnapshot }),
}));
