"use client";

import { useMemo } from "react";

import { useThreadNestedAnswersInfiniteQuery } from "@/queries";
import { Thread } from "@/types";

import { ActivityIndicator } from "react-native";
import { Colors } from "@/constants/Colors";
import React from "react";
import { useLocalSearchParams } from "expo-router";
import { NestedMainPost } from "./nested-main-post";
import { MainPostLoadingSkeleton } from "@/components/post-loading-skeletons";

export const NestedPosts = () => {
  const params = useLocalSearchParams() as { id: string };
  const { data: nestedAnswersData, isLoading: isAnswersLoading } =
    useThreadNestedAnswersInfiniteQuery(params.id);

  const threads = useMemo(() => {
    if (!nestedAnswersData) return [];

    return nestedAnswersData.pages
      .reduce((prev, current) => {
        return [...prev, ...(current?.threads ?? [])];
      }, [] as Thread[])
      .reverse();
  }, [nestedAnswersData]);

  return (
    <>
      {isAnswersLoading && (
        <>
          <MainPostLoadingSkeleton />
          <MainPostLoadingSkeleton />
        </>
      )}
      {!isAnswersLoading &&
        nestedAnswersData &&
        threads.map((thread, index) => (
          <NestedMainPost key={thread.id} thread={thread} linkType={index === 0 ? "main" : "nested"}/>
        ))}
    </>
  );
};
