import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useNewUsersQuery } from "@/queries/user-queries";
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { ProfileItemCard } from "../_components/profile-item-card";
import { Colors } from "@/constants/Colors";

export default function NewProfiles() {
  const { data, isLoading } = useNewUsersQuery();

  if (isLoading)
    return (
      <ThemedView>
        <ActivityIndicator color={Colors.dark.offWhite} size="small" />
      </ThemedView>
    );

  return (
    <ThemedView style={styles.container}>
      <ThemedView>
        <FlatList
          data={data?.users || []}
          renderItem={({ item }) => <ProfileItemCard user={item} />}
          keyExtractor={(item, index) => item.id || index.toString()}
        />
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 24,
  },
});
