import React, { useState, useEffect } from "react";
import { View, Pressable, BackHandler } from "react-native";
import { Stack, useLocalSearchParams, router } from "expo-router";
import { ThemedView } from "@/components/ThemedView";
import { ThemedText } from "@/components/ThemedText";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { Colors } from "@/constants/Colors";
import { Search } from "@/components/ui/search";
import { useUserByHandleQuery } from "@/queries/user-queries";
import useThrottle from "@/hooks/use-throttle";
import FollowersTab from "./followers";
import FollowingTab from "./following";
import {
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  createMaterialTopTabNavigator,
} from "@react-navigation/material-top-tabs";
import { withLayoutContext } from "expo-router";
import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import { styled } from "nativewind";
import { Href } from "expo-router";
import { SearchStringContext } from "./SearchStringContext";

const { Navigator } = createMaterialTopTabNavigator();

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

export default function ConnectionsVerticalTabs() {
  const params = useLocalSearchParams<{ userHandle: string; tab?: string }>();
  const initialTab = params.tab === "following" ? "Following" : "Followers";
  const [searchString, setSearchString] = useState("");
  const throttledSearchString = useThrottle(searchString, 500);
  const { data: userData } = useUserByHandleQuery(
    params.userHandle as string
  );

  const handleBack = () => {
    router.push(`/${params.userHandle}` as Href<string>);
  };

  useEffect(() => {
    const onBackPress = () => {
      handleBack();
      return true; // prevent default behavior
    };
    BackHandler.addEventListener('hardwareBackPress', onBackPress);
    return () => BackHandler.removeEventListener('hardwareBackPress', onBackPress);
  }, [params.userHandle]);

  return (
    <SearchStringContext.Provider value={throttledSearchString}>
      <ThemedView className="flex-1 bg-dark-background">
        <Stack.Screen options={{ headerShown: false }} />

        {/* Header */}
        <View className="flex-row items-center justify-between px-4 pt-2 pb-3">
          <Pressable
            onPress={handleBack}
            className="p-1"
            accessibilityRole="button"
          >
            <ArrowBackOutlineIcon
              width={24}
              height={24}
              color={Colors.dark.offWhite}
            />
          </Pressable>
          <View className="flex-row items-center gap-x-1 mr-4">
         <ThemedText type="bold" className=" text-base font-medium">
                {userData?.user?.twitterName}
          </ThemedText>
          </View>

          <View className="w-8" />
        </View>

        {/* Search Input */}
        <View className="px-6 py-4 mt-1">
          <Search
            placeholder="Search "
            value={searchString}
            onChangeText={setSearchString}
          />
        </View>

        {/* Material Top Tabs */}
        <MaterialTopTabs
          initialRouteName={initialTab}
          screenOptions={{
            tabBarScrollEnabled: true,
            tabBarItemStyle: {
              flexGrow: 0,
              width: "auto",
              paddingHorizontal: 16,
            },
            tabBarStyle: {
              backgroundColor: Colors.dark.background,
              elevation: 0,
              shadowOpacity: 0,
              borderBottomWidth: 0,
            },
            tabBarIndicatorContainerStyle: {
              borderBottomWidth: 0.5,
              borderBottomColor: Colors.dark.darkGrey,
              width: '100%',
              alignSelf: 'stretch',
              marginHorizontal: 0,
              paddingHorizontal: 0,
            },
            tabBarIndicatorStyle: {
              backgroundColor: Colors.dark.primary,
              height: 1,
              marginHorizontal: 14,
            },
            tabBarActiveTintColor: Colors.dark.offWhite,
            tabBarInactiveTintColor: Colors.dark.darkGrey,
            tabBarLabelStyle: {
              textTransform: "none",
              fontSize: 14,
              fontWeight: "600",
              marginLeft: 28,
            },
            tabBarPressColor: "transparent",
          }}
        >
          <MaterialTopTabs.Screen name="followers" options={{ title: "Followers" }} />
          <MaterialTopTabs.Screen name="following" options={{ title: "Following" }} />
        </MaterialTopTabs>
      </ThemedView>
    </SearchStringContext.Provider>
  );
}
