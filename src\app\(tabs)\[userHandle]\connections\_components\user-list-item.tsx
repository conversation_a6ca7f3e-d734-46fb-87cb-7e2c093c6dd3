import { Link } from "expo-router";
import { Text, View } from "react-native";
import { useQueryClient } from "@tanstack/react-query";
import { InfiniteData } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import Avatar from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useFollowMutation, useUnfollowMutation } from "@/queries/follow-mutations";
import { useUser } from "@/stores/user";
import { ThemedText } from "@/components/ThemedText";
import { Href } from "expo-router";
import { User } from "@/types";
import { FollowersResponse, FollowingResponse, Follower, Following } from "@/queries/types/follow";

interface UserListItemProps {
  user: User; // User from follower or following
  isFollowing: boolean;
}

export const UserListItem = ({ user, isFollowing }: UserListItemProps) => {
  const queryClient = useQueryClient();
  const { user: me } = useUser();

  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${user.twitterName}!`);

      // Update followers list cache
      const previousFollowersLists = queryClient.getQueriesData({
        queryKey: ["followers", "list"],
      });

      queryClient.setQueriesData(
        { queryKey: ["followers", "list"] },
        (old: InfiniteData<FollowersResponse> | undefined) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              if (!page) return page;

              return {
                ...page,
                followersWithFollowedByloggedInUser:
                  page.followersWithFollowedByloggedInUser?.map((item: Follower) => {
                    if (item.follower?.id === user.id) {
                      return {
                        ...item,
                        followedByloggedInUser: true,
                      };
                    }
                    return item;
                  }),
              };
            }),
          };
        }
      );

      // Update following list cache
      const previousFollowingLists = queryClient.getQueriesData({
        queryKey: ["following", "list"],
      });

      queryClient.setQueriesData(
        { queryKey: ["following", "list"] },
        (old: InfiniteData<FollowingResponse> | undefined) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              if (!page) return page;

              return {
                ...page,
                followingsWithFollowedByloggedInUser:
                  page.followingsWithFollowedByloggedInUser?.map((item: Following) => {
                    if (item.following?.id === user.id) {
                      return {
                        ...item,
                        followedByloggedInUser: true,
                      };
                    }
                    return item;
                  }),
              };
            }),
          };
        }
      );

      return { previousFollowersLists, previousFollowingLists };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to follow ${user.twitterName}.`);

      queryClient.setQueriesData(
        { queryKey: ["followers", "list"] },
        context?.previousFollowersLists
      );

      queryClient.setQueriesData(
        { queryKey: ["following", "list"] },
        context?.previousFollowingLists
      );
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      toast.green(`You've unfollowed ${user.twitterName}.`);

      // Update followers list cache
      const previousFollowersLists = queryClient.getQueriesData({
        queryKey: ["followers", "list"],
      });

      queryClient.setQueriesData(
        { queryKey: ["followers", "list"] },
        (old: InfiniteData<FollowersResponse> | undefined) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              if (!page) return page;

              return {
                ...page,
                followersWithFollowedByloggedInUser:
                  page.followersWithFollowedByloggedInUser?.map((item: Follower) => {
                    if (item.follower?.id === user.id) {
                      return {
                        ...item,
                        followedByloggedInUser: false,
                      };
                    }
                    return item;
                  }),
              };
            }),
          };
        }
      );

      // Update following list cache
      const previousFollowingLists = queryClient.getQueriesData({
        queryKey: ["following", "list"],
      });

      queryClient.setQueriesData(
        { queryKey: ["following", "list"] },
        (old: InfiniteData<FollowingResponse> | undefined) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              if (!page) return page;

              return {
                ...page,
                followingsWithFollowedByloggedInUser:
                  page.followingsWithFollowedByloggedInUser?.map((item: Following) => {
                    if (item.following?.id === user.id) {
                      return {
                        ...item,
                        followedByloggedInUser: false,
                      };
                    }
                    return item;
                  }),
              };
            }),
          };
        }
      );

      return { previousFollowersLists, previousFollowingLists };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to unfollow ${user.twitterName}.`);

      queryClient.setQueriesData(
        { queryKey: ["followers", "list"] },
        context?.previousFollowersLists
      );

      queryClient.setQueriesData(
        { queryKey: ["following", "list"] },
        context?.previousFollowingLists
      );
    },
  });

  const handleFollow = () => {
    if (isFollowing) {
      unfollow({ userId: user.id });
    } else {
      follow({ userId: user.id });
    }
  };

  return (
    <View className="flex-row justify-between items-center px-6 py-4">
      <View className="flex-row items-center gap-4">
        <Link href={`/${user.twitterHandle}` as Href<string>}>
          <Avatar src={user.twitterPicture} size={42} />
        </Link>
        <View className="gap-1">
          <Link href={`/${user.twitterHandle}` as Href<string>}>
            <ThemedText className="text-[#F4F4F4] font-semibold">{user.twitterName}</ThemedText>
          </Link>
          <Link href={`/${user.twitterHandle}` as Href<string>}>
            <ThemedText className="text-[#808080]">@{user.twitterHandle}</ThemedText>
          </Link>
        </View>
      </View>
      {me?.id !== user.id && (
        <Button
        variant={isFollowing ? "outline" : "secondary"}
        onPress={handleFollow}
        style={{ width: 96, paddingVertical: 6}}
      >
        {isFollowing ? "Unfollow" : "Follow"}
    </Button>
      )}
    </View>
  );
};

