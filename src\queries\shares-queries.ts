import { useQuery } from "@tanstack/react-query";

import {
  getSharesHolders,
  getSharesHoldings,
  getSharesStats,
} from "@/api/client/shares";

import { SharesHoldingsResponse, SharesStatsResponse } from "./types";

export const useSharesStatsQuery = ({ userId }: { userId?: string }) => {
  return useQuery<SharesStatsResponse>({
    queryKey: ["shares", "stats", userId],
    queryFn: async () => {
      if (!userId) return null;

      return await getSharesStats({ userId });
    },
  });
};

export const useSharesHoldersQuery = (
  data: {
    userId?: string;
    enabled?: boolean;
  } = {
    enabled: true,
  },
) => {
  return useQuery({
    queryKey: ["shares", "holders", data?.userId],
    queryFn: () => {
      if (!data.enabled) return null;

      return getSharesHolders(data?.userId);
    },
  });
};

export const useSharesHoldingsQuery = () => {
  return useQuery<SharesHoldingsResponse>({
    queryKey: ["shares", "holdings"],
    queryFn: getSharesHoldings,
  });
};
