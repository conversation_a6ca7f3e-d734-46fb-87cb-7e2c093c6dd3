import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import {
  deleteRepost,
  deleteThread,
  postBookmarkThread,
  postLikeThread,
  postPinThread,
  postQuote,
  postRepost,
  postThread,
  postThreadAnswer,
  postTipThread,
  postUnbookmarkThread,
  postUnlikeThread,
} from "@/api/client/threads";
import {
  PostQuoteData,
  PostThreadAnswerData,
  PostThreadData,
  PostThreadResponse,
  QuoteResponse,
  ThreadActionData,
  ThreadResponse,
} from "@/types";

type PostThreadMutationType = MutationOptions<
  PostThreadResponse,
  DefaultError,
  PostThreadData,
  any
>;

export const usePostThreadMutation = (options?: PostThreadMutationType) => {
  return useMutation({
    mutationFn: postThread,
    ...options,
  });
};

type PostThreadAnswerMutationType = MutationOptions<
  ThreadResponse,
  DefaultError,
  PostThreadAnswerData,
  any
>;

export const usePostThreadAnswerMutation = (
  options?: PostThreadAnswerMutationType,
) => {
  return useMutation({
    mutationFn: postThreadAnswer,
    ...options,
  });
};

type PostQuoteMutationType = MutationOptions<
  QuoteResponse,
  DefaultError,
  PostQuoteData,
  any
>;

export const usePostQuoteMutation = (options?: PostQuoteMutationType) => {
  return useMutation({
    mutationFn: postQuote,
    ...options,
  });
};

type PostRepostMutationType = MutationOptions<
  ThreadResponse,
  DefaultError,
  {
    threadId: string;
  },
  any
>;

export const useRepostThreadMutation = (options?: PostRepostMutationType) => {
  return useMutation({
    mutationFn: postRepost,
    ...options,
  });
};

type DeleteRepostMutationType = MutationOptions<
  ThreadResponse,
  DefaultError,
  {
    threadId: string;
  },
  any
>;

export const useDeleteRepostMutation = (options?: DeleteRepostMutationType) => {
  return useMutation({
    mutationFn: deleteRepost,
    ...options,
  });
};

type ActionThreadMutationType = MutationOptions<
  unknown,
  DefaultError,
  ThreadActionData,
  any
>;

export const useLikeThreadMutation = (options?: ActionThreadMutationType) => {
  return useMutation({
    mutationFn: postLikeThread,
    ...options,
  });
};

export const useUnlikeThreadMutation = (options?: ActionThreadMutationType) => {
  return useMutation({
    mutationFn: postUnlikeThread,
    ...options,
  });
};

export const useBookmarkThreadMutation = (
  options?: ActionThreadMutationType,
) => {
  return useMutation({
    mutationFn: postBookmarkThread,
    ...options,
  });
};

export const useUnbookmarkThreadMutation = (
  options?: ActionThreadMutationType,
) => {
  return useMutation({
    mutationFn: postUnbookmarkThread,
    ...options,
  });
};

type TipThreadMutationType = MutationOptions<
  unknown,
  DefaultError,
  {
    threadId?: string;
    tipAmount: string;
    userId: string;
    currency: string;
  },
  any
>;

export const useTipThreadMutation = (options?: TipThreadMutationType) => {
  return useMutation({
    mutationFn: postTipThread,
    ...options,
  });
};

type PinThreadMutationType = MutationOptions<
  ThreadResponse,
  DefaultError,
  {
    threadId: string;
    isPinned: boolean;
  },
  any
>;

export const usePinThreadMutation = (options?: PinThreadMutationType) => {
  return useMutation({
    mutationFn: postPinThread,
    ...options,
  });
};

export const useDeleteThreadMutation = (options?: ActionThreadMutationType) => {
  return useMutation({
    mutationFn: deleteThread,
    ...options,
  });
};
