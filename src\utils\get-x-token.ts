import { env } from "@/env";
import { dynamicClient } from '@/dynamicClient';

export async function getOAuthAccessToken(): Promise<string | null> {
  const token = dynamicClient.auth.token;

  const oauthAccountId = dynamicClient.auth.authenticatedUser?.verifiedCredentials?.find(
    (cred: any) => cred.oauthProvider === "twitter",
  )?.id;

  if (!token || !oauthAccountId) {
    console.error("Missing required dynamic authentication details: token or oauthAccountId");
    return null;
  }

  const apiUrl = `https://app.dynamicauth.com/api/v0/sdk/${env.EXPO_PUBLIC_DYNAMIC_ENVIRONMENT_ID}/oauthAccounts/${oauthAccountId}/accessToken`;

  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
        console.error(`Error retrieving x token: ${response.statusText}`);
        return null;
    }

    const data = await response.json();

    if (data?.accessToken) {
      return data.accessToken;
    } else {
        return null;
    }
  } catch (error) {
    console.error("Error fetching OAuth access token:", error);
    return null;
  }
}
