import "react-native-get-random-values"
import "@ethersproject/shims"
import {
  formatEther as _formatEther,
  formatUnits as _formatUnit,
} from "ethers";

const formatter = new Intl.NumberFormat("en-US", {
  maximumFractionDigits: 4,
});

export const formatEther = (price: string) => {
  return formatter.format(Number(_formatEther(price)));
};

export const formatAvax = (price: string) => {
  const avax = Number(_formatEther(price));
  const formatter = new Intl.NumberFormat("en-US", {
    maximumFractionDigits: avax > 0.1 ? 2 : 4,
    minimumFractionDigits: 2,
  });
  return formatter.format(avax);
};

export const formatUnits = (price: string, decimals: number) => {
  return formatter.format(Number(_formatUnit(price, decimals)));
};
