import { create<PERSON>ontext, ReactNode, useContext, useMemo, useState, useCallback } from "react";

import { useQueryClient } from "@tanstack/react-query";
import { createStore, StoreApi, useStore } from "zustand";

import { useGroupByIdQuery, useIsBlockedByUserQuery, useIsUserBlockedQuery, useUserByIdQuery } from "@/queries";
import { FileType, HeaderGroupResponse, MessageType } from "@/queries/types/chats";
import { useLocalSearchParams } from "expo-router";
import { useUser } from "@/stores";
import { User, UserFlaggedEnum } from "@/types";
import { Undefined } from "@/types/common";

interface TPreview {
  uri: string;
  type: "image" | "video";
  progress: number;
  loading: boolean;
}

type GroupContextType = {
  data: HeaderGroupResponse | undefined;
  isLoading: boolean;
  isSuccess: boolean;
  groupId: string;
  twitterHandle: string | null;
  userHandle: string | undefined;
  updateIsRequest: (isRequest: boolean) => void;
  store: StoreApi<GroupState>;
  isOwnerSuspended: boolean;
  chatMate: {
    user: User;
  } | undefined
  isChatMateLoading: boolean;
  amIOwner: boolean;
  isOwnerLoading: boolean;
  isAttachImageOpen: boolean;
  setIsAttachImageOpen: React.Dispatch<React.SetStateAction<boolean>>
  messageToReplyTo: MessageType | null;
  setMessageToReplyTo: React.Dispatch<React.SetStateAction<MessageType | null>>
  preview: TPreview | null;
  setPreview: React.Dispatch<React.SetStateAction<TPreview | null>>
  files: FileType[];
  setFiles: React.Dispatch<React.SetStateAction<FileType[]>>
  groupName: Undefined<string>;
  isBlockAPILoading: boolean;
  isYouBlockedUser: boolean;
  isYouBlockedByUser: boolean;
  isSuspended: boolean;
  isAdmin: boolean;
  refetchData: () => void;
};

interface Store {
  messageId: string;
  initialLoad: boolean;
  pinnedMessagesCount: number | null;
  isLoadingOverlay: boolean;
}

interface Actions {
  actions: {
    setMessageId: (messageId: string) => void;
    setInitialLoad: (initialLoad: boolean) => void;
    resetMessageId: () => void;
    setPinnedMessagesCount: (pinnedMessagesCount: number) => void;
    setLoadingOverlay: (isLoadingOverlay: boolean) => void;
  };
}

type GroupState = Store & Actions;

const GroupContext = createContext<GroupContextType | undefined>(undefined);

export function GroupProvider({ children }: { children: ReactNode }) {
  const params = useLocalSearchParams() as { groupId: string };
  const { user } = useUser();
  const queryClient = useQueryClient();
  const [isAttachImageOpen, setIsAttachImageOpen] = useState(false);
  const [messageToReplyTo, setMessageToReplyTo] = useState<MessageType | null>(null);
  const [preview, setPreview] = useState<TPreview | null>(null);
  const [files, setFiles] = useState<FileType[]>([]);

  const [store] = useState(() =>
    createStore<GroupState>((set, get) => ({
      messageId: "",
      isLoadingOverlay: false,
      initialLoad: true,
      pinnedMessagesCount: null,
      actions: {
        setMessageId: (messageId) => {
          set({ messageId, initialLoad: true });
        },
        resetMessageId: () => {
          set({ messageId: "", initialLoad: true });
        },
        setLoadingOverlay: (isLoadingOverlay) => {
          set({ isLoadingOverlay });
        },
        setInitialLoad: (initialLoad) => {
          set({ initialLoad });
        },
        setPinnedMessagesCount: (pinnedMessagesCount) => {
          set({ pinnedMessagesCount });
        },
      },
    })),
  );

  const { data, isLoading: isGroupLoading, isSuccess, refetch: refetchGroup, isFetching: isGroupFetching } = useGroupByIdQuery({
    groupId: params.groupId,
    twitterHandle: user?.twitterHandle || "",
  });

  const { group, chatMateSettings } = data || {};

  const isEnabled = !isGroupLoading && isSuccess && !!group

  const { 
    data: chatMate,  
    isLoading: isChatMateLoading, 
    refetch: refetchChatMate, 
    isFetching: isChatMateFetching 
  } = useUserByIdQuery(group?.chatMateId || "", isEnabled);

  const { 
    data: owner, 
    isLoading: isOwnerLoading, 
    refetch: refetchOwner, 
    isFetching: isOwnerFetching 
  } = useUserByIdQuery(group?.ownerUserId || "", isEnabled);

  const amIOwner = useMemo(() => {
    return user !== null && user?.id === group?.ownerUserId;
  }, [user, user?.id, group?.ownerUserId]);

  const isOwnerSuspended = useMemo(() => {
    if (group?.isDirect && amIOwner) {
      return chatMate?.user?.flag === UserFlaggedEnum.SUSPENDED;
    } else {
      return owner?.user?.flag === UserFlaggedEnum.SUSPENDED;
    }
  }, [group?.isDirect, amIOwner, chatMate?.user?.flag, owner?.user?.flag])

  const userHandle = useMemo(() => {
    if (group?.isDirect && amIOwner) return chatMate?.user?.twitterHandle;
    else return owner?.user?.twitterHandle
  }, [group?.isDirect, amIOwner, chatMate?.user?.twitterHandle, owner?.user?.twitterHandle]);

  const groupName = useMemo(() => {
    return (isOwnerSuspended ? "Suspended Account" :
      amIOwner ? group?.chatMateName :
        group?.name) || group?.name;
  }, [isOwnerSuspended, amIOwner, group?.chatMateName, group?.name]);

  /* 
  chatmate is person to whom loggedin user is trying to chat
  isYouBlockedUser is true if the loggedin user blocked the chatmate
  */
  const {
    data: isYouBlockedUser,
    isLoading: isYouBlockedUserLoading,
    refetch: refetchIsYouBlockedUser,
    isFetching: isYouBlockedUserFetching
  } = useIsUserBlockedQuery(chatMateSettings?.chatMate?.id);

  //isYouBlockedByUser is true if the loggedin user is blocked by the chatmate
  const {
    data: isYouBlockedByUser,
    isLoading: isYouBlockedByUserLoading,
    refetch: refetchIsYouBlockedByUser,
    isFetching: isYouBlockedByUserFetching
  } = useIsBlockedByUserQuery(chatMateSettings?.chatMate?.id);

  const isBlockAPILoading =
    isYouBlockedUserLoading ||
    isYouBlockedByUserLoading ||
    isYouBlockedUserFetching ||
    isYouBlockedByUserFetching;

  const ADMIN_ID = "d4b0b964-6122-4c9b-8759-19cc17c0e6a0";
  const isAdmin = user?.id === ADMIN_ID;
  const isSuspended =
    group
      ? group.isDirect
        ? chatMateSettings?.chatMate?.flag === UserFlaggedEnum.SUSPENDED
        : owner?.user?.flag === UserFlaggedEnum.SUSPENDED
      : owner?.user?.flag === UserFlaggedEnum.SUSPENDED;

  const updateIsRequest = useCallback((isRequest: boolean) => {
    queryClient.setQueryData<HeaderGroupResponse | undefined>(
      ["chat", "group", params.groupId],
      (oldData) => {
        if (oldData && oldData.group) {
          return {
            ...oldData,
            group: { ...oldData.group, isRequest },
          };
        }
        return oldData;
      },
    );
  }, [params.groupId]);

  const refetchData = useCallback(() => {
    refetchGroup();
    refetchChatMate();
    refetchOwner();
    refetchIsYouBlockedUser();
    refetchIsYouBlockedByUser();
  }, []);

  return (
    <GroupContext.Provider
      value={{
        data,
        isLoading: isGroupLoading || isGroupFetching,
        isSuccess,
        groupId: params.groupId,
        twitterHandle: user?.twitterHandle || '',
        userHandle,
        updateIsRequest,
        store,
        isOwnerSuspended,
        chatMate,
        isChatMateLoading: isChatMateLoading || isChatMateFetching,
        amIOwner,
        isOwnerLoading: isOwnerLoading || isOwnerFetching,
        isAttachImageOpen,
        setIsAttachImageOpen,
        messageToReplyTo,
        setMessageToReplyTo,
        preview,
        setPreview,
        files,
        setFiles,
        groupName,
        isBlockAPILoading,
        isYouBlockedUser: isYouBlockedUser || false,
        isYouBlockedByUser: isYouBlockedByUser || false,
        isSuspended,
        isAdmin,
        refetchData
      }}
    >
      {children}
    </GroupContext.Provider>
  );
}

export function useGroup() {
  const context = useContext(GroupContext);
  const data = context && context.data;
  const isLoading = context ? context.isLoading : true;
  const isSuccess = context ? context.isSuccess : true;
  const groupId = context && context.groupId;
  const twitterHandle = context?.twitterHandle;
  const updateIsRequest = context?.updateIsRequest;
  const isOwnerSuspended = context?.isOwnerSuspended;
  const chatMate = context?.chatMate;
  const isChatMateLoading = context?.isChatMateLoading;
  const isOwnerLoading = context?.isOwnerLoading;
  const amIOwner = context?.amIOwner;
  const isAttachImageOpen = context?.isAttachImageOpen;
  const setIsAttachImageOpen = context?.setIsAttachImageOpen;
  const messageToReplyTo = context?.messageToReplyTo;
  const setMessageToReplyTo = context?.setMessageToReplyTo;
  const preview = context?.preview;
  const setPreview = context?.setPreview;
  const files = context?.files;
  const setFiles = context?.setFiles;
  const groupName = context?.groupName;
  const userHandle = context?.userHandle;
  const isBlockAPILoading = context?.isBlockAPILoading;
  const isYouBlockedUser = context?.isYouBlockedUser;
  const isYouBlockedByUser = context?.isYouBlockedByUser;
  const isSuspended = context?.isSuspended;
  const isAdmin = context?.isAdmin;
  const refetchData = context?.refetchData;
  
  return {
    data,
    isLoading,
    isSuccess,
    groupId,
    twitterHandle,
    updateIsRequest,
    isOwnerSuspended,
    chatMate,
    isChatMateLoading,
    amIOwner,
    isOwnerLoading,
    isAttachImageOpen,
    setIsAttachImageOpen,
    messageToReplyTo,
    setMessageToReplyTo,
    preview,
    setPreview,
    files,
    setFiles,
    groupName,
    userHandle,
    isBlockAPILoading,
    isYouBlockedUser,
    isYouBlockedByUser,
    isSuspended,
    isAdmin,
    refetchData
  };
}

export function useGroupStore(): GroupState;
export function useGroupStore<T>(selector: (state: GroupState) => T): T;
export function useGroupStore<T>(selector?: (state: GroupState) => T) {
  const context = useContext(GroupContext);
  if (!context) {
    throw new Error("Missing GroupContextProvider");
  }
  const store = context.store;

  return useStore(store, selector!);
}
