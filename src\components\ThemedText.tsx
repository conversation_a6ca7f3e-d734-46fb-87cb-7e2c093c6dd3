import { Text, type TextProps } from "react-native";
import { globalStyles } from "@/app/globalStyles";

import { useThemeColor } from "@/hooks/useThemeColor";

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?:
  | "default"
  | "defaultGrey"
  | "title"
  | "defaultSemiBold"
  | "subtitle"
  | "link"
  | "lightGreySubtitle"
  | "greyText"
  | "bold"
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = "default",
  children,
  ...rest
}: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, "text");

  return (
      <Text
        style={[
          { color },
          type === "default" ? globalStyles.default : undefined,
          type === "defaultGrey" ? globalStyles.defaultGrey : undefined,
          type === "title" ? globalStyles.title : undefined,
          type === "defaultSemiBold" ? globalStyles.defaultSemiBold : undefined,
          type === "subtitle" ? globalStyles.subtitle : undefined,
          type === "link" ? globalStyles.link : undefined,
          type === "lightGreySubtitle"
            ? globalStyles.lightGreySubtitle
            : undefined,
          type === "greyText" ? globalStyles.greyText : undefined,
          { fontFamily: "InterRegular" },
          type === "bold" ? globalStyles.fontBold : undefined,
          style,
        ]}
        {...rest}
      >
        {children}
      </Text>
  );
}
