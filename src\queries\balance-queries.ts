import { useQuery } from "@tanstack/react-query";

import { getPrice, getSolanaBalance, getTokenPrice, refreshBalance } from "@/api/balance";

export const useBalancesQuery = ({ address }: { address?: string }) => {
  return useQuery({
    queryKey: ["wallet", "balances", address],
    queryFn: async () => {
      if (!address) return null;
      return await refreshBalance(address);
    },
    enabled: !!address,
  });
};

export const usePriceQuery = ({ address }: { address?: string }) => {
  return useQuery({
    queryKey: ["wallet", "price", address],
    queryFn: async () => {
      if (!address) return null;
      return await getPrice(address);
    },
  });
};

export const useSolanaBalanceQuery = ({ address }: { address?: string }) => {
  return useQuery({
    queryKey: ["wallet", "solana_balance", address],
    queryFn: async () => {
      if (!address) return null;
      return await getSolanaBalance(address);
    },
    enabled: !!address,
  });
};

export const useGetTokenPriceQuery = () => {
  return useQuery({
    queryKey: ["token", "price"],
    queryFn: async () => {
      return await getTokenPrice();
    },
  });
};
