"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { View } from "react-native";

export const UserListItemLoadingSkeleton = () => {
  return (
    <View className="flex-row w-full justify-between space-x-4 px-6 py-4">
      <View className="flex-row items-center gap-[10px]">
        <Skeleton circle height={42} width={42} />
        <View className="flex w-full flex-col gap-1 leading-4">
          <Skeleton height={14} width={112} style={{marginTop: 4}} />
          <Skeleton height={12} width={80} />
        </View>
      </View>
    </View>
  );
};
