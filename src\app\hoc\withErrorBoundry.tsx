import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { navigationRoutes } from '@/navigationRoutes';
import { usePathname, useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import { ErrorBoundary, FallbackProps } from 'react-error-boundary';
import { ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import Svg, { G, Path } from 'react-native-svg';

interface ErrorInfo {
  componentStack?: string | null;
  digest?: string | null;
}

const getCurrentScreenName = () => {
  const pathname = usePathname();
  const pathSegments = pathname.split('/');
  return pathSegments[pathSegments.length - 1] || pathname;
};

export const FallBackComponent = ({
  error,
  resetErrorBoundary,
}: FallbackProps) => {
  const router = useRouter();
  // log crashes
  useEffect(() => {
    const errorData = {
      name: error.name,
      message: error.message,
      stack: error.componentStack,
    };

    console.error(errorData);
  }, [error]);

  const handleReset = () => {
    resetErrorBoundary();
    try {
      router.back();

      setTimeout(() => {
        const currentPath = getCurrentScreenName();
        // If we're still on the same screen after trying to go back,
        // force navigation to home
        if (getCurrentScreenName() === currentPath) {
          router.replace(navigationRoutes.home);
        }
      }, 100);
    } catch (e) {
      // Last resort - force navigation to home
      router.replace(navigationRoutes.home);
    }
  };

  // Remove the Stack.Screen component that's causing the error
  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.arenaLogo}>
        <ArenaLogo />
      </ThemedText>
      <ThemedText type="title" style={styles.headerText}>
        Something went wrong
      </ThemedText>

      <ThemedText type="defaultSemiBold" style={styles.moduleText}>
        Error occurred in module: {getCurrentScreenName()}
      </ThemedText>

      <ScrollView style={styles.errorContainer}>
        <ThemedText type="defaultSemiBold" style={styles.moduleNameText}>
          Module: {getCurrentScreenName()}
        </ThemedText>
        <ThemedText type="default" style={styles.errorMessage}>
          {error.message}
        </ThemedText>
        <ThemedText type="defaultGrey" style={styles.stackTrace}>
          {error.componentStack}
        </ThemedText>
      </ScrollView>

      <ThemedView style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={resetErrorBoundary}
          accessibilityLabel="try_again_button"
        >
          <ThemedText type="link" style={styles.buttonText}>
            Go Back
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </ThemedView>
  );
};

export const withErrorBoundary = (StackComponent: React.ComponentType<any>) => {
  const WithErrorBoundary = (props: any) => {
    const logError = (error: Error, info: ErrorInfo) => {
      console.log('Error:', error);
      console.log('Component stack:', info);
    };

    return (
      <ErrorBoundary FallbackComponent={FallBackComponent} onError={logError}>
        <StackComponent {...props} />
      </ErrorBoundary>
    );
  };

  return WithErrorBoundary;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  headerText: {
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  moduleText: {
    marginBottom: 16,
  },
  errorContainer: {
    padding: 16,
    marginTop: 16,
    width: '100%',
    maxHeight: 300,
    borderLeftWidth: 2,
    borderColor: '#808080',
  },
  moduleNameText: {
    marginBottom: 8,
  },
  errorMessage: {
    marginTop: 8,
    marginBottom: 12,
  },
  stackTrace: {
    marginTop: 8,
  },
  buttonContainer: {
    width: '100%',
    marginTop: 32,
    alignItems: 'center',
  },
  button: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    width: '100%',
    backgroundColor: '#EB540A',
  },
  buttonText: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  arenaLogo: {
    marginBottom: -9,
  },
});

const ArenaLogo = () => (
  <Svg width={72} height={100} viewBox="0 0 202 263" fill="none">
    <G>
      <Path
        d="M86.3957 203.945H65.3498L64.1061 207.07H71.8442L66.604 223.755H74.5866L78.6257 207.07H85.8855L86.3957 203.945Z"
        fill="#EB540A"
      />
      <Path
        d="M111.714 203.945H105.156L105.464 211.097H96.7695L97.0884 203.945H90.7002L88.3936 223.755H96.1849L96.5994 214.838H105.443L105.815 223.755H114.01L111.714 203.945Z"
        fill="#EB540A"
      />
      <Path
        d="M137.097 203.945H116.094L119.357 223.755H144.952L143.209 219.334H126.096L125.012 214.711H137.916L136.566 210.981H124.14L123.215 207.07H138.341L137.097 203.945Z"
        fill="#EB540A"
      />
      <Path
        d="M22.5877 244.107L26.8076 237.093C27.1052 236.604 27.541 236.136 28.0618 235.722C28.5827 235.307 29.2098 234.935 29.8794 234.627C30.5491 234.319 31.2825 234.074 32.0266 233.894C32.7706 233.724 33.5253 233.628 34.2693 233.628C35.0134 233.628 35.6511 233.724 36.2039 233.894C36.7566 234.064 37.203 234.308 37.5219 234.627C37.8514 234.935 38.0534 235.307 38.1065 235.722C38.1703 236.136 38.0852 236.604 37.8301 237.093L34.2268 244.107H22.5877ZM37.5857 227.847C35.7574 227.847 33.876 228.07 32.0372 228.474C30.1877 228.877 28.3701 229.473 26.68 230.217C24.9687 230.971 23.3637 231.874 21.9925 232.905C20.5894 233.947 19.4096 235.116 18.5699 236.37L0.935898 262.599H11.7565L19.0482 250.441H31.0061L24.7774 262.599H35.5767L47.1201 236.37C47.6729 235.116 47.7898 233.947 47.5559 232.905C47.3221 231.885 46.7375 230.971 45.8553 230.217C44.9836 229.473 43.8357 228.877 42.4326 228.474C41.0508 228.07 39.4139 227.847 37.5857 227.847Z"
        fill="#EB540A"
      />
      <Path
        d="M167.965 244.107L164.361 237.05C164.106 236.561 164.032 236.094 164.096 235.679C164.159 235.265 164.361 234.893 164.691 234.584C165.02 234.276 165.467 234.032 166.02 233.862C166.572 233.692 167.221 233.596 167.965 233.596C168.709 233.596 169.453 233.692 170.197 233.862C170.941 234.032 171.664 234.276 172.333 234.584C173.003 234.893 173.619 235.265 174.151 235.679C174.682 236.094 175.108 236.561 175.405 237.05L179.646 244.107H167.965ZM164.861 227.847C163.033 227.847 161.396 228.07 160.014 228.474C158.622 228.877 157.463 229.473 156.591 230.217C155.709 230.971 155.124 231.874 154.891 232.905C154.646 233.947 154.774 235.116 155.326 236.37L166.87 262.599H177.69L171.451 250.441H183.409L190.701 262.599H201.5L183.866 236.37C183.026 235.116 181.847 233.947 180.443 232.905C179.062 231.885 177.467 230.971 175.756 230.217C174.066 229.473 172.248 228.877 170.399 228.474C168.571 228.07 166.7 227.847 164.861 227.847Z"
        fill="#EB540A"
      />
      <Path
        d="M60.4812 240.153L62.5858 233.628H70.6853C71.0892 233.628 71.4294 233.66 71.7164 233.713C72.0034 233.766 72.2372 233.851 72.4073 233.968C72.5773 234.085 72.6943 234.234 72.7474 234.404C72.8005 234.584 72.8005 234.786 72.7474 235.031L71.9396 238.474C71.8758 238.75 71.7482 238.995 71.5676 239.197C71.3869 239.409 71.153 239.579 70.8554 239.728C70.5578 239.866 70.207 239.973 69.7925 240.047C69.3779 240.121 68.9102 240.153 68.3894 240.153H60.4812ZM75.7555 228.527H56.0913L42.7303 262.599H53.2533L58.4191 246.54H67.0288C67.5709 246.54 68.028 246.583 68.4107 246.679C68.7933 246.774 69.1016 246.923 69.3248 247.114C69.5586 247.306 69.7074 247.55 69.7712 247.837C69.8456 248.134 69.835 248.475 69.7393 248.868L66.5505 262.588H77.3499L79.784 247.348C79.8584 246.859 79.869 246.413 79.8052 246.009C79.7415 245.605 79.6033 245.233 79.4013 244.904C79.1994 244.574 78.923 244.277 78.5723 244.011C78.2215 243.745 77.8069 243.522 77.318 243.331C77.8813 243.108 78.3809 242.842 78.8274 242.544C79.2632 242.247 79.6458 241.907 79.9647 241.524C80.2836 241.152 80.5387 240.727 80.7406 240.281C80.9426 239.834 81.0808 239.345 81.1552 238.825L82.1756 232.427C82.2181 232.129 82.2287 231.853 82.1756 231.587C82.1331 231.322 82.0374 231.077 81.8992 230.843C81.761 230.61 81.5697 230.386 81.3359 230.185C81.102 229.983 80.815 229.791 80.4855 229.611C80.156 229.43 79.8052 229.271 79.4438 229.143C79.0825 229.005 78.6998 228.899 78.3065 228.814C77.9132 228.729 77.4987 228.654 77.0735 228.612C76.6696 228.548 76.2232 228.527 75.7555 228.527Z"
        fill="#EB540A"
      />
      <Path
        d="M115.105 228.527H88.3939L84.5992 262.599H119.219L118.262 254.628H95.4517L95.7813 246.54H112.735L112.182 240.153H96.047L96.3127 233.628H115.722L115.105 228.527Z"
        fill="#EB540A"
      />
      <Path
        d="M146.355 228.527H137.596L142.231 243.214L129.667 228.527H120.632L126.382 262.599H137.756L133.1 243.618L148.491 262.599H159.716L146.355 228.527Z"
        fill="#EB540A"
      />
    </G>
    <G>
      <Path
        d="M183.749 83.1952V224.733H180.167V97.5317C180.167 54.0014 144.771 18.6117 101.255 18.6117C57.7386 18.6117 22.3219 54.0014 22.3219 97.5317V224.743H18.7398V83.1952C18.7398 37.6244 55.6765 0.693695 101.255 0.693695C146.812 0.693695 183.749 37.6244 183.749 83.1952Z"
        fill="#EB540A"
      />
      <Path
        d="M169.421 91.2719V211.352H165.838V103.451C165.838 67.8276 136.863 38.8569 101.255 38.8569C65.6469 38.8569 36.6502 67.8276 36.6502 103.451V211.352H33.0681V91.2719C33.0681 53.6186 63.5954 23.0963 101.255 23.0963C138.893 23.0963 169.421 53.6186 169.421 91.2719Z"
        fill="#EB540A"
      />
      <Path
        d="M155.092 99.3488V197.972H151.51V109.381C151.51 81.6646 128.966 59.1236 101.255 59.1236C73.5445 59.1236 50.9892 81.6646 50.9892 109.381V197.972H47.4071V99.3488C47.4071 69.613 71.525 45.5098 101.255 45.5098C130.975 45.4991 155.092 69.613 155.092 99.3488Z"
        fill="#EB540A"
      />
      <Path
        d="M140.753 107.426V184.592H137.171V115.311C137.171 95.491 121.057 79.3796 101.255 79.3796C81.4525 79.3796 65.3172 95.491 65.3172 115.311V184.592H61.7351V107.426C61.7351 85.6074 79.4329 67.9125 101.255 67.9125C123.055 67.9019 140.753 85.6074 140.753 107.426Z"
        fill="#EB540A"
      />
      <Path
        d="M126.425 115.492V171.202H122.843V121.231C122.843 109.317 113.16 99.6252 101.255 99.6252C89.35 99.6252 79.6455 109.307 79.6455 121.231V171.202H76.0634V115.492C76.0634 101.591 87.3517 90.3048 101.255 90.3048C115.137 90.3155 126.425 101.591 126.425 115.492Z"
        fill="#EB540A"
      />
      <Path
        d="M112.097 123.569V157.822H108.514V127.161C108.514 123.144 105.251 119.892 101.255 119.892C97.2581 119.892 93.9842 123.155 93.9842 127.161V157.822H90.4022V123.569C90.4022 117.586 95.2704 112.718 101.255 112.718C107.239 112.718 112.097 117.586 112.097 123.569Z"
        fill="#EB540A"
      />
    </G>
  </Svg>
);
