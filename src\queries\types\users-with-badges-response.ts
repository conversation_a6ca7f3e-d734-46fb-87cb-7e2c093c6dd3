interface Badge {
  id: number;
  userId: string;
  badgeType: number;
  order: number;
}

interface Stats {
  buys: number;
  sells: number;
  feesPaid: string;
  feesEarned: string;
  amountSpent: string;
  amountEarned: string;
  referralsEarned: string;
  distributedToShareholders: string;
  badgeType: number;
  id: string;
  userId: string;
  supply: string;
  volume: string;
  keyPrice: string;
}

interface User {
  threadCount: number;
  followerCount: number;
  followingsCount: number;
  twitterFollowers: number;
  id: string;
  createdOn: string;
  twitterId: string;
  twitterHandle: string;
  twitterName: string;
  twitterPicture: string;
  bannerUrl: string;
  address: string;
  ethereumAddress: null | string;
  prevAddress: string;
  addressConfirmed: boolean;
  twitterDescription: string;
  signedUp: boolean;
  subscriptionCurrency: string;
  subscriptionCurrencyAddress: null | string;
  subscriptionPrice: string;
  keyPrice: string;
  lastKeyPrice: string;
  subscriptionsEnabled: boolean;
  userConfirmed: boolean;
  twitterConfirmed: boolean;
  flag: number;
  ixHandle: string;
  handle: null | string;
  stats: Stats;
  badges: Badge[];
}

export interface UsersWithBadgesResponse {
  degods: User[];
  dokyo: User[];
  ogs: User[];
  sappySeals: User[];
  steady: User[];
  gogonauts: User[];
  bodoggos: User[];
  pudgy: User[];
  coq: User[];
  numberOfPages: number;
  numberOfResults: number;
  pageSize: number;
}
