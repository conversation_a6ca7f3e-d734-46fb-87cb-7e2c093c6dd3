import React, { forwardRef } from "react";
import { ViewStyle, StyleProp, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";

// Define the type for the component props
type GradientProps = {
  colors?: string[];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
  onPress?: () => void;
};

// Create the CustomLinearGradient component using React.forwardRef
const CustomLinearGradient = forwardRef<any, GradientProps>(
  (
    {
      colors,
      start = { x: 0, y: 0 },
      end = { x: 1, y: 1 },
      style,
      children,
      onPress,
    },
    ref
  ) => {
    return (
      <LinearGradient
        colors={['#F36A27', '#CE4909']}  // Color gradient
        start={{ x: 0.109, y: 0 }}  // Start point for the gradient (109 degrees)
        end={{ x: 0.7968, y: 1 }}   // End point for the gradient (79.68%)
        style={style}
        ref={ref}
      >
        <TouchableOpacity activeOpacity={0.7} onPress={onPress}>{children}</TouchableOpacity>
      </LinearGradient>
    );
  }
);

export default CustomLinearGradient;
