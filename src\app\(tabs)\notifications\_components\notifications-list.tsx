// "use client";

// import { Virtuoso } from "react-virtuoso";
// import { useQuery, useQueryClient } from "@tanstack/react-query";

// import { NotificationsCircleOutlineIcon } from "@/components/icons";
// import { useNotificationsInfiniteQuery } from "@/queries";
// import { NotificationItem } from "./notification-item";
// import { NotificationItemLoadingSkeleton } from "./notification-item-loading-skeleton";
// import { setAllNotificationsSeen } from "@/api/client/notifications";

// export const NotificationsList = () => {
//   const queryClient = useQueryClient();
//   const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage } =
//     useNotificationsInfiniteQuery();

//   useQuery({
//     queryKey: ["notifications", "set-all-seen"],
//     queryFn: async () => {
//       const response = await setAllNotificationsSeen();

//       await queryClient.invalidateQueries({
//         queryKey: ["notifications", "unseen"],
//       });

//       return response;
//     },
//     enabled: !isLoading,
//   });

//   const notifications = data?.pages.flatMap((page) => page.notifications) ?? [];

//   return (
//     <>
//       {notifications.length === 0 && !isLoading && (
//         <div className="-mt-4 flex h-full flex-col items-center justify-center gap-3">
//           <NotificationsCircleOutlineIcon className="h-16 w-16 fill-[#343434] text-[#343434]" />
//           <p className="max-w-[230px] text-center text-base leading-[22px] text-[#5A5A5A]">
//             You have no new notifications at this time
//           </p>
//         </div>
//       )}
//       {isLoading &&
//         Array.from({ length: 10 }).map((_, index) => (
//           <NotificationItemLoadingSkeleton key={index} />
//         ))}
//       {notifications.length > 0 && !isLoading && (
//         <Virtuoso
//           useWindowScroll
//           data={notifications}
//           overscan={600}
//           itemContent={(index, notification) => {
//             return <NotificationItem {...notification} />;
//           }}
//           endReached={() => {
//             if (hasNextPage) {
//               fetchNextPage();
//             }
//           }}
//           components={{
//             Footer: () => {
//               if (isFetchingNextPage) {
//                 return (
//                   <>
//                     {Array.from({ length: 10 }).map((_, i) => (
//                       <NotificationItemLoadingSkeleton key={i} />
//                     ))}
//                   </>
//                 );
//               }
//               return null;
//             },
//           }}
//         />
//       )}
//     </>
//   );
// };
