import { Colors } from '@/constants/Colors';
import React, { FC } from 'react';
import { StyleProp, TouchableOpacity, ViewStyle, StyleSheet } from 'react-native';
import { CheckFilledIcon } from '../icons';
import { CheckOutlineIcon } from '../icons/check-outline';

interface ICheckBoxProps {
    value?: boolean;
    onValueChange: (newValue: boolean) => void;
    boxType?: 'circle' | 'square';
    color?: string;
    isDisabled?: boolean;
    style?: StyleProp<ViewStyle>;
}

export const CheckBox: FC<ICheckBoxProps> = ({
    value,
    onValueChange,
    boxType = 'square',
    style,
    isDisabled = false,
    ...props
}) => {

    const handlePress = () => {
        onValueChange(!value);
    };

    return (
        <TouchableOpacity
            onPress={handlePress}
            accessibilityRole="checkbox"
            accessibilityState={value ? { checked: true } : {}}
            style={[
                styles.checkBox,
                { borderColor: value ? Colors.dark.white : Colors.dark.grey },
                boxType === 'circle' && [styles.circle, value && styles.circleChecked],
                boxType === 'square' && [styles.square, value && { backgroundColor: Colors.dark.brandOrange }],
                style,
            ]}
            disabled={isDisabled}
            {...props}>
            {boxType === 'square' && value && (
                <CheckOutlineIcon
                    height={20}
                    width={20}
                    color={Colors.dark.white}
                />
            )}
        </TouchableOpacity>
    );
};

export const styles = StyleSheet.create({
    checkBox: {
        width: 24,
        height: 24,
    },
    square: {
        borderRadius: 6,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    circle: {
        borderRadius: 12,
        borderWidth: 1,
    },
    circleChecked: {
        borderWidth: 8,
        borderColor: Colors.dark.brandOrange
    },
});

