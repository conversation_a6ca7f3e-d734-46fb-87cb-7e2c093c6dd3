import { axios } from "@/lib/axios";
import { ReferralStatsResponse } from "@/queries/types/user";
import { Me, User } from "@/types";

export const getMe = async () => {
  const response = await axios.get<{
    user: Me;
  }>("/user/me");
  return response.data;
};

export const getUserByHandle = async (params: { handle: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<{ user: User }>(
    `/user/handle?${searchParams.toString()}`,
  );
  return response.data;
};

export const getUserById = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<{ user: User }>(
    `/user/id?${searchParams.toString()}`,
  );
  return response.data;
};

interface UserSearchData {
  searchString: string;
}

export const getUsersSearch = async ({ searchString }: UserSearchData) => {
  const searchParams = new URLSearchParams({
    searchString,
  });

  const response = await axios.get(`/user/search?${searchParams.toString()}`);
  return response.data;
};

export const getUsersWithBadges = async () => {
  const response = await axios.get("/user/badges");
  return response.data;
};

export const getTopUsers = async () => {
  const response = await axios.get("/user/top");
  return response.data;
};

export const getNewUsers = async () => {
  const response = await axios.get("/user/page");
  return response.data;
};

export const getIsUserBlocked = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<boolean>(
    `/user/isUserBlocked?${searchParams.toString()}`,
  );
  return response.data;
};

export const getIsCurrentUserBlocked = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<boolean>(
    `/user/isTaggedUserblocked?${searchParams.toString()}`,
  );
  return response.data;
};

export const blockUser = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get(`/user/block?${searchParams.toString()}`);
  return response.data;
};

export const unblockUser = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get(`/user/unblock?${searchParams.toString()}`);
  return response.data;
};

export const reportUser = async (data: {
  userId: string;
  reason: string;
  details: string;
}) => {
  const response = await axios.post(`/user/report`, data);
  return response.data;
};

interface Referrer {
  id: string;
  referrerId: string;
  isYourReferrerAnswer: boolean | null;
  twitterHandle: string;
}

export const getReferrers = async () => {
  const response = await axios.get<{ referrers: Referrer[] }>(
    "/user/referrers",
  );
  return response.data;
};

export const getReferralStats = async () => {
  const response = await axios.get<ReferralStatsResponse>(
    "/user/stats/referral",
  );
  return response.data;
};

export const postConfirmReferrer = async (data: {
  referralId: string;
  referrerId: string;
  answer: boolean;
}) => {
  const response = await axios.post(`/user/confirmReferrer`, data);
  return response.data;
};

export const getIsBlockedByUser = async (params: { userId: string }) => {
  const searchParams = new URLSearchParams(params);

  const response = await axios.get<boolean>(
    `/user/isBlockedByUser?${searchParams.toString()}`,
  );
  return response.data;
};