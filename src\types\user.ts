export interface User {
  threadCount: number;
  followerCount: number;
  followingsCount: number;
  twitterFollowers: number;
  id: string;
  createdOn: string;
  twitterId: string;
  twitterHandle: string;
  twitterName: string;
  twitterPicture: string;
  bannerUrl: string;
  address: string;
  ethereumAddress: string | null;
  prevAddress: string | null;
  addressConfirmed: boolean;
  twitterDescription: string;
  signedUp: boolean;
  subscriptionCurrency: string;
  subscriptionCurrencyAddress: string | null;
  subscriptionPrice: string;
  keyPrice: string;
  lastKeyPrice: string;
  subscriptionsEnabled: boolean;
  userConfirmed: boolean;
  twitterConfirmed: boolean;
  flag: number;
  ixHandle: string;
  handle: string | null;
  following:
    | {
        id: number;
        createdOn: string;
        modifiedOn: string;
        followerId: string;
        followingId: string;
        followingActionTs: string | null;
        confirmed: string | null;
      }
    | boolean
    | null;
}

export enum UserFlaggedEnum {
  NONE = 0,
  BOT = 1,
  SPAM = 2,
  SUSPENDED = 3,
}
