import { useLocalSearchParams } from "expo-router";
import {
  Text,
  View,
  FlatList,
} from "react-native";

import React from "react";
import { usePostReactionsLikesInfiniteQuery } from "@/queries/postreactions-queries";
import { UserListItem } from "./_components/user-list-item";
import { UserListItemLoadingSkeleton } from "./_components/user-item-loading-skeleton";

export default function LikesScreen() {
  const params = useLocalSearchParams() as { userHandle: string; id: string };

  const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage } =
    usePostReactionsLikesInfiniteQuery({
      threadId: params.id,
    });

  const likes = data?.pages.flatMap((page) => page.likedUsers) || [];

  if (likes.length === 0 && !isLoading) {
    return (
      <View className="flex-row h-full w-full items-center justify-center px-6 pt-16 ">
        <Text className="text-base text-off-white">No likes yet</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={likes}
      renderItem={({ item }) => (
        <UserListItem user={item} threadId={params.id} />
      )}
      keyExtractor={(item, index) => item.id || index.toString()}
      onEndReached={() => {
        if (hasNextPage) {
          fetchNextPage();
        }
      }}
      onEndReachedThreshold={0.7}
      ListEmptyComponent={
        isLoading ? (
          <>
            {Array.from({ length: 7 }).map((_, i) => (
              <UserListItemLoadingSkeleton key={i} />
            ))}
          </>
        ) : null
      }
      ListFooterComponent={
        isFetchingNextPage ? (
          <>
            {Array.from({ length: 5 }).map((_, i) => (
              <UserListItemLoadingSkeleton key={`footer-${i}`} />
            ))}
          </>
        ) : null
      }
    />
  );
}
