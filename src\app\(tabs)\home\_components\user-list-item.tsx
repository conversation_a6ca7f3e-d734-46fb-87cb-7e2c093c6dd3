import { <PERSON> } from "expo-router";
import { StyleSheet, Text, View } from "react-native";

import { toast } from "@/components/toast";
import Avatar from "@/components/ui/avatar";
import { Colors } from "@/constants/Colors";
import { Button } from "@/components/ui/button";
import { useQueryClient } from "@tanstack/react-query";
import { TopUsersResponse, User } from "@/queries/types/top-users-response";
import { useFollowMutation, useUnfollowMutation } from "@/queries/follow-mutations";interface UserListItemProps {
  user: User;
  onFollow: () => void;
  onUnfollow: () => void;
}

export const UserListItem = ({
  user,
  onFollow,
  onUnfollow,
}: UserListItemProps) => {
  const queryClient = useQueryClient();
  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      onFollow();
      toast.green(`You're now following ${user.twitterName}!`);

      const previousTopUsers = queryClient.getQueriesData({
        queryKey: ["user", "top"],
      });

      queryClient.setQueryData(["user", "top"], (old: TopUsersResponse) => {
        if (!old) return old;

        return {
          ...old,
          users: old.users.map((u) => {
            if (u.id === user.id) {
              return { ...u, following: true };
            }

            return u;
          }),
        };
      });

      return { previousTopUsers };
    },
    onError(err, variables, context) {
      onUnfollow();
      toast.danger(`Failed to follow ${user.twitterName}.`);
      queryClient.setQueryData(["user", "top"], context?.previousTopUsers);
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      onUnfollow();

      const previousTopUsers = queryClient.getQueriesData({
        queryKey: ["user", "top"],
      });

      queryClient.setQueryData(["user", "top"], (old: TopUsersResponse) => {
        if (!old) return old;

        return {
          ...old,
          users: old.users.map((u) => {
            if (u.id === user.id) {
              return { ...u, following: false };
            }

            return u;
          }),
        };
      });

      return { previousTopUsers };
    },
    onError(err, variables, context) {
      onFollow();
      toast.danger(`Failed to unfollow ${user.twitterName}.`);
      queryClient.setQueryData(["user", "top"], context?.previousTopUsers);
    },
  });

  function handleFollow() {
    if (user.following) {
      unfollow({ userId: user.id });
    } else {
      follow({ userId: user.id });
    }
  }

  return (
    <View className="flex flex-row justify-between items-center px-6 py-4">
      <View className="flex flex-row items-center gap-4">
        <Link href={`./${user.twitterHandle}`}>
          <Avatar src={user.twitterPicture} size={42} />
        </Link>
        <View>
          <Link href={`./${user.twitterHandle}`}>
            <Text className="text-[#F4F4F4] font-semibold">
              {user.twitterName}
            </Text>
          </Link>
          <Link href={`./${user.twitterHandle}`}>
            <Text className="text-[#808080]">@{user.twitterHandle}</Text>
          </Link>
        </View>
      </View>
      <View>
        <Button
          onPress={handleFollow}
          variant={user.following ? "outline" : "secondary"}
        >
          <Text
            style={styles.followUnfollowText}
          >
            {user.following ? "Unfollow" : "Follow"}
          </Text>
        </Button>
      </View>
    </View>
  );
};

export const UserListItemLoadingSkeleton = () => {
  return <></>;
};

const styles = StyleSheet.create({
  followUnfollowText: {
    fontFamily: "InterSemiBold",
    color: Colors.dark.offWhite,
    fontSize: 12,
  },
});
