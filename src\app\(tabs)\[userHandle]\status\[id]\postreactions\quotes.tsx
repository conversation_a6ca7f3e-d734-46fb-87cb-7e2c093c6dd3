import { useLocalSearchParams } from "expo-router";
import {
  Text,
  View,
  FlatList,
} from "react-native";

import React, { useMemo } from "react";
import { usePostReactionsQuotesInfiniteQuery } from "@/queries/postreactions-queries";
import { PostReactionsQuote } from "./_components/postreactions-quote";
import { PostLoadingSkeleton } from "@/components/post-loading-skeletons";

export default function QuotesScreen() {
  const params = useLocalSearchParams() as { userHandle: string; id: string };

  const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage } =
    usePostReactionsQuotesInfiniteQuery({
      threadId: params.id,
    });

    const quotes = useMemo(() => {
        return (
            data?.pages.flatMap((page) =>
            page.quotes.map((quote) => ({
                ...quote,
                repost: page.parentQuote,
            })),
            ) || []
        );
    }, [data]);

  if (quotes.length === 0 && !isLoading) {
    return (
      <View className="flex-row h-full w-full items-center justify-center px-6 pt-16 ">
        <Text className="text-base text-off-white">No quotes yet</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={quotes}
      renderItem={({ item }) => (
        <PostReactionsQuote thread={item} />
      )}
      keyExtractor={(item, index) => item.id || index.toString()}
      onEndReached={() => {
        if (hasNextPage) {
          fetchNextPage();
        }
      }}
      onEndReachedThreshold={0.7}
      ListEmptyComponent={
        isLoading ? (
          <>
            {Array.from({ length: 7 }).map((_, i) => (
              <PostLoadingSkeleton key={i} />
            ))}
          </>
        ) : null
      }
      ListFooterComponent={
        isFetchingNextPage ? (
          <>
            {Array.from({ length: 5 }).map((_, i) => (
              <PostLoadingSkeleton key={`footer-${i}`} />
            ))}
          </>
        ) : null
      }
    />
  );
}
