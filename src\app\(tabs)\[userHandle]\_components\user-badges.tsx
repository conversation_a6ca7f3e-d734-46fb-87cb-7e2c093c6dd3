import { Badge } from "@/queries/types/top-users-response";
import { styled } from "nativewind";
import { Image, ImageSourcePropType, StyleSheet, View } from "react-native";

interface UserBadgesProps {
  badges: Badge[];
  className?: string;
}

type ImageMap = {
  [key: number]: ImageSourcePropType;
};

const StyledView = styled(View);
const StyledImage = styled(Image);

const images: ImageMap = {
  1: require("@assets/badges/badge-type-1.png"),
  2: require("@assets/badges/badge-type-2.png"),
  3: require("@assets/badges/badge-type-3.png"),
  4: require("@assets/badges/badge-type-4.png"),
  5: require("@assets/badges/badge-type-5.png"),
  6: require("@assets/badges/badge-type-6.png"),
  8: require("@assets/badges/badge-type-8.png"),
  9: require("@assets/badges/badge-type-9.png"),
  10: require("@assets/badges/badge-type-10.png"),
  11: require("@assets/badges/badge-type-11.png"),
  12: require("@assets/badges/badge-type-12.png"),
  13: require("@assets/badges/badge-type-13.png"),
  14: require("@assets/badges/badge-type-14.png"),
  15: require("@assets/badges/badge-type-15.png"),
};

export const UserBadges = ({ badges }: UserBadgesProps) => {
  return (
    <StyledView className="flex-row items-center gap-x-1.5">
      {badges.map((badge) => (
        <Image
          source={images[badge.badgeType]}
          key={badge.id}
          alt="Badge"
          style={styles.badge}
        />
      ))}
    </StyledView>
  );
};

const styles = StyleSheet.create({
  badge: {
    width: 14,
    aspectRatio: 1
  }
})