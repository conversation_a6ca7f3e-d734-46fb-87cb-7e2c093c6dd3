import { View, StyleSheet, TouchableOpacity, Text, Linking } from "react-native"
import { format } from "date-fns";
import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { screenWidth } from "@/constants/Device";
import { GAP } from "./account-suspended";
import { ReplyOutlineIcon } from "@/components/icons/reply-outline";
import { AddCircleOutlineIcon, CopyOutline2Icon, PinOutlineIcon, UnpinOutlineIcon } from "@/components/icons";
import * as Clipboard from 'expo-clipboard';
import { toast } from "@/components/toast";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { ChatMessagesResponse, GroupMessageTypeEnum, MessageType } from "@/queries/types/chats";
import ReplyMessage from "./reply-message";
import Avatar, { AvatarFallBack } from "@/components/ui/avatar";
import { router } from "expo-router";
import { checkContent, getEmojiFromUnicode, getUnicodeFromEmoji, isAnimatedGif, stringToColor } from "@/utils";
import { EmojiType } from "rn-emoji-keyboard";
import { EmojiSelector } from "@/components/ui/emoji-picker";
import { useReactToMessage, useUnreactToMessage } from "@/queries/chat-mutations";
import { InfiniteData, useQueryClient } from "@tanstack/react-query";
import { v4 } from "uuid";
import { Menu, MenuItem } from "@/components/ui/popup-menu";
import Animated, { interpolateColor, runOnUI, useAnimatedStyle, useSharedValue, withTiming, Easing, runOnJS, withSpring, useDerivedValue } from "react-native-reanimated";
import { VideoDeck } from "@/components/ui/video-deck";
import ReactionList from "./reaction-list";
import WebView from "react-native-webview";
import { Image } from 'expo-image';
import {
    GestureDetector,
    Gesture,
} from 'react-native-gesture-handler';

// [❤️, 🚀, 👍, 👎, 🤬, 😭, 😂]
const REACTIONS = [
    "2764-fe0f",
    "1f680",
    "1f44d",
    "1f44e",
    "1f92c",
    // "2694-fe0f", ⚔️,
    "1f62d",
    "1f602",
];

type TMessage = {
    messageItem: MessageType;
    isMyMessage: boolean;
    isPreviousSameUser: boolean;
    isNextSameUser: boolean;
    amIOwner: boolean;
    loggedinUserId: string;
    groupId: string;
    clickedMessageId: string;
    onReplySelect: (item: MessageType) => void;
    onPinSelect: (item: MessageType) => void;
    onImagePress: (item: MessageType) => void;
    setLoadingOverlay: (isLoadingOverlay: boolean) => void;
    setMessageId: (messageId: string) => void;
    simultaneousHandlers?: React.RefObject<any>;
}

export const BORDER_RADIUS = 20;
const NO_BORDER_RADIUS = 0;
const AVATAR_SIZE = 32;
const SWIPE_THRESHOLD = 60;

export const Message = memo((
    {
        messageItem,
        isMyMessage,
        isPreviousSameUser,
        isNextSameUser,
        amIOwner,
        loggedinUserId,
        groupId,
        clickedMessageId,
        onReplySelect,
        onPinSelect,
        onImagePress,
        setLoadingOverlay,
        setMessageId,
        simultaneousHandlers
    }: TMessage) => {

    const {
        id: messageId,
        message,
        createdOn,
        userName: teammateUserName,
        userId: teammateUserId,
        user,
        isPinned,
        reactions,
        reply,
        attachments,
    } = messageItem;

    const {
        twitterPicture: teammateProfilePictureUri,
        twitterHandle: teammateTwitterHandle
    } = user || {};

    const {
        messageType: attachmentType,
        url: attachmentUrl
    } = attachments?.[0] || {};

    const replyExist = !!reply;

    const {
        id: replyMessageId = "",
        userId: replyUserId = "",
        userName: replyUserName = "",
        message: replyMessage = "",
        attachments: replyAttachments = []
    } = reply || {};

    const {
        messageType: replyAttachmentType,
        url: replyAttachmentUrl
    } = replyAttachments?.[0] || {};
    /*
     DO NOT EXPOSE ANY CONTEXT TO THIS COMPONENT OR ELSE IT WILL BE RE-RENDERED EVERY TIME THE CONTEXT IS UPDATED
     ONLY PASS PROPS AND ADD THEM TO THE MEMO FUNCTION
     Keep this component as simple and lightweight as possible
    */

    const youtubeEmbedUrl = useMemo(() => {
        const [finalContent, isTruncated, youtubeEmbedUrl] = checkContent({
            content: message ?? "",
            truncate: false,
        });

        return youtubeEmbedUrl;
    }, [message]);

    const canCopyMessage = useMemo(() => message.trim().length > 0, [message])
    const isImage = useMemo(() => attachmentType === GroupMessageTypeEnum.Image, [attachmentType]);
    const isVideo = useMemo(() => attachmentType === GroupMessageTypeEnum.Video, [attachmentType]);
    const showProfilePicture = useMemo(() => !isMyMessage && !isNextSameUser, [isMyMessage, isNextSameUser])
    const [isEmojiKeyboardOpen, setIsEmojiKeyboardOpen] = useState(false);
    const queryClient = useQueryClient();
    const [isMenuOpen, setIsMenuOpen] = useState(false);

    const messageWithHighlightedLinks = useMemo(() => {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const parts = message.split(urlRegex);
        return parts.map((part, index) => {
            // If part matches a URL, style it as a link
            if (part.match(urlRegex)) {
                return (
                    <ThemedText
                        key={index}
                        style={[styles.commonMessageText, styles.link]}
                        onPress={() => Linking.openURL(part)}
                    >
                        {part}
                    </ThemedText>
                );
            }
            // For non-URL parts, render as normal text
            return <ThemedText key={index} style={styles.commonMessageText}>{part}</ThemedText>;
        });
    }, [message]);

    // Shared value for background opacity
    const backgroundOpacity = useSharedValue(0);

    useEffect(() => {
        const increaseBackgroundOpacity = () => {
            'worklet';
            backgroundOpacity.value = withTiming(0.15, { duration: 500, easing: Easing.ease });
        }

        const decreaseBackgroundOpacity = () => {
            'worklet';
            backgroundOpacity.value = withTiming(0, { duration: 500, easing: Easing.ease });
        }

        if (clickedMessageId === messageId) {
            //if pinned message is clicked, animate background color opacity to 1, hold for 2 seconds, then fade back to 0
            setTimeout(() => {
                setLoadingOverlay(false);
                runOnUI(increaseBackgroundOpacity)();
            }, 1500);
            setTimeout(() => {
                runOnUI(decreaseBackgroundOpacity)();
            }, 2500);
        }
    }, [clickedMessageId]);

    const translateX = useSharedValue(0);

    // Animated style to apply to the chat message
    const animatedMessageStyle = useAnimatedStyle(() => {
        return {
            backgroundColor: interpolateColor(
                backgroundOpacity.value,
                [0, 0.15],
                ['transparent', `rgba(235, 84, 10, 0.15)`],
            ),
        };
    });

    const animatedMessageStyleForSlideToReply = useAnimatedStyle(() => {
        return {
            transform: [{ translateX: translateX.value }],
        }
    })

    const replyIconOpacity = useDerivedValue(() => {
        return translateX.value / SWIPE_THRESHOLD > 1 ? 1 : translateX.value / SWIPE_THRESHOLD;
    });

    const animatedReplyIconStyle = useAnimatedStyle(() => ({
        opacity: replyIconOpacity.value,
    }));

    const { mutate: reactToMessage } = useReactToMessage({
        onMutate: async ({ messageId, groupId, reaction }) => {
            await queryClient.cancelQueries({
                queryKey: [
                    "chat",
                    "group-infinite-messages",
                    { groupId, messageId: clickedMessageId },
                ],
            });

            const previousMessages = queryClient.getQueryData([
                "chat",
                "group-infinite-messages",
                { groupId, messageId: clickedMessageId },
            ]);

            queryClient.setQueryData(
                [
                    "chat",
                    "group-infinite-messages",
                    { groupId, messageId: clickedMessageId },
                ],
                (old: InfiniteData<ChatMessagesResponse>) => {
                    if (!old) return old;
                    return {
                        ...old,
                        pages: old.pages.map((page, index) => {
                            if (page.messages.find((message) => message.id === messageId)) {
                                return {
                                    ...page,
                                    messages: page.messages.map((message) => {
                                        if (message.id === messageId) {
                                            //remove loggedinUserId's prev reactions for message if any
                                            const filteredReactions = message.reactions?.filter(
                                                (reaction) => reaction.userId !== loggedinUserId,
                                            );
                                            return {
                                                ...message,
                                                reactions: [
                                                    ...(filteredReactions || []),
                                                    //add new message reaction
                                                    {
                                                        id: v4(),
                                                        createdOn: Date.now().toString(),
                                                        messageId,
                                                        userId: loggedinUserId ?? "",
                                                        reaction,
                                                    },
                                                ],
                                            };
                                        }
                                        return message;
                                    }),
                                };
                            }

                            return page;
                        }),
                    };
                },
            );
            return { previousMessages };
        },
        onError(err, variables, context) {
            queryClient.setQueryData(
                [
                    "chat",
                    "group-infinite-messages",
                    { groupId: variables.groupId, messageId: clickedMessageId },
                ],
                context?.previousMessages,
            );
        },
    });

    const { mutateAsync: unreactToMessage } = useUnreactToMessage({
        onMutate: async ({ messageId, groupId }) => {
            await queryClient.cancelQueries({
                queryKey: [
                    "chat",
                    "group-infinite-messages",
                    { groupId, messageId: clickedMessageId },
                ],
            });

            const previousMessages = queryClient.getQueryData([
                "chat",
                "group-infinite-messages",
                { groupId, messageId: clickedMessageId },
            ]);

            queryClient.setQueryData(
                [
                    "chat",
                    "group-infinite-messages",
                    { groupId, messageId: clickedMessageId },
                ],
                (old: InfiniteData<ChatMessagesResponse>) => {
                    if (!old) return old;

                    return {
                        ...old,
                        pages: old.pages.map((page, index) => {
                            if (page.messages.find((message) => message.id === messageId)) {
                                return {
                                    ...page,
                                    messages: page.messages.map((message) => {
                                        if (message.id === messageId) {
                                            const filteredReactions = message.reactions?.filter(
                                                (reaction) => reaction.userId !== loggedinUserId,
                                            );
                                            return {
                                                ...message,
                                                reactions: filteredReactions,
                                            };
                                        }
                                        return message;
                                    }),
                                };
                            }

                            return page;
                        }),
                    };
                },
            );

            return { previousMessages };
        },
        onError(err, variables, context) {
            queryClient.setQueryData(
                [
                    "chat",
                    "group-infinite-messages",
                    { groupId: variables.groupId, messageId: clickedMessageId },
                ],
                context?.previousMessages,
            );
        },
    });

    const copyToClipboard = async () => {
        await Clipboard.setStringAsync(message);
        return toast.green("Copied to clipboard")
    };

    const openProfile = () => {
        router.push(`/${teammateTwitterHandle}`)
    }

    const onCircleIconPress = () => {
        setIsMenuOpen(false);
        setIsEmojiKeyboardOpen(prev => !prev)
    }

    const handleEmojiSelected = (emoji: EmojiType) => {
        if (messageId && groupId) {
            reactToMessage({
                messageId: messageId,
                groupId: groupId,
                reaction: getUnicodeFromEmoji(emoji.emoji),
            });
        }
    }

    const unreact = useCallback(() => {
        unreactToMessage({
            messageId,
            groupId,
        });
    }, [messageId, groupId]);

    const handleReplyPress = (replyMessageId: string) => {
        setLoadingOverlay(true);
        setMessageId(replyMessageId);
    }

    const panGesture = Gesture.Pan()
        .activeOffsetX([15])
        .failOffsetY([-0.1, 0.1])
        .onUpdate((event) => {
            'worklet';
            if (event.translationX > 0) {
                translateX.value = event.translationX;
            }
        })
        .onEnd(() => {
            'worklet';
            if (translateX.value > SWIPE_THRESHOLD) {
                runOnJS(onReplySelect)(messageItem);
            }
            translateX.value = withSpring(0, { damping: 20, stiffness: 200 });
        })
        .simultaneousWithExternalGesture(simultaneousHandlers as any);

    return (
        <View className="justify-center">
            <Animated.View className="absolute left-[16px] z-[-1]" style={animatedReplyIconStyle}>
                <ReplyOutlineIcon color={Colors.dark.white} height={22} width={22} />
            </Animated.View>
            <GestureDetector gesture={panGesture}>
                <Animated.View
                    style={[
                        styles.messageContainerWrapper,
                        isMyMessage ? {} : styles.teamMateMessageContainerWrapper,
                        { marginBottom: reactions?.length > 0 ? 22 : 10 },
                        animatedMessageStyle,
                        animatedMessageStyleForSlideToReply,
                    ]}>
                    {
                        isMyMessage ? null : showProfilePicture ? teammateProfilePictureUri ? (
                            <TouchableOpacity style={styles.avatarContainer} onPress={openProfile}>
                                {teammateProfilePictureUri ? (
                                    <Avatar size={AVATAR_SIZE} src={teammateProfilePictureUri} />
                                ) : <AvatarFallBack avatarSize={AVATAR_SIZE} />}
                            </TouchableOpacity>
                        ) :
                            <TouchableOpacity onPress={openProfile}>
                                <Avatar src={require("@assets/images/sample-profile-pic.png")} size={AVATAR_SIZE} />
                            </TouchableOpacity> :
                            <View style={[{ width: AVATAR_SIZE }, styles.avatarContainer]} />
                    }
                    <View style={[styles.messageContainer, {
                        alignSelf: isMyMessage ? "flex-end" : "flex-start",
                        backgroundColor: isMyMessage ? Colors.dark.brandOrange : Colors.dark.secondaryBackgroundDark,
                        borderBottomLeftRadius: isMyMessage ? BORDER_RADIUS : isNextSameUser ? BORDER_RADIUS : NO_BORDER_RADIUS,
                        borderBottomRightRadius: isMyMessage ? isNextSameUser ? BORDER_RADIUS : NO_BORDER_RADIUS : BORDER_RADIUS
                    }]}>
                        <Menu
                            alignModal={isMyMessage ? "left" : "right"}
                            isMenuOpen={isMenuOpen}
                            setIsMenuOpen={setIsMenuOpen}
                            trigger={
                                <View style={{ maxWidth: screenWidth * 0.75 }}>
                                    {
                                        isMyMessage ? null : !isPreviousSameUser ? (
                                            <TouchableOpacity style={styles.chatDetails} onPress={openProfile}>
                                                <ThemedText
                                                    style={[styles.username,
                                                    { color: stringToColor(teammateUserId) }
                                                    ]}>
                                                    {teammateUserName}
                                                </ThemedText>
                                            </TouchableOpacity>
                                        ) : null
                                    }
                                    {
                                        replyExist ? (
                                            <TouchableOpacity style={styles.replyContainer} onPress={() => handleReplyPress(replyMessageId)}>
                                                <ReplyMessage
                                                    messageToReplyToUserId={replyUserId}
                                                    messageToReplyToUserName={replyUserName}
                                                    messageToReplyToMessage={replyMessage}
                                                    userId={loggedinUserId}
                                                    type={"chat-room"}
                                                    isMyMessage={isMyMessage}
                                                    attachmentType={replyAttachmentType}
                                                    attachmentUrl={replyAttachmentUrl}
                                                />
                                            </TouchableOpacity>
                                        ) : null
                                    }
                                    {
                                        isImage ? (
                                            <TouchableOpacity style={styles.imageContainer} onPress={() => onImagePress(messageItem)}>
                                                <Image
                                                    source={{ uri: attachmentUrl, isAnimated: isAnimatedGif(attachmentUrl) }}
                                                    style={styles.image} />
                                            </TouchableOpacity>
                                        ) : isVideo ? (
                                            <View style={{ width: screenWidth * 0.6, height: 150, marginTop: 8 }}>
                                                <VideoDeck attachmentUrl={attachmentUrl} borderRadius={BORDER_RADIUS} />
                                            </View>
                                        ) : null
                                    }
                                    {youtubeEmbedUrl && (
                                        <View
                                            style={styles.imageContainer}
                                        >
                                            <WebView
                                                source={{ uri: youtubeEmbedUrl }}
                                                javaScriptEnabled={true}
                                                domStorageEnabled={true}
                                                allowsFullscreenVideo={true}
                                            />
                                        </View>
                                    )}
                                    <View style={styles.chatDetails}>
                                        <View style={styles.messageView}>
                                            <ThemedText
                                                style={[styles.myMessageText, styles.commonMessageText]}
                                            >
                                                {messageWithHighlightedLinks}
                                            </ThemedText>
                                        </View>
                                        <View style={styles.textView}>
                                            <ThemedText style={styles.messageTime}>{format(new Date(+createdOn), "K:mm a")}</ThemedText>
                                        </View>
                                        <View style={styles.textView}>
                                            {
                                                isPinned ? (
                                                    <PinOutlineIcon style={{ marginLeft: 5 }} color={Colors.dark.white} height={12} width={12} />
                                                ) : null
                                            }
                                        </View>
                                    </View>
                                </View>
                            }
                            popup1={
                                <View style={styles.reactionsContainer}>
                                    {REACTIONS.map((eachReaction, index) => (
                                        <MenuItem
                                            onSelect={() => {
                                                setIsMenuOpen(false);
                                                reactToMessage({
                                                    messageId: messageId,
                                                    groupId: groupId,
                                                    reaction: eachReaction,
                                                });
                                            }}
                                            key={index}
                                            style={styles.eachMenu}
                                        >
                                            <Text style={{ fontSize: 20 }}>{getEmojiFromUnicode(eachReaction)}</Text>
                                        </MenuItem>
                                    ))}
                                    <MenuItem
                                        style={[styles.addCircleIcon]}
                                        onSelect={onCircleIconPress}
                                    >
                                        <AddCircleOutlineIcon
                                            width={29}
                                            height={29}
                                            color={Colors.dark.offWhite}
                                        />
                                    </MenuItem>
                                </View>
                            }
                            popup1Styles={styles.popup1Styles}
                            popup2={
                                <View>
                                    <MenuItem style={styles.eachMenu} onSelect={() => {
                                        setIsMenuOpen(false);
                                        onReplySelect(messageItem);
                                    }}>
                                        <View style={styles.row}>
                                            <ReplyOutlineIcon color={Colors.dark.white} height={22} width={22} />
                                            <ThemedText style={styles.semiBoldText}>
                                                Reply
                                            </ThemedText>
                                        </View>
                                    </MenuItem>
                                    {
                                        canCopyMessage ? (
                                            <MenuItem style={styles.eachMenu} onSelect={() => {
                                                setIsMenuOpen(false);
                                                copyToClipboard();
                                            }}>
                                                <View style={styles.row}>
                                                    <CopyOutline2Icon color={Colors.dark.white} height={27} width={27} />
                                                    <ThemedText style={styles.semiBoldText}>
                                                        Copy
                                                    </ThemedText>
                                                </View>
                                            </MenuItem>
                                        ) : null
                                    }
                                    {
                                        amIOwner ? (
                                            <MenuItem style={styles.eachMenu} onSelect={() => {
                                                setIsMenuOpen(false);
                                                onPinSelect(messageItem);
                                            }}>
                                                <View style={styles.row}>
                                                    {
                                                        isPinned ? (
                                                            <UnpinOutlineIcon color={Colors.dark.white} height={27} width={27} />
                                                        ) : (
                                                            <PinOutlineIcon color={Colors.dark.white} height={27} width={27} />
                                                        )
                                                    }
                                                    <ThemedText style={styles.semiBoldText}>
                                                        {isPinned ? "Unpin" : "Pin"}
                                                    </ThemedText>
                                                </View>
                                            </MenuItem>
                                        ) : null
                                    }
                                </View>
                            }
                            popup2Styles={styles.popup2Styles}
                        />
                        {
                            reactions?.length > 0 && loggedinUserId ? (
                                <ReactionList
                                    reactions={reactions}
                                    loggedinUserId={loggedinUserId}
                                    isMyMessage={isMyMessage}
                                    unreactToMessage={unreact}
                                />
                            ) : null
                        }
                    </View>
                    <EmojiSelector
                        isEmojiKeyboardOpen={isEmojiKeyboardOpen}
                        setIsEmojiKeyboardOpen={setIsEmojiKeyboardOpen}
                        onEmojiSelected={handleEmojiSelected}
                    />
                </Animated.View>
            </GestureDetector>
        </View>
    )
}, (prevProps, nextProps) => {
    return (
        JSON.stringify(prevProps.messageItem) === JSON.stringify(nextProps.messageItem) &&
        prevProps.isMyMessage === nextProps.isMyMessage &&
        prevProps.isPreviousSameUser === nextProps.isPreviousSameUser &&
        prevProps.isNextSameUser === nextProps.isNextSameUser &&
        prevProps.amIOwner === nextProps.amIOwner &&
        prevProps.loggedinUserId === nextProps.loggedinUserId &&
        prevProps.groupId === nextProps.groupId &&
        prevProps.clickedMessageId === nextProps.clickedMessageId &&
        prevProps.onReplySelect === nextProps.onReplySelect &&
        prevProps.onPinSelect === nextProps.onPinSelect &&
        prevProps.onImagePress === nextProps.onImagePress &&
        prevProps.setLoadingOverlay === nextProps.setLoadingOverlay &&
        prevProps.setMessageId === nextProps.setMessageId
    )
})

const styles = StyleSheet.create({
    messageContainerWrapper: {
        width: '100%',
        pointerEvents: 'box-none',
    },
    teamMateMessageContainerWrapper: {
        flexDirection: "row",
        alignItems: 'flex-end',
    },
    messageContainer: {
        maxWidth: screenWidth * 0.75,
        borderRadius: BORDER_RADIUS,
    },
    chatDetails: {
        flexDirection: "row",
    },
    messageView: {
        maxWidth: "80%",
        marginRight: 6
    },
    textView: {
        justifyContent: "flex-end",
    },
    commonMessageText: {
        fontSize: 14,
        color: Colors.dark.white,
    },
    myMessageText: {
        alignSelf: "flex-start",
    },
    link: {
        textDecorationLine: 'underline',
    },
    messageTime: {
        fontSize: 10,
        color: Colors.dark.offWhite,
        opacity: 0.5,
        alignSelf: "flex-end",
    },
    username: {
        fontSize: 12,
        fontFamily: "InterSemiBold",
        fontWeight: "600",
        marginVertical: 3
    },
    usericon: {
        fontSize: 11,
    },
    semiBoldText: {
        fontSize: 18,
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: GAP * 1.5,
    },
    eachMenu: {
        paddingVertical: 8
    },
    replyContainer: {
        marginVertical: 5
    },
    avatarContainer: {
        marginRight: 5
    },
    reactionsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10
    },
    addCircleIcon: {
        marginRight: 6
    },
    popup1Styles: {
        borderRadius: 40,
        backgroundColor: Colors.dark.secondaryBackground,
        paddingVertical: 8,
        paddingHorizontal: 16
    },
    popup2Styles: {
        borderRadius: 20,
        backgroundColor: Colors.dark.secondaryBackground,
        paddingVertical: 8,
        paddingHorizontal: 16
    },
    imageContainer: {
        overflow: 'hidden',
        width: screenWidth * 0.7,
        height: 150,
        marginTop: 5,
        borderRadius: BORDER_RADIUS
    },
    image: {
        width: '100%',
        height: '100%',
        borderRadius: BORDER_RADIUS,
        alignSelf: 'center'
    }
});
