import React from "react";
import { Tabs } from "expo-router";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { View, StyleSheet, Text } from "react-native";
import {
  ChatbubbleOutlineIcon,
  HomeOutlineIcon,
  NotificationsOutlineIcon,
  SearchOutlineIcon,
  WalletOutlineIcon,
} from "@/components/icons-v2";
import { CogOutlineIcon, MicOutlineIcon } from "@/components/icons";
import { ActiveTabIcon } from "../_components/active-tab-icon";
import { useUnseenNotificationsQuery } from "@/queries/notifications-queries";

export default function TabLayout() {
  const { data, isLoading } = useUnseenNotificationsQuery();

  const colorScheme = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarLabel: () => null,
        tabBarStyle: styles.tabBarStyle,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ActiveTabIcon
              icon={
                <HomeOutlineIcon
                  style={styles.tabBarIconStyle}
                  color={focused ? Colors.dark.offWhite : Colors.dark.grey}
                />
              }
              focused={focused}
            />
          ),
          headerTitleAlign: "left",
          headerTitle: () => {
            return <View style={styles.mainHeader}></View>;
          },
          headerStyle: styles.headerStyle,
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ActiveTabIcon
              icon={
                <SearchOutlineIcon
                  style={styles.tabBarIconStyle}
                  color={focused ? Colors.dark.offWhite : Colors.dark.grey}
                />
              }
              focused={focused}
            />
          ),
          headerShown: true,
          headerStyle: {
            backgroundColor: "transparent",
          },
          title: "Explore",
          headerTitleStyle: {
            fontWeight: "600",
            fontSize: 26,
            color: Colors.dark.white,
          },
          headerTitleAlign: "left",
        }}
      />
      <Tabs.Screen
        name="notifications"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <>
              {!isLoading && data && data?.count > 0 && (
                <View style={styles.notificationBadge}>
                  <Text className="text-xs text-off-white">{data.count}</Text>
                </View>
              )}
              <ActiveTabIcon
                icon={
                  <NotificationsOutlineIcon
                    style={styles.tabBarIconStyle}
                    color={focused ? Colors.dark.offWhite : Colors.dark.grey}
                  />
                }
                focused={focused}
              />
            </>
          ),
          headerShown: true,
          headerStyle: {
            backgroundColor: "transparent",
          },
          title: "Notifications",
          headerRight: () => (
            <CogOutlineIcon
              height={24}
              width={24}
              color={Colors.dark.offWhite}
            />
          ),
          headerRightContainerStyle: {
            paddingRight: 10,
          },
          headerTitleStyle: {
            fontWeight: "600",
            fontSize: 26,
            color: Colors.dark.white,
          },
          headerTitleAlign: "left",
        }}
      />
      <Tabs.Screen
        name="tippingParty"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ActiveTabIcon
              icon={
                <MicOutlineIcon
                  style={styles.tabBarIconStyle}
                  color={focused ? Colors.dark.offWhite : Colors.dark.grey}
                />
              }
              focused={focused}
            />
          ),
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="messages"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ActiveTabIcon
              icon={
                <ChatbubbleOutlineIcon
                  style={styles.tabBarIconStyle}
                  color={focused ? Colors.dark.offWhite : Colors.dark.grey}
                />
              }
              focused={focused}
            />
          ),
          headerShown: false,
          tabBarHideOnKeyboard: true,
        }}
      />
      <Tabs.Screen
        name="wallet"
        options={{
          tabBarIcon: ({ color, focused }) => (
            <ActiveTabIcon
              icon={
                <WalletOutlineIcon
                  style={styles.tabBarIconStyle}
                  color={focused ? Colors.dark.offWhite : Colors.dark.grey}
                />
              }
              focused={focused}
            />
          ),
          headerShown: false,
        }}
      />
      <Tabs.Screen
        name="[userHandle]"
        options={{
          href: null,
          headerShown: false,
        }}
      />
    </Tabs>
  );
}

const styles = StyleSheet.create({
  mainHeader: {},
  headerStyle: {},
  tabBarStyle: {
    backgroundColor: "transparent",
    borderTopWidth: 1,
  },
  tabBarIconStyle: {
    height: 22,
    width: 22,
  },
  notificationBadge: {
    position: "absolute",
    right: 16,
    top: 4,
    minWidth: 20,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 4,
    backgroundColor: "#333",
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
});
