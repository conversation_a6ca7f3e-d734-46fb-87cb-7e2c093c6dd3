import { axios } from "@/lib/axios";

export interface SystemCurrency {
  symbol: string;
  systemRate: string;
  name: string;
  balance?: string;
  marketCap?: string;
  isToken: boolean;
  photoURL?: string;
  contractAddress?: string;
  tokenPhase?: number;
  isStakedArena?: boolean;
}

export const getExchangeCurrencies = async () => {
  const response = await axios.get<SystemCurrency[]>(`/currency/exchange`);
  return response.data;
};
