"use client";

import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { depositSolana } from "@/api/balance";
import { tipSolana } from "@/api/client/chain";
import { postTipThread } from "@/api/client/threads";
import { minDelay } from "@/utils/min-delay";

interface TipMutationData {
  currency: string;
  tipAmount: string;
  userId: string;
  threadId?: string;
  wallet: string;
}

type TipMutationType = MutationOptions<
  unknown,
  DefaultError,
  TipMutationData,
  any
>;

async function tip({
  currency,
  tipAmount,
  userId,
  threadId,
  wallet,
}: TipMutationData) {
  if (
    ["SOL", "Bonk", "$WIF", "USEDCAR", "Moutai", "HARAMBE"].includes(currency)
  ) {
    const provider =
      typeof window !== "undefined" && (window as any).phantom?.solana;

    if (wallet == "Phantom") {
      await depositSolana(
        provider,
        parseFloat(tipAmount),
        currency,
        "finalized",
      );
      const response = tipSolana({
        userIdTo: userId,
        amount: parseFloat(tipAmount),
        currency,
      });

      return response;
    } else if (wallet == "The Arena") {
      const response = tipSolana({
        userIdTo: userId,
        amount: parseFloat(tipAmount),
        currency,
      });

      return response;
    }
  } else {
    const response = postTipThread({
      threadId,
      tipAmount,
      userId,
      currency,
    });

    return response;
  }
}

export const useTipMutation = (options?: TipMutationType) => {
  return useMutation({
    mutationFn: async (data: TipMutationData) => {
      return await minDelay(tip(data));
    },
    ...options,
  });
};
