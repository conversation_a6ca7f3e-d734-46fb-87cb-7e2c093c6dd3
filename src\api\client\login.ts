import { setCookie } from '@/cookies/secure-store';
import { axios } from '@/lib/axios';

export const checkEmail = async (email: string) => {
  const response = await axios.get(`/userEmail/registered/${email}`);
  return response.data;
};

interface LoginEmailProps {
  token: string;
  ref: string | null;
}

export async function loginEmail({ token, ref }: LoginEmailProps) {
  try {
    const { data } = await axios.post(
      `/auth/dynamic-jwt-exchange-email-login`,
      { token, ref },
    );

    if (data.errorCode) {
      throw data;
    }

    // 30 days
    const expires = new Date(Date.now() + 30 * 1000 * 60 * 60 * 24);

    setCookie('token', data.token, expires);
    setCookie(
      'user',
      JSON.stringify({
        ...data.user,
        loggedInAt: new Date(),
      }),
      expires,
    );
    setCookie('twitterUser', JSON.stringify(data.twitterUser), expires);

    return { data };
  } catch (error: any) {
    return { error: error.message };
  }
}
