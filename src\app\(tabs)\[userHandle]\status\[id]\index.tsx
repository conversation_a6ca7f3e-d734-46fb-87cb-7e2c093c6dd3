import { MainPost } from "./_components/main-post";
import { Comments } from "../../_components/comments";
import React, { useState } from "react";
import { ReplyEditor } from "@/components/editor/reply-editor";
import { View, ScrollView } from "react-native";

export default function PostPage() {
  const [replyEditorHeight, setReplyEditorHeight] = useState(60);

  return (
    <>
      <ScrollView style={{ marginBottom: replyEditorHeight }}>
        <MainPost />
        <Comments />
      </ScrollView>
      <View
        className="absolute bottom-0 left-0 right-0 z-[100]"
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout;
          setReplyEditorHeight(height);
        }}
      >
        <ReplyEditor />
      </View>
    </>
  );
}
