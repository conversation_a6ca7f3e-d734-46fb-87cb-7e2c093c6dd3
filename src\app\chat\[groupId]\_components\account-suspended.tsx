import { Linking, StyleSheet, View } from "react-native";
import React from "react";
import { InfoOutlineIcon } from "@/components/icons";
import { ThemedText } from "@/components/ThemedText";
import { Nullable } from "@/types/common";
import { globalStyles } from "@/app/globalStyles";
import { useGroup } from "../_contexts/group-context";

const ICON_SIZE = 16;
export const GAP = 10

type TAccountSuspended = {
    headerHeight: Nullable<number>;
}

const AccountSuspended = ({ headerHeight }: TAccountSuspended) => {
    const { isOwnerSuspended } = useGroup();

    return headerHeight && isOwnerSuspended ? (
        <View style={[styles.blurContainer, { top: headerHeight }]}>
            <View style={styles.subContainer}>
                <InfoOutlineIcon height={ICON_SIZE} width={ICON_SIZE} />
                <ThemedText style={styles.title} type="bold">
                    Account Suspended
                </ThemedText>
            </View>
            <View style={{ marginLeft: ICON_SIZE + GAP }}>
                <ThemedText style={styles.title} type="defaultGrey">
                    This account has been suspended for violating
                    The Arena's{" "}
                    <ThemedText
                        onPress={() => {
                            Linking.openURL("https://arena.social/terms-of-use")
                        }}
                        style={[styles.title, { textDecorationLine: 'underline' }]}
                        type="bold">
                        terms of use.
                    </ThemedText>
                </ThemedText>
            </View>
        </View>
    ) : null;
};

export default AccountSuspended;

const styles = StyleSheet.create({
    subContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: GAP
    },
    title: {
        fontSize: 15,
    },
    blurContainer: {
        backgroundColor: "#********",
        ...globalStyles.windowWidth,
        zIndex: 1,
        paddingHorizontal: 20,
        paddingVertical: 10,
        gap: GAP,
        position: 'absolute',
    },
});
