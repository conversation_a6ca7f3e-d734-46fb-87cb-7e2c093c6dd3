import { useMemo, useRef, useState } from "react";

import { FlatList, RefreshControl, StyleSheet, Text, View } from "react-native";

import { Thread } from "@/types";
import { useHomeStore } from "@/stores";
import { Colors } from "@/constants/Colors";
import { ThemedView } from "@/components/ThemedView";
import { useQueryClient } from "@tanstack/react-query";
import { useTopUsersQuery } from "@/queries/user-queries";
import { useThreadsInfiniteQuery } from "@/queries/threads-query";

import { TimelinePost } from "../_components/timeline-post";
import { UserListItem } from "../_components/user-list-item";
import { PostLoadingSkeleton } from "@/components/post-loading-skeletons";

export default function FollowingTimeline() {
  const queryClient = useQueryClient();
  // const timelineState = useRef<StateSnapshot>();
  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useHomeStore((state) => state.followingTimelineRef);
  const [followsCount, setFollowsCount] = useState(0);

  const pullToRefresh = () =>
    queryClient.resetQueries({ queryKey: ["home", "threads", "my-feed"] });

  const { snapshot, setSnapshot } = useHomeStore((state) => ({
    snapshot: state.followingSnapshot,
    setSnapshot: state.setFollowingSnapshot,
  }));

  const { data, fetchNextPage, isLoading, isFetchingNextPage, hasNextPage } =
    useThreadsInfiniteQuery();

  const { data: topUsersData, isLoading: isTopUsersLoading } =
    useTopUsersQuery();

  const threads = useMemo(() => {
    if (!data) return [];

    return data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [data]);

  if (threads.length === 0 && !isLoading) {
    return (
      <View className="relative mt-9 pb-6 shadow-[0px_1px_1px_0px_#323232]">
        <View>
          <Text className="text-base font-semibold leading-5 text-white text-center">
            Welcome to the Arena!
          </Text>
          <Text className="mt-1 text-sm text-[#808080] text-center">
            Follow some of the popular accounts below to get started
          </Text>
        </View>
        <View className="mt-4 pb-10">
          {!isTopUsersLoading && topUsersData && (
            <FlatList
              data={topUsersData.users}
              keyExtractor={(item, index) =>
                item.id.toString() || index.toString()
              }
              renderItem={({ item }) => (
                <UserListItem
                  key={item.id}
                  user={item}
                  onFollow={() => {
                    setFollowsCount((count) => count + 1);
                  }}
                  onUnfollow={() => {
                    setFollowsCount((count) => count - 1);
                  }}
                />
              )}
            />
          )}
        </View>
      </View>
    );
  }

  const isRefreshing = isLoading || isTopUsersLoading;

  return (
    <ThemedView style={[styles.container]}>
      <FlatList
        data={threads}
        keyExtractor={(item, index) => item.id.toString() || index.toString()}
        renderItem={({ item }) => <TimelinePost thread={item} />}
        scrollEventThrottle={16}
        onEndReached={() => {
          if (hasNextPage) fetchNextPage();
        }}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={pullToRefresh} />
        }
        ListEmptyComponent={
          isLoading ? (
            <>
              {Array.from({ length: 7 }).map((_, i) => (
                <PostLoadingSkeleton key={i} />
              ))}
            </>
          ) : null
        }
        ListFooterComponent={
          isFetchingNextPage ? (
            <>
              {Array.from({ length: 5 }).map((_, i) => (
                <PostLoadingSkeleton key={`footer-${i}`} />
              ))}
            </>
          ) : null
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
    position: "relative",
  },
});
