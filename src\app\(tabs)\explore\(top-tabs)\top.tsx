import { FlatList, StyleSheet } from "react-native";

import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import {
  useRecentTradesQuery,
  useTrendingUsersQuery,
} from "@/queries/trade-queries";
import {
  useNewUsersQuery,
  useTopUsersQuery,
  useUsersWithBadgesQuery,
} from "@/queries/user-queries";
import { ProfileItemCard } from "../_components/profile-item-card";

export default function TopProfiles() {
  const { data: trendingUsersData, isLoading: isTrendingUsersLoading } =
    useTrendingUsersQuery();

  const { data, isLoading } = useTopUsersQuery();

  useTopUsersQuery({
    notifyOnChangeProps: [],
  });
  useNewUsersQuery({
    notifyOnChangeProps: [],
  });
  useRecentTradesQuery({
    notifyOnChangeProps: [],
  });
  useUsersWithBadgesQuery({
    notifyOnChangeProps: [],
  });

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.headline}>Trending right now</ThemedText>
      <ThemedView>
        <FlatList
          data={data?.users || []}
          renderItem={({ item }) => <ProfileItemCard user={item} />}
          keyExtractor={(item, index) => item.id || index.toString()}
        />
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 24,
  },
  headline: {
    color: "#808080",
    fontSize: 14,
    fontWeight: "400",
    marginTop: 20,
  },
});
