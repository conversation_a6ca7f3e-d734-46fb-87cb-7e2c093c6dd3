import { ThemedText } from "@/components/ThemedText";
import { But<PERSON> } from "@/components/ui/button";
import { Colors } from "@/constants/Colors";
import { router } from "expo-router";
import { memo } from "react";
import { LayoutChangeEvent, StyleSheet, View } from "react-native";


type TYouBlockedUserView = {
    onLayout: (event: LayoutChangeEvent) => void;
    chatMateHandle: string;
}

export const YouBlockedUserView = memo(({ onLayout, chatMateHandle }: TYouBlockedUserView) => {
    return (
        <View onLayout={onLayout} style={styles.container}>
            <ThemedText style={styles.text}>
                You blocked this user, to continue this conversation please unblock them through their profile page.
            </ThemedText>
            <View style={styles.buttonContainer}>
                <Button
                    onPress={() => {
                        router.push(`/${chatMateHandle}`);
                    }}
                    style={styles.button}
                >
                    <ThemedText type="bold">Go to Profile</ThemedText>
                </Button>
                <Button
                    onPress={() => {
                        router.back();
                    }}
                    variant="outline"
                    style={styles.button}
                >
                    <ThemedText type="bold">Back</ThemedText>
                </Button>
            </View>
        </View>
    )
}, (prevProps, nextProps) => {
    return prevProps.onLayout === nextProps.onLayout
})

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderTopWidth: 1,
        borderTopColor: Colors.dark.darkGrey,
        marginTop: 10
    },
    text: {
        lineHeight: 24
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 20,
    },
    button: {
        width: '48%',
    }
});