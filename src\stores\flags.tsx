"use client";

import { createContext, useContext, useState } from "react";

import { createStore, StoreApi, useStore } from "zustand";

interface Store {
  stages: boolean;
  canCreateStages: boolean;
}

interface Actions {
  actions: {
    setFeatureFlags: (flags: Store) => void;
  };
}

type FeatureFlagsState = Store & Actions;

const FeatureFlagsStoreContext =
  createContext<StoreApi<FeatureFlagsState> | null>(null);

export const FeatureFlagsStoreProvider = ({
  flags,
  children,
}: {
  children: React.ReactNode;
  flags: Record<string, string | boolean>;
}) => {
  const [store] = useState(() =>
    createStore<FeatureFlagsState>((set, get) => ({
      stages: true, //getBooleanFlag(flags["stages"]),
      canCreateStages: true, //getBooleanFlag(flags["can-create-stages"]),
      actions: {
        setFeatureFlags: (flags) => {
          set(flags);
        },
      },
    }))
  );

  return (
    <FeatureFlagsStoreContext.Provider value={store}>
      {children}
    </FeatureFlagsStoreContext.Provider>
  );
};

export function useFeatureFlagsStore(): FeatureFlagsState;
export function useFeatureFlagsStore<T>(
  selector: (state: FeatureFlagsState) => T
): T;
export function useFeatureFlagsStore<T>(
  selector?: (state: FeatureFlagsState) => T
) {
  const store = useContext(FeatureFlagsStoreContext);
  if (!store) {
    throw new Error("Missing FeatureFlagsStoreProvider");
  }
  return useStore(store, selector!);
}

const getBooleanFlag = (flag?: string | boolean) => {
  if (typeof flag === "boolean") {
    return flag;
  }

  return false;
};
