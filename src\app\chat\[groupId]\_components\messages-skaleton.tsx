import { Skeleton } from "@/components/ui/skaleton";
import { View, StyleSheet } from "react-native";
import { BORDER_RADIUS } from "./message";

export const MessagesSkaleton = () => {
    const list = [
        {
            id: "1",
            isMyMessage: false,
        },
        {
            id: "2",
            isMyMessage: false,
        },
        {
            id: "3",
            isMyMessage: true,
        },
        {
            id: "4",
            isMyMessage: false,
        },
        {
            id: "5",
            isMyMessage: true,
        },
        {
            id: "6",
            isMyMessage: false,
        },
        {
            id: "7",
            isMyMessage: true,
        },
        {
            id: "8",
            isMyMessage: false,
        },
        {
            id: "9",
            isMyMessage: false,
        },
        {
            id: "10",
            isMyMessage: false,
        },
        {
            id: "11",
            isMyMessage: true,
        },
        {
            id: "12",
            isMyMessage: false,
        },
        {
            id: "13",
            isMyMessage: false,
        },
        {
            id: "14",
            isMyMessage: true,
        },
        {
            id: "15",
            isMyMessage: false,
        },
    ]
    
    return (
        <View style={styles.skaletonContainer}>
            {list.map((item) => {
                const isImage = item.id === "8";
                const isLongMessage = item.id === "11";
                const isWideMessage = item.id === '7';
               return (
                <Skeleton
                    key={item.id}
                    height={isImage ? 100 : isLongMessage ? 70 : 30}
                    width={isImage ? "70%" : isWideMessage || isLongMessage ? '50%' : "30%"}
                    style={{ marginVertical: 5, borderRadius: BORDER_RADIUS, alignSelf: item.isMyMessage ? "flex-end" : "flex-start", }}
                />
            )})}
        </View>
    )
}

const styles = StyleSheet.create({
    skaletonContainer: {
        flex: 1,
        justifyContent: 'flex-end',
        alignItems: 'center',
        paddingHorizontal: 16,
    }
})