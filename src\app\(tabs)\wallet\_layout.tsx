import React from "react";
import Avatar from "@/components/ui/avatar";
import Header from "@/components/ui/header";
import { Stack } from "expo-router";
import { WalletHeader } from "./_components/wallet-header";
import { SafeAreaView } from "react-native-safe-area-context";

const WalletLayout = () => {
  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Stack>
        <Stack.Screen
          name="(top-tabs)"
          options={{ headerShown: true, header: () => <WalletHeader /> }}
        />
        <Stack.Screen
          name="claim-nft-badge"
          options={{
            header: () => <Header title="Claim Steady Badge" />,
          }}
        />
      </Stack>
    </SafeAreaView>
  );
};

export default WalletLayout;
