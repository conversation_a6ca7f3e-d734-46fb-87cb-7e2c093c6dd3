import { Path, Svg, SvgProps, Circle } from "react-native-svg";

export const TippingPartyOutlineIcon = (props: SvgProps) => (
  <Svg
    viewBox="0 0 22 22"
    fill="none"
    {...props}
  >
    <Circle cx="11" cy="11" r="10.25" stroke="currentColor" strokeWidth="1.5"/>
    <Path
      d="M11 4.40039V17.6004"
      stroke="currentColor"
      strokeWidth="1.25"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M14.3002 8.19939C14.3002 7.27147 13.3854 5.5 11.0331 5.5C8.6808 5.5 7.67504 7.03528 7.70067 8.19939C7.7263 9.3635 8.41943 11.1687 11.0331 11.1687C13.6468 11.1687 14.3002 12.839 14.3002 13.9693C14.3002 14.8298 13.5422 16.5 11.0331 16.5C8.52398 16.5 7.76601 14.8129 7.70067 13.9693"
      stroke="currentColor"
      strokeWidth="1.25"
      strokeLinecap="round"
    />
  </Svg>
);
