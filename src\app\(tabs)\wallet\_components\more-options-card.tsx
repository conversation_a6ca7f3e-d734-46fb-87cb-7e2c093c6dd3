import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import CustomLinearGradient from "@/components/ui/linear-gradient";
import { Colors } from "@/constants/Colors";
import {
  Pressable,
  StyleProp,
  StyleSheet,
  View,
  ViewStyle,
} from "react-native";

interface CardProps {
  leftComponent: React.ReactNode;
  rightComponent?: React.ReactNode;
  children: React.ReactNode;
  containerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<ViewStyle>;
  useLinearGradient?: React.ReactNode;
  gradientColors?: string[];
  onPress?: () => void;
  linearGradientOnPress?: () => void;
}

export const MoreOptionsCard = ({
  leftComponent,
  rightComponent,
  children,
  containerStyle,
  contentStyle,
  useLinearGradient,
  gradientColors = ["rgba(243, 106, 39, 1)", "rgba(206, 73, 9, 1)"],
  onPress,
  linearGradientOnPress,
}: CardProps) => {
  return (
    <>
      {useLinearGradient ? (
        <CustomLinearGradient
          colors={gradientColors}
          style={[styles.container, containerStyle]}
          onPress={linearGradientOnPress}
        >
          <View style={[styles.content, contentStyle]}>
            {leftComponent}
            {children}
          </View>
          {rightComponent ? rightComponent : null}
        </CustomLinearGradient>
      ) : (
        <Pressable onPress={onPress} style={[styles.container, containerStyle]}>
          <View style={[styles.content, contentStyle]}>
            {leftComponent}
            {children}
          </View>
          {rightComponent ? rightComponent : null}
        </Pressable>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: Colors.dark.borderColor,
    padding: 16,
    borderRadius: 10,
    alignItems: "center",
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
});
