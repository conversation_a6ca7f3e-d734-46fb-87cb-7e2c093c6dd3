import { LayoutChangeEvent, StyleSheet, View, Text, TouchableOpacity } from "react-native";
import React, { useEffect, useMemo, useState } from "react";
import BackButton from "@/components/navigation/BackButton";
import Avatar, { AvatarFallBack } from "@/components/ui/avatar";
import { SuspendedUserChatPreviewOutlineIcon } from "@/components/icons";
import { useUser } from "@/stores";
import { useGroup } from "../_contexts/group-context";
import { router } from "expo-router";
import { Skeleton } from "@/components/ui/skaleton";
import { Colors } from "@/constants/Colors";
import { useSocket } from "@/stores/socket";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { cn } from "@/utils/cn";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from "react-native-reanimated";

const AVATAR_SIZE = 42

type TChatHeader = {
  onLayout: (event: LayoutChangeEvent) => void;
}

interface Typing {
  groupId: string;
  user: {
    id: string;
    name: string;
  };
  date: number;
}

const ChatHeader = ({ onLayout }: TChatHeader) => {
  const { user } = useUser();
  const [typing, setTyping] = useState<Typing>();
  const { socket } = useSocket();
  const {
    data,
    isLoading,
    groupId,
    isSuccess,
    isOwnerSuspended,
    amIOwner,
    chatMate,
    groupName,
    userHandle
  } = useGroup();

  useEffect(() => {
    socket?.on(SOCKET_MESSAGE.CHAT_TYPING, (data: { typing: Typing }) => {
      if (
        data.typing &&
        data.typing.groupId === groupId &&
        data.typing.user.id !== user?.id
      ) {
        setTyping({ ...data.typing, date: Date.now() });
      }
    });

    return () => {
      socket?.off(SOCKET_MESSAGE.CHAT_TYPING);
    };
  }, [socket, groupId, user]);

  useEffect(() => {
    const interval = setInterval(() => {
      setTyping((prev) => {
        const now = Date.now();

        if (prev && now - prev.date > 500) {
          return undefined;
        }

        return prev;
      });
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  const translateY = useSharedValue(0);

  useEffect(() => {
    translateY.value = withTiming(typing ? -5 : 0, {
      duration: 200,
    });
  }, [typing]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const { group } = data || {};
  const { isDirect } = group || {};

  const avatarImageSrc = useMemo(() => {
    return isDirect
      ? amIOwner
        ? chatMate?.user?.twitterPicture
        : group?.profilePictureUrl
      : group?.profilePictureUrl;
  }, [amIOwner, isDirect, group?.profilePictureUrl, chatMate?.user?.twitterPicture]);

  const openProfile = () => {
    router.push(`/${userHandle}`);
  }

  if (
    group?.isDirect &&
    group.chatMateId !== user?.id &&
    group.ownerUserId !== user?.id
  ) {
    router.back();
    return null;
  } else {
    return (
      <View onLayout={onLayout} style={styles.blurContainer}>
        <View style={styles.backButtonContainer}>
          <BackButton />
        </View>
        <TouchableOpacity activeOpacity={0.7} style={styles.touchableContainer} onPress={openProfile}>
          {
            isLoading ? (
              <Skeleton height={AVATAR_SIZE} width={AVATAR_SIZE} style={{ borderRadius: AVATAR_SIZE / 2 }} />
            ) : isOwnerSuspended ? (
              <SuspendedUserChatPreviewOutlineIcon height={AVATAR_SIZE} width={AVATAR_SIZE} />
            ) : avatarImageSrc ? (
              <Avatar size={AVATAR_SIZE} src={avatarImageSrc} />
            ) : <AvatarFallBack avatarSize={AVATAR_SIZE} />
          }
          <View style={styles.groupTitleContainer}>
            {
              isLoading ? (
                <Skeleton height={20} width={200} />
              ) : isSuccess && groupName ? (
                <>
                  <Animated.Text
                    style={animatedStyle}
                    className="text-base font-semibold leading-5 text-white"
                  >
                    {isLoading ? (
                      <Skeleton height={4} width={24} />
                    ) : (
                      groupName
                    )}
                  </Animated.Text>
                  {typing && <Text className={cn(
                    "absolute -bottom-3 left-4 w-60 whitespace-nowrap text-xs leading-4 text-gray-text transition-opacity",
                    typing ? "opacity-100" : "opacity-0",
                  )} numberOfLines={1}>{typing?.user.name} is typing...</Text>}
                </>
              ) : null
            }
          </View>
        </TouchableOpacity>

      </View>
    );
  }
};

export default ChatHeader;

const styles = StyleSheet.create({
  blurContainer: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 8,
    width: '100%',
    alignItems: "center",
    gap: 10,
  },
  backButtonContainer: {
    width: '10%',
    height: '100%',
    alignItems: 'center',
    paddingVertical: 8
  },
  touchableContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    width: '90%'
  },
  groupTitleContainer: {
    paddingLeft: 15,
    maxWidth: '95%',
  },
  semiBoldText: {
    fontFamily: "InterSemiBold",
    fontSize: 15,
    color: Colors.dark.text,
  },
});
