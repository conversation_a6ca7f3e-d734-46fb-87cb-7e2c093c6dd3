import { View, Pressable } from "react-native";
import { ThemedText } from "@/components/ThemedText";

export interface ReportType {
  reason: string;
  description: string;
}

export const reportTypes: ReportType[] = [
  {
    reason: "Bots & Spam",
    description:
      "The user is a bot, it's spamming or presents inorganic activity.",
  },
  {
    reason: "Scammers",
    description:
      "The user is impersonating someone else, stealing content, lying or sharing malicious links.",
  },
  {
    reason: "Abhorrent Content",
    description:
      "The user is posting content meant to disturb, engaging in animal abuse or child exploitation.",
  },
  {
    reason: "Hate & Harassment",
    description:
      "The user is threatening another user, inciting violence against a person or group, harassing someone or sharing sensitive personal information.",
  },
  {
    reason: "Other",
    description: "Please provide additional details for your report.",
  },
];

export const ReasonPage = ({
  setReason,
  isContentReport,
  selectedReason,
}: {
  setReason: (reason: string) => void;
  isContentReport: boolean;
  selectedReason: string | null;
}) => (
  <View className="px-6">
    <ThemedText type="bold" className="text-xl text-off-white">
      {isContentReport
        ? "Why are you reporting this content?"
        : "Why are you reporting this user?"}
    </ThemedText>
    <View className="mt-4">
      {reportTypes.map((type) => (
        <Pressable
          key={type.reason}
          onPress={() => setReason(type.reason)}
          className="flex-row items-start justify-between py-2"
        >
          <View className="flex-1 mr-4">
            <ThemedText type="bold" className="text-base text-white">{type.reason}</ThemedText>
            <ThemedText className="text-sm text-gray-text mt-2">{type.description}</ThemedText>
          </View>
          <View 
            className={`h-5 w-5 rounded-full border-2 ${
              selectedReason === type.reason 
                ? "bg-primary border-primary" 
                : "border-gray-text"
            }`}
          >
            {selectedReason === type.reason && (
              <View className="h-2 w-2 m-auto rounded-full bg-white" />
            )}
          </View>
        </Pressable>
      ))}
    </View>
  </View>
);
