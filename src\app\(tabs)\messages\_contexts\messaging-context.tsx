import { createContext, useContext, useState } from "react";
import { Nullable } from "@/types/common";

interface MessagingContextType {
    searchValue: string;
    setSearchValue: (value: string) => void;
    selectedGroup: Nullable<IGroupInfo>;
    setSelectedGroup: (group: Nullable<IGroupInfo>) => void;
    isSelectedDirectMessage: boolean;
    setIsSelectedDirectMessage: (value: boolean) => void;
}
export interface IGroupInfo {
    id: string;
    isPinned: boolean;
}

export const MessagingContext = createContext<MessagingContextType>({
    searchValue: '',
    setSearchValue: () => { },
    selectedGroup: null,
    setSelectedGroup: () => { },
    isSelectedDirectMessage: false,
    setIsSelectedDirectMessage: () => { }
});


export const useMessaging = () => useContext(MessagingContext);

export const MessagingProvider = ({ children }: { children: React.ReactNode }) => {
    const [selectedGroup, setSelectedGroup] = useState<Nullable<IGroupInfo>>(null);
    const [searchValue, setSearchValue] = useState<string>('');
    const [isSelectedDirectMessage, setIsSelectedDirectMessage] = useState<boolean>(false);

    return (
        <MessagingContext.Provider
            value={{
                searchValue,
                setSearchValue,
                selectedGroup,
                setSelectedGroup,
                isSelectedDirectMessage,
                setIsSelectedDirectMessage
            }}>
            {children}
        </MessagingContext.Provider>
    );
};  