import { UndefinedInitialDataOptions, useQuery } from "@tanstack/react-query";

import { getRequestUsersSearch } from "@/api/client/chat";
import {
  getIsBlockedByUser,
  getIsCurrentUserBlocked,
  getIsUserBlocked,
  getMe,
  getNewUsers,
  getReferralStats,
  getReferrers,
  getTopUsers,
  getUserByHandle,
  getUserById,
  getUsersSearch,
  getUsersWithBadges,
} from "@/api/client/user";

import { NewUsersResponse, UsersWithBadgesResponse } from "./types";
import { TopUsersResponse } from "./types/top-users-response";

export const useUserByHandleQuery = (handle: string) => {
  return useQuery({
    queryKey: ["user", "handle", handle],
    queryFn: () => {
      return getUserByHandle({ handle });
    },
  });
};

export const useUserByIdQuery = (userId: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ["user", "id", userId],
    queryFn: () => {
      return getUserById({ userId });
    },
    enabled
  });
};

export const useUsersSearchQuery = (searchString: string) => {
  return useQuery<TopUsersResponse | null>({
    queryKey: ["user", "search", searchString],
    queryFn: () => {
      if (!searchString) return null;

      return getUsersSearch({ searchString });
    },
    enabled: !!searchString,
  });
};

type UsersWithBadgesQueryOptions = Omit<
  UndefinedInitialDataOptions<UsersWithBadgesResponse>,
  "queryKey"
>;

export const useUsersWithBadgesQuery = (
  options?: UsersWithBadgesQueryOptions,
) => {
  return useQuery<UsersWithBadgesResponse>({
    queryKey: ["user", "badges"],
    queryFn: getUsersWithBadges,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

type TopUsersQueryOptions = Omit<
  UndefinedInitialDataOptions<TopUsersResponse>,
  "queryKey"
>;

export const useTopUsersQuery = (options?: TopUsersQueryOptions) => {
  return useQuery<TopUsersResponse>({
    queryKey: ["user", "top"],
    queryFn: getTopUsers,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

type NewUsersQueryOptions = Omit<
  UndefinedInitialDataOptions<NewUsersResponse>,
  "queryKey"
>;

export const useNewUsersQuery = (options?: NewUsersQueryOptions) => {
  return useQuery<NewUsersResponse>({
    queryKey: ["user", "page"],
    queryFn: getNewUsers,
    // cache for 5 minutes to prevent unnecessary requests
    staleTime: 1000 * 60 * 5,
    ...options,
  });
};

export const useIsUserBlockedQuery = (userId?: string) => {
  return useQuery({
    queryKey: ["user", "isBlocked", userId],
    queryFn: () => {
      if (!userId) return false;
      return getIsUserBlocked({ userId });
    },
    enabled: !!userId,
  });
};

export const useIsCurrentUserBlockedQuery = (userId?: string) => {
  return useQuery({
    queryKey: ["user", "isTaggedUserBlocked", userId],
    queryFn: () => {
      if (!userId) return false;
      return getIsCurrentUserBlocked({ userId });
    },
    enabled: Boolean(userId),
  });
};

export const useReferrersQuery = () => {
  return useQuery({
    queryKey: ["user", "referrers"],
    queryFn: getReferrers,
  });
};

export const useMeQuery = () => {
  return useQuery({
    queryKey: ["user", "me"],
    queryFn: getMe,
  });
};

export const useReferralStatsQuery = () => {
  return useQuery({
    queryKey: ["user", "stats", "referral"],
    queryFn: getReferralStats,
  });
};

export const useRequestUsersSearchQuery = (searchString: string) => {
  return useQuery<TopUsersResponse | null>({
    queryKey: ["request", "search", searchString],
    queryFn: () => {
      if (!searchString) return null;

      return getRequestUsersSearch({ searchString });
    },
    enabled: !!searchString,
  });
};

export const useIsBlockedByUserQuery = (userId?: string) => {
  return useQuery({
    queryKey: ["user", "isBlockedByUser", userId],
    queryFn: () => {
      if (!userId) return false;
      return getIsBlockedByUser({ userId });
    },
    enabled: Boolean(userId),
  });
};

