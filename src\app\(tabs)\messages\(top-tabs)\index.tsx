import {
  StyleSheet,
  View,
} from "react-native";
import React, { useEffect, useMemo } from "react";
import {
  useConversationsInfiniteQuery,
  useSearchRoomConversationsInfiniteQuery,
} from "@/queries";
import { GroupsResponse } from "@/queries/types/chats";
import { useSocket } from "@/hooks/use-socket";
import { SOCKET_MESSAGE } from "@/environments/socket-messages";
import { InfiniteData, useQueryClient } from "@tanstack/react-query";
import { useUser } from "@/stores";
import { useMessaging } from "../_contexts/messaging-context";
import useThrottle from "@/hooks/use-throttle";
import GroupList from "../_components/group-list";

const Messages = () => {
  const { searchValue } = useMessaging();
  const throttledSearchValue = useThrottle(searchValue).trim();
  const { socket, setSeen } = useSocket();
  const queryClient = useQueryClient();
  const { user } = useUser();
  const { selectedGroup, setSelectedGroup } = useMessaging();

  const {
    data: searchRoomsData,
    isLoading: searchRoomsIsLoading,
    hasNextPage: searchRoomsHasNextPage,
    isFetchingNextPage: searchRoomsIsFetchingNextPage,
    fetchNextPage: searchFetchNextRoomsPage,
    refetch: searchRefetchRooms
  } = useSearchRoomConversationsInfiniteQuery(throttledSearchValue);

  const {
    data: roomsData,
    isLoading: roomsIsLoading,
    hasNextPage: roomsHasNextPage,
    isFetchingNextPage: roomsIsFetchingNextPage,
    fetchNextPage: fetchNextRoomsPage,
    refetch: refetchRooms
  } = useConversationsInfiniteQuery();

  const rooms = useMemo(() => {
    return roomsData?.pages.map((page) => page.groups).flat().filter((group) => !group.isDirect);
  }, [roomsData]);

  const searchRooms = useMemo(() => {
    return searchRoomsData?.pages.map((page) => page.groups).flat().filter((group) => !group.isDirect);
  }, [searchRoomsData]);

  return (
    <View style={styles.flex}>
      <GroupList
        isLoading={throttledSearchValue ? searchRoomsIsLoading : roomsIsLoading}
        data={throttledSearchValue ? searchRooms : rooms}
        refetch={throttledSearchValue ? searchRefetchRooms : refetchRooms}
        hasNextPage={throttledSearchValue ? searchRoomsHasNextPage : roomsHasNextPage}
        fetchNextPage={throttledSearchValue ? searchFetchNextRoomsPage : fetchNextRoomsPage}
        isFetchingNextPage={throttledSearchValue ? searchRoomsIsFetchingNextPage : roomsIsFetchingNextPage}
        setSeen={setSeen}
        emptyListText={"No rooms found!"}
        selectedGroupId={selectedGroup?.id || ""}
        setSelectedGroup={setSelectedGroup}
      />
    </View>
  );
};

export default Messages;

const styles = StyleSheet.create({
  flex: {
    flex: 1,
    marginTop: 15
  },
});
