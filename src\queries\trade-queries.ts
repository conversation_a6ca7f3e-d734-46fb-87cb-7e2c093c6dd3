import { UndefinedInitialDataOptions, useQuery } from "@tanstack/react-query";

import {
  getRecentTrades,
  getTrades,
  getTrendingUsers,
} from "@/api/client/trade";

import {
  TradeRecentResponse,
  TradesUsersTrendingResponse,
  TradeTradesResponse,
} from "./types";

export const useTradesQuery = () => {
  return useQuery<TradeTradesResponse>({
    queryKey: ["trade", "trades"],
    queryFn: getTrades,
  });
};

type RecentTradesQueryOptions = Omit<
  UndefinedInitialDataOptions<TradeRecentResponse>,
  "queryKey"
>;

export const useRecentTradesQuery = (options?: RecentTradesQueryOptions) => {
  return useQuery<TradeRecentResponse>({
    queryKey: ["trade", "recent"],
    queryFn: getRecentTrades,
    // cache for 1 minute to prevent unnecessary requests
    staleTime: 1000 * 60,
    ...options,
  });
};

type TrendingUsersQueryOptions = Omit<
  UndefinedInitialDataOptions<TradesUsersTrendingResponse>,
  "queryKey"
>;

export const useTrendingUsersQuery = (options?: TrendingUsersQueryOptions) => {
  return useQuery<TradesUsersTrendingResponse>({
    queryKey: ["trade", "users", "trending"],
    queryFn: getTrendingUsers,
    // cache for 1 minute to prevent unnecessary requests
    staleTime: 1000 * 60,
    ...options,
  });
};
