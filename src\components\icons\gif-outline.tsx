import { ComponentProps } from "react";
import { Path, Svg, SvgProps } from "react-native-svg";

export const GIFOutlineIcon = (props: SvgProps) => (
  <Svg
    viewBox="0 0 32 32"
    fill="none"
    {...props}
  >
    <Path
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M27.611 27H5.39c-.368 0-.722-.145-.982-.403A1.368 1.368 0 0 1 4 25.625V6.375c0-.365.146-.714.407-.972.26-.258.614-.403.982-.403H27.61c.369 0 .722.145.982.403s.407.607.407.972v19.25c0 .365-.146.714-.407.972-.26.258-.613.403-.982.403Z"
    />
    <Path
      fill="currentColor"
      d="M19.421 19.893v-7.787H24.5v1.183h-3.643v2.114h3.294v1.182h-3.294v3.308h-1.436ZM17.866 12.106v7.787H16.43v-7.787h1.436ZM13.512 14.593a2 2 0 0 0-.267-.551 1.71 1.71 0 0 0-.402-.422 1.634 1.634 0 0 0-.535-.263 2.267 2.267 0 0 0-.658-.09c-.425 0-.805.104-1.138.315-.333.21-.595.52-.786.927-.188.406-.282.9-.282 1.483 0 .588.094 1.087.282 1.495.189.408.45.718.786.931.336.21.725.316 1.17.316.402 0 .75-.076 1.044-.228.297-.152.526-.368.686-.647.16-.281.24-.61.24-.988l.325.05h-2.152v-1.103h3.216v.935c0 .666-.144 1.243-.433 1.73a2.94 2.94 0 0 1-1.193 1.125c-.505.261-1.086.392-1.741.392-.73 0-1.372-.161-1.924-.483a3.365 3.365 0 0 1-1.29-1.38c-.306-.598-.46-1.308-.46-2.13 0-.628.09-1.19.271-1.684.183-.494.439-.914.766-1.258.328-.348.713-.611 1.154-.791a3.731 3.731 0 0 1 1.44-.274c.439 0 .848.063 1.227.19.38.124.716.302 1.01.532.297.231.541.505.732.822.191.317.316.666.376 1.05h-1.464Z"
    />
  </Svg>
);
