import { useMemo } from "react";

import { FlatList, RefreshControl, StyleSheet } from "react-native";

import { Thread } from "@/types";
import { useHomeStore } from "@/stores";
import { Colors } from "@/constants/Colors";
import { ThemedView } from "@/components/ThemedView";
import { useQueryClient } from "@tanstack/react-query";
import { useTrendingThreadsInfiniteQuery } from "@/queries/threads-query";

import { TimelinePost } from "../_components/timeline-post";
import { PostLoadingSkeleton } from "@/components/post-loading-skeletons";

export default function TrendingTimeline() {
  const queryClient = useQueryClient();

  const pullToRefresh = () => {
    return queryClient.resetQueries({
      queryKey: ["home", "threads", "trending-feed"],
    });
  };

  const { snapshot, setSnapshot } = useHomeStore((state) => ({
    snapshot: state.trendingSnapshot,
    setSnapshot: state.setTrendingSnapshot,
  }));

  const { data, fetchNextPage, isLoading, isFetchingNextPage, hasNextPage } =
    useTrendingThreadsInfiniteQuery();

  const threads = useMemo(() => {
    if (!data) return [];

    return data.pages.reduce((prev, current) => {
      return [...prev, ...current.threads];
    }, [] as Thread[]);
  }, [data]);

  return (
    <ThemedView style={[styles.container]}>
      <FlatList
        data={threads}
        keyExtractor={(item, index) => item.id.toString() || index.toString()}
        renderItem={({ item }) => <TimelinePost thread={item} />}
        scrollEventThrottle={16}
        onEndReached={() => {
          if (hasNextPage) fetchNextPage();
        }}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={pullToRefresh} />
        }
        ListEmptyComponent={
          isLoading ? (
            <>
              {Array.from({ length: 7 }).map((_, i) => (
                <PostLoadingSkeleton key={i} />
              ))}
            </>
          ) : null
        }
        ListFooterComponent={
          isFetchingNextPage ? (
            <>
              {Array.from({ length: 5 }).map((_, i) => (
                <PostLoadingSkeleton key={`footer-${i}`} />
              ))}
            </>
          ) : null
        }
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
    position: "relative",
  },
});
