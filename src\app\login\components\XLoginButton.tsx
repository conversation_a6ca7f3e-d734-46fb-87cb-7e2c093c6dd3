import CustomLinearGradient from '@/components/ui/linear-gradient';
import { memo } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';
const { width } = Dimensions.get('window');

// Define the type for the component props
type CustomPressableProps = {
  onPress: () => void;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  isLoading?: boolean;
};

const XLoginButton: React.FC<CustomPressableProps> = ({
  onPress,
  style,
  textStyle,
  isLoading,
}) => {
  return (
    <CustomLinearGradient
      style={[style, styles.wrapperStyle]}
      colors={['#EFEFEF', '#F1F1F1', '#C3C3C3']}
      onPress={onPress}
    >
      {isLoading ? (
        <View style={styles.twitterButton}>
          <ActivityIndicator color={'#C3C3C3'} />
        </View>
      ) : (
        <View style={styles.twitterButton}>
          <Text style={textStyle}>Enter the Arena with</Text>
          <Image
            style={styles.twitterLogo}
            source={require('@assets/icons/twitter.png')}
          />
        </View>
      )}
    </CustomLinearGradient>
  );
};

const styles = StyleSheet.create({
  wrapperStyle: {
    borderRadius: 30,
  },
  twitterButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: width / 1.2,
    padding: 16,
    gap: 7,
  },
  twitterLogo: {
    width: 18,
    aspectRatio: 1,
  },
});

export default memo(XLoginButton);
