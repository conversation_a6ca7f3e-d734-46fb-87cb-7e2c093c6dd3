import { ThemedText } from "@/components/ThemedText";
import { NotificationItem } from "./_components/notification-item";
import { FlatList, StyleSheet } from "react-native";
import { ThemedView } from "@/components/ThemedView";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useNotificationsInfiniteQuery } from "@/queries/notifications-queries";
import { setAllNotificationsSeen } from "@/api/client/notifications";
import { NotificationsCircleOutlineIcon } from "@/components/icons";

function NotificationsPage() {
  const queryClient = useQueryClient();
  const { data, isLoading, isFetchingNextPage, fetchNextPage, hasNextPage } =
    useNotificationsInfiniteQuery();

  useQuery({
    queryKey: ["notifications", "set-all-seen"],
    queryFn: async () => {
      const response = await setAllNotificationsSeen();

      await queryClient.invalidateQueries({
        queryKey: ["notifications", "unseen"],
      });

      return response;
    },
    enabled: !isLoading,
  });

  const notifications = data?.pages.flatMap((page) => page.notifications) ?? [];

  return (
    <ThemedView style={styles.container}>
      {notifications.length === 0 && !isLoading && (
        <ThemedView className="-mt-4 flex h-full flex-col items-center justify-center gap-3">
          <NotificationsCircleOutlineIcon className="h-16 w-16 fill-[#343434] text-[#343434]" />
          <ThemedText className="max-w-[230px] text-center text-base leading-[22px] text-[#5A5A5A]">
            You have no new notifications at this time
          </ThemedText>
        </ThemedView>
      )}

      {notifications.length > 0 && !isLoading && (
        <FlatList
          data={notifications}
          renderItem={({ item }) => <NotificationItem {...item} />}
          keyExtractor={(item, index) => item.id || index.toString()}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    marginTop: 20,
  },
});

export default NotificationsPage;
