import { ArrowDownFilledIcon, ArrowUpFilledIcon } from "@/components/icons";
import { ThemedText } from "@/components/ThemedText";
import Avatar from "@/components/ui/avatar";
import { Colors } from "@/constants/Colors";
import { User } from "@/types";
import { numberFormatter } from "@/utils";
import { Image, StyleSheet, View } from "react-native";

interface ProfileHeaderCardProps {
  data: { user: User } | undefined;
  ticketPrice?: string;
  isNegative?: boolean;
  percentageIncrease?: string;
  showTicketPriceAndFollowers?: boolean;
  totalTicketHolders?: string;
  showTwitterHandle?: boolean;
}

export default function ProfileHeaderCard({
  data,
  ticketPrice,
  isNegative,
  percentageIncrease,
  showTicketPriceAndFollowers,
  totalTicketHolders,
  showTwitterHandle,
}: ProfileHeaderCardProps) {
  return (
    <View style={styles.headerCard}>
      <Avatar src={data?.user.twitterPicture || ""} size={40} />
      <View style={styles.headerCardChild}>
        <View className="gap-1">
          <ThemedText style={styles.xHandle}>
            @{data?.user.twitterHandle}
          </ThemedText>
          {showTwitterHandle ? (
            <ThemedText style={styles.ticketHolderText}>
              {data?.user?.twitterHandle}
            </ThemedText>
          ) : (
            <ThemedText style={styles.ticketHolderText}>
              {showTicketPriceAndFollowers
                ? numberFormatter.format(data?.user.followerCount || 0) +
                  " Followers"
                : `${totalTicketHolders} ticket holders`}
            </ThemedText>
          )}
        </View>
        {showTicketPriceAndFollowers ? (
          <View className="flex flex-col items-end justify-between gap-1">
            <View className="flex flex-row items-center gap-1">
              <Image
                source={require("@assets/coins/avax.png")}
                style={styles.avaxLogo}
              />
              <ThemedText className="text-sm">{ticketPrice}</ThemedText>
            </View>
            <View className="flex items-center">
              <ThemedText
                className={`flex items-center gap-[2px] text-xs ${
                  isNegative ? "text-danger" : "text-[#40B877]"
                }`}
              >
                {percentageIncrease !== "0" && (
                  <>
                    {isNegative ? (
                      <ArrowDownFilledIcon height={4} width={4} />
                    ) : (
                      <ArrowUpFilledIcon height={4} width={4} />
                    )}
                  </>
                )}
                <ThemedText className="text-sm font-semibold text-[#00F470]">
                  {percentageIncrease}%
                </ThemedText>
              </ThemedText>
            </View>
          </View>
        ) : null}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  xHandle: {
    fontWeight: "600",
    fontSize: 14,
  },
  headerCard: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    borderColor: Colors.dark.lightgrey,
    borderWidth: 0.5,
    borderRadius: 10,
    paddingHorizontal: 10,
    paddingVertical: 15,
  },
  ticketHolderText: {
    fontSize: 12,
    color: Colors.dark.lightgrey,
    fontWeight: "500",
  },
  headerCardChild: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    flex: 1,
  },
  avaxLogo: {
    width: 12,
    aspectRatio: 1,
  },
});
