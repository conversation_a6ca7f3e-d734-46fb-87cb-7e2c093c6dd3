import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  ActivityIndicator,
  Image,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { ResizeMode, Video } from "expo-av";
import { Href, router } from "expo-router";
import { v4 } from "uuid";
import Animated, { FadeIn, FadeOut } from "react-native-reanimated";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { ThemedView } from "@/components/ThemedView";
import Avatar from "@/components/ui/avatar";
import ProgressBar from "@/components/ui/progress-bar";
import { XCircleOutlineIcon } from "@/components/icons";
import AddPostActionBox from "@/app/_components/add-post-actions";

import { Colors } from "@/constants/Colors";

import { useUser, useHomeStore, usePostStore } from "@/stores";

import {
  usePostQuoteMutation,
  usePostThreadAnswerMutation,
  usePostThreadMutation,
  useThreadNestedAnswersInfiniteQuery,
} from "@/queries";

import { ThreadsResponse } from "@/queries/types";

import {
  FileType,
  Thread,
  ThreadPrivacyTypeEnum,
  ThreadResponse,
} from "@/types";

import { getOAuthAccessToken } from "@/utils/get-x-token";

import { ReplyPostUI } from "./_components/replyPostUI";
import { QuotePost } from "./_components/quotePostUI";

const NewPostTextInput: React.FC = () => {
  const queryClient = useQueryClient();
  const { user } = useUser();
  const type = usePostStore((state) => state.type);
  const thread = usePostStore((state) => state.thread);
  const setFollowingSnapshot = useHomeStore(
    (state) => state.setFollowingSnapshot
  );
  const setTrendingSnapshot = useHomeStore(
    (state) => state.setTrendingSnapshot
  );

  const { data: nestedAnswersData, isLoading: isAnswersLoading } =
    useThreadNestedAnswersInfiniteQuery(
      type === "reply" && thread ? thread?.id : undefined
    );

  const usersHandles = useMemo(() => {
    if (!nestedAnswersData || isAnswersLoading) return null;

    return Array.from(
      new Set(
        nestedAnswersData.pages
          .reduce((prev, current) => {
            return [...prev, ...(current?.threads ?? [])];
          }, [] as Thread[])
          .reverse()
          .map((thread) => thread?.user?.twitterHandle)
      )
    );
  }, [nestedAnswersData, isAnswersLoading]);

  const [files, setFiles] = useState<FileType[]>([]);
  const [xMediaIds, setXMediaIds] = useState<string[]>([]);

  const [isUploading, setIsUploading] = useState(false);
  const [isUploadingToX, setIsUploadingToX] = useState(false);
  const [content, setContent] = useState("");
  const [preview, setPreview] = useState<{
    url: string;
    type: "video" | "image";
  } | null>(null);
  const [progress, setProgress] = useState(0);

  const [isPrivate, setIsPrivate] = useState(false);
  const [isPostToX, setIsPostToX] = useState(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const isPostToXRef = useRef(isPostToX);
  
  const { mutateAsync: postThread, isPending: isPostPending } =
    usePostThreadMutation({
      onMutate: async (variables) => {
        toast.green("You created a new post!");
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "my-feed"],
        });
        await queryClient.cancelQueries({
          queryKey: ["home", "threads", "trending-feed"],
        });

        const previousMyFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "my-feed",
        ]);
        const previousTrendingFeed:
          | InfiniteData<ThreadsResponse, unknown>
          | undefined = queryClient.getQueryData([
          "home",
          "threads",
          "trending-feed",
        ]);

        const threadId = v4();
        const images = files
          .filter((f) => f.fileType === "image")
          .map((file) => ({
            id: v4(),
            url: file.url,
            threadId,
          }));

        const videos = files
          .filter((f) => f.fileType === "video")
          .map((file) => ({
            id: v4(),
            url: file.url,
            threadId,
          }));

        const newThread: Thread = {
          displayStatus: 0,
          id: threadId,
          content: variables.content,
          contentUrl: "",
          threadType:
            images.length > 0 ? "image" : videos.length > 0 ? "video" : "text",
          userId: user?.id ?? "",
          userName: user?.twitterName ?? "",
          userHandle: user?.twitterHandle ?? "",
          userPicture: user?.twitterPicture ?? "",
          createdDate: new Date().toISOString(),
          answerCount: 0,
          likeCount: 0,
          bookmarkCount: 0,
          repostCount: 0,
          repostId: null,
          answerId: null,
          isDeleted: false,
          privacyType: 0,
          answerPrivacyType: 0,
          language: "en",
          isPinned: false,
          paywall: false,
          price: "0",
          tipAmount: 0,
          tipCount: 0,
          currency: "AVAX",
          currencyAddress: null,
          currencyDecimals: 18,
          like: null,
          bookmark: null,
          reposted: null,
          images,
          videos,
          user: {
            threadCount: user?.threadCount ?? 0,
            followerCount: user?.followerCount ?? 0,
            followingsCount: user?.followingsCount ?? 0,
            twitterFollowers: user?.twitterFollowers ?? 0,
            id: user?.id ?? "",
            createdOn: user?.createdOn ?? "",
            twitterId: user?.twitterId ?? "",
            twitterHandle: user?.twitterHandle ?? "",
            twitterName: user?.twitterName ?? "",
            twitterPicture: user?.twitterPicture ?? "",
            lastLoginTwitterPicture: user?.twitterPicture ?? "",
            bannerUrl: user?.bannerUrl ?? "",
            address: user?.address ?? "",
            ethereumAddress: user?.ethereumAddress ?? "",
            solanaAddress: user?.solanaAddress ?? "",
            prevAddress: user?.prevAddress ?? "",
            addressConfirmed: user?.addressConfirmed ?? false,
            twitterDescription: user?.twitterDescription ?? "",
            signedUp: user?.signedUp ?? false,
            subscriptionCurrency: user?.subscriptionCurrency ?? "",
            subscriptionCurrencyAddress:
              user?.subscriptionCurrencyAddress ?? "",
            subscriptionPrice: user?.subscriptionPrice ?? "",
            keyPrice: user?.keyPrice ?? "",
            subscriptionsEnabled: user?.subscriptionsEnabled ?? false,
            userConfirmed: user?.userConfirmed ?? false,
            twitterConfirmed: user?.twitterConfirmed ?? false,
            flag: user?.flag ?? 0,
            ixHandle: user?.ixHandle ?? "",
            handle: user?.twitterHandle ?? "",
          },
        };

        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: [newThread, ...page.threads],
                  };
                }
                return page;
              }),
            };
          }
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: [newThread, ...page.threads],
                  };
                }
                return page;
              }),
            };
          }
        );

        router.replace("/home/<USER>" as Href<string>);
        return { previousMyFeed };
      },
      onError(err, variables, context) {
        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          context?.previousMyFeed
        );
      },
      onSuccess: (data, variables, context) => {
        if (data.xTweetResult && !data.xTweetResult.success) {
          toast.danger(data.xTweetResult.error || "Failed to post on X");
        }
        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: page.threads.map((t) =>
                      t.id === context.tempThreadId ? data.thread : t,
                    ),
                  };
                }
                return page;
              }),
            };
          },
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: page.threads.map((t) =>
                      t.id === context.tempThreadId ? data.thread : t,
                    ),
                  };
                }
                return page;
              }),
            };
          },
        );
        queryClient.invalidateQueries({
          queryKey: ["threads", "user", user?.id],
        });
        queryClient.invalidateQueries({
          queryKey: ["threads", "uploads"],
        });
        setFollowingSnapshot(undefined);
        setTrendingSnapshot(undefined);
        setFiles([]);
        setXMediaIds([]);
        resetUploading();
        setIsPostToX(false);
        setIsPrivate(false);
      },
    });
  const { mutateAsync: postReply, isPending: isReplyPending } =
    usePostThreadAnswerMutation({
      onSuccess: (data) => {
        toast.green("Your post was sent");
        queryClient.setQueryData(
          ["threads", thread?.id],
          (old: ThreadResponse) => {
            if (!old) return old;

            return {
              ...old,
              thread: {
                ...old.thread,
                answerCount: old.thread.answerCount + 1,
              },
            };
          }
        );

        queryClient.setQueryData(
          ["threads", "answers", thread?.id],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) {
              queryClient.invalidateQueries({
                queryKey: ["threads", "answers", thread?.id],
              });
              return old;
            }

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: [data.thread, ...page.threads],
                  };
                }
                return page;
              }),
            };
          }
        );
        router.replace(`/${thread?.user?.twitterHandle}/nested/${thread?.id}` as Href<string>);

        setFiles([]);
        resetUploading();
      },
    });
  const { mutateAsync: postQuote, isPending: isQuotePending } =
    usePostQuoteMutation({
      onSuccess: (data) => {
        queryClient.setQueryData(
          ["home", "threads", "my-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: [
                      {
                        ...data.thread,
                        repost: {
                          ...data.repostingThread,
                          user: data.repostingThread?.user
                            ? data.repostingThread?.user
                            : thread?.user,
                        },
                      },
                      ...page.threads,
                    ],
                  };
                }
                return page;
              }),
            };
          }
        );
        queryClient.setQueryData(
          ["home", "threads", "trending-feed"],
          (old: InfiniteData<ThreadsResponse, unknown>) => {
            if (!old) return old;

            return {
              ...old,
              pages: old.pages.map((page, index) => {
                if (index === 0) {
                  return {
                    ...page,
                    threads: [
                      {
                        ...data.thread,
                        repost: {
                          ...data.repostingThread,
                          user: data.repostingThread?.user
                            ? data.repostingThread?.user
                            : thread?.user,
                        },
                      },
                      ...page.threads,
                    ],
                  };
                }
                return page;
              }),
            };
          }
        );

        setFollowingSnapshot(undefined);
        setTrendingSnapshot(undefined);
        toast.green("You created a new post!");
        router.replace("/(tabs)/home/<USER>");

        setFiles([]);
        resetUploading();
      },
    });

  const handlePost = async () => {
    if (content.trim() === "" && files.length === 0) {
      return;
    }
    await postThread({
      content,
      privacyType: isPrivate
        ? ThreadPrivacyTypeEnum.SHAREHOLDERS
        : ThreadPrivacyTypeEnum.PUBLIC,
      files,
      ...(isPostToX &&
        accessToken && {
          xPostData: {
            isPostToX,
            accessToken,
            mediaIds: xMediaIds.length > 0 ? [xMediaIds[0]] : [],
          },
        }),
    });
  };

  const handleReply = async () => {
    if (!thread) return;
    if (content.trim() === "" && files.length === 0) {
      return;
    }

    await postReply({
      content,
      threadId: thread?.id,
      userId: thread?.userId,
      files,
    });
  };

  const handleQuote = async () => {
    if (!thread) return;
    if (content.trim() === "" && files.length === 0) {
      return;
    }
    const threadId =
      thread.threadType === "repost" && thread.repost
        ? thread.repost.id
        : thread.id;
    await postQuote({
      threadId,
      content,
      files,
    });
  };

  const resetUploading = () => {
    setIsUploading(false);
    setIsUploadingToX(false);
    setProgress(0);
    setPreview(null);
  };

  useEffect(() => {
    isPostToXRef.current = isPostToX;

    const fetchTokenOnDemand = async () => {
      if (!isPostToX || accessToken) return;
      const token = await getOAuthAccessToken();
      setAccessToken(token);
    };

    fetchTokenOnDemand();
  }, [isPostToX]);

  return (
    <ThemedView style={styles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : undefined}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.wrapper}>
            <ScrollView
              style={{ flex: 1 }}
              keyboardShouldPersistTaps="handled"
              contentContainerStyle={{ paddingBottom: 12 }}
              showsVerticalScrollIndicator={false}
            >
              {type && thread && type === "reply" && (
                <ReplyPostUI thread={thread} usersHandles={usersHandles} />
              )}
              <View
                style={[
                  styles.InputWrapper,
                  type === "reply" && { paddingTop: 4 },
                ]}
              >
                <Avatar src={user?.twitterPicture || ""} size={42} />
                <View className="flex felx-col flex-1 space-y-3">
                  <TextInput
                    multiline
                    placeholderTextColor={Colors.dark.grey}
                    placeholder={
                      type === "reply"
                        ? "Post your reply"
                        : "What's Happening?"
                    }
                    style={styles.textInput}
                    value={content}
                    onChangeText={(text) => setContent(text)}
                  />
                  {(preview || files.length > 0) && (
                    <View className="relative overflow-hidden rounded-[10px]">
                      {preview && preview.type === "video" && (
                        <Video
                          source={{
                            uri:
                              isUploading && !files[0]?.url
                                ? preview.url
                                : files[0]?.url,
                          }}
                          useNativeControls={true}
                          resizeMode={ResizeMode.COVER}
                          style={styles.image}
                          isLooping
                          shouldPlay
                          isMuted
                        />
                      )}
                      {preview && preview.type === "image" && (
                        <Image
                          style={styles.image}
                          source={{
                            uri:
                              isUploading && !files[0]?.url
                                ? preview.url
                                : files[0]?.url,
                          }}
                          resizeMode="cover"
                        />
                      )}
                      {!preview && files.length > 0 && (
                        <Image
                          style={styles.image}
                          source={{ uri: files[0].url }}
                        />
                      )}

                      <TouchableOpacity
                        onPress={() => {
                          setFiles([]);
                          resetUploading();
                        }}
                        className="absolute right-2 top-2 z-10"
                      >
                        <XCircleOutlineIcon
                          fill={"#020202BF"}
                          height={23}
                          width={23}
                          className="fill-dark-bk/75 text-off-white"
                        />
                      </TouchableOpacity>
                      {isUploading && (!isPostToX || progress < 75) && (
                        <View className="w-full absolute bottom-0 left-0">
                          <ProgressBar progressValue={progress} />
                        </View>
                      )}
                      {(isUploadingToX || (isPostToX && isUploading && progress >= 75)) && (
                        <Animated.View
                          entering={FadeIn}
                          exiting={FadeOut}
                          style={StyleSheet.absoluteFill}
                          className="flex items-center justify-center rounded-[10px] bg-[#0F0F0F]/40"
                        >
                          <View className="flex items-center justify-center gap-1 rounded-[10px] bg-[#0F0F0F]/90 p-6">
                            <ActivityIndicator size="large" color="#ffffff" />
                            <Text className="text-sm font-medium text-off-white mt-2">
                              Processing media to X...
                            </Text>
                          </View>
                        </Animated.View>
                      )}
                    </View>
                  )}
                  {type === "quote" && thread && <QuotePost {...thread} />}
                </View>
              </View>
            </ScrollView>
            <AddPostActionBox
              content={content}
              handlePost={handlePost}
              handleQuote={handleQuote}
              handleReply={handleReply}
              isPostPending={isPostPending}
              isReplyPending={isReplyPending}
              isQuotePending={isQuotePending}
              isUploading={isUploading}
              setIsUploading={setIsUploading}
              files={files}
              setFiles={setFiles}
              setPreview={setPreview}
              isPrivate={isPrivate}
              setIsPrivate={setIsPrivate}
              setProgress={setProgress}
              isPostToX={isPostToX}
              setIsPostToX={setIsPostToX}
              xMediaIds={xMediaIds}
              setXMediaIds={setXMediaIds}
              isUploadingToX={isUploadingToX}
              setIsUploadingToX={setIsUploadingToX}
            />
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderTopWidth: 1,
    borderTopColor: Colors.dark.grey,
  },
  wrapper: {
    flex: 1,
    justifyContent: "space-between",
  },
  InputWrapper: {
    flexDirection: "row",
    gap: 12,
    paddingHorizontal: 24,
    paddingTop: 22,
  },
  textInput: {
    minHeight: 42,
    borderBottomWidth: 1,
    color: Colors.dark.offWhite,
  },
  image: {
    width: "100%",
    height: 180,
    alignSelf: "flex-end",
  },
});

export default NewPostTextInput;
