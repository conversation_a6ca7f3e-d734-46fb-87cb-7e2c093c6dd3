import { View } from "react-native";
import { Skeleton } from "@/components/ui/skaleton";

const ProfileHeaderSkeleton = () => (
  <View>
    {/* Banner Skeleton */}
    <Skeleton height={120} width="100%" style={{ borderRadius: 0 }} />
    <View className="px-5">
      <View className="flex-row items-center gap-[25px] mb-2.5">
        {/* Avatar Skeleton */}
        <View className="h-[90px] -mt-10 rounded-full overflow-hidden border border-white justify-center items-center">
          <Skeleton height={90} width={90} style={{ borderRadius: 45 }} />
        </View>
        {/* Followers/Following Skeletons */}
        <View className="gap-2">
          <Skeleton height={16} width={60} style={{ borderRadius: 4 }} />
          <Skeleton height={14} width={40} style={{ borderRadius: 4 }} />
        </View>
        <View className="gap-2">
          <Skeleton height={16} width={60} style={{ borderRadius: 4 }} />
          <Skeleton height={14} width={40} style={{ borderRadius: 4 }} />
        </View>
        {/* Rank or Twitter Skeleton */}
        <Skeleton height={16} width={40} style={{ borderRadius: 4 }} />
      </View>
      {/* Name and handle */}
      <View className="mb-5 mt-2">
        <Skeleton height={18} width={120} style={{ borderRadius: 4, marginBottom: 4 }} />
        <Skeleton height={14} width={80} style={{ borderRadius: 4 }} />
      </View>
      {/* Bio */}
      <View className="mb-5">
        <Skeleton height={14} width="100%" style={{ borderRadius: 4, marginBottom: 4 }} />
        <Skeleton height={14} width="80%" style={{ borderRadius: 4 }} />
      </View>
      {/* Buttons */}
      <View className="flex-row gap-x-3 justify-center">
        <Skeleton height={40} width={140} style={{ borderRadius: 9999 }} />
        <Skeleton height={40} width={140} style={{ borderRadius: 9999 }} />
      </View>
    </View>
  </View>
);

export default ProfileHeaderSkeleton; 