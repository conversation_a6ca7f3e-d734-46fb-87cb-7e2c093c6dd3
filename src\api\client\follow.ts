import { axios } from "@/lib/axios";
import { FollowUserData } from "@/queries/types";
import { FollowersResponse, FollowingResponse } from "@/queries/types/follow";

export const postFollowUser = async ({ userId }: FollowUserData) => {
  const response = await axios.post(`/follow/follow`, {
    userId,
  });
  return response.data;
};

export const postUnfollowUser = async ({ userId }: FollowUserData) => {
  const response = await axios.post(`/follow/unfollow`, {
    userId,
  });
  return response.data;
};

interface GetFollowerListParams {
  userId: string;
  searchString: string;
  page: number;
  pageSize: number;
}

export const getFollowersList = async ({
  userId,
  searchString,
  page,
  pageSize,
}: GetFollowerListParams) => {
  const searchParams = new URLSearchParams({
    followersOfUserId: userId,
    searchString,
    pageNumber: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<FollowersResponse>(
    `/follow/followers/list?${searchParams.toString()}`,
  );
  return response.data;
};

export const getFollowingList = async ({
  userId,
  searchString,
  page,
  pageSize,
}: GetFollowerListParams) => {
  const searchParams = new URLSearchParams({
    followingUserId: userId,
    searchString,
    pageNumber: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<FollowingResponse>(
    `/follow/following/list?${searchParams.toString()}`,
  );
  return response.data;
};
