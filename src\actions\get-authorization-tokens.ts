import { axios } from "@/lib/axios";
import { env } from "@/env";

interface AuthorizeUrlResponse {
  authUrl: string;
}

export async function getAuthorizationTokens(ref: string | null) {
  try {
    // if (ref) {
    //   cookies().set("__ref", ref, { httpOnly: true });
    // }

    const { data } = await axios<AuthorizeUrlResponse>("/twitter/login", {
      params: {
        callbackUrl: `${env.EXPO_PUBLIC_APP_DOMAIN}twitter/auth`,
      },
    });
  
    return {
      url: data.authUrl,
    };
  } catch (error) {
    console.error("error", error);
  }
}
