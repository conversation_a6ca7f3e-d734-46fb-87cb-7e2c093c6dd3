import { navigationRoutes } from '@/navigationRoutes';
import { Me, TwitterUser } from '@/types';
import { router } from 'expo-router';
import { createContext, useContext, useEffect } from 'react';

const UserContext = createContext<{
  user: Me | null;
  twitterUser: TwitterUser | null;
  token: string | null;
  setUser: React.Dispatch<React.SetStateAction<any>>;
  setTwitterUser: React.Dispatch<React.SetStateAction<any>>;
  setToken: React.Dispatch<React.SetStateAction<any>>;
}>({
  user: null,
  twitterUser: null,
  token: null,
  setUser: () => {},
  setTwitterUser: () => {},
  setToken: () => {},
});

export function useUser() {
  return useContext(UserContext);
}

interface UserProviderProps {
  user: Me | null;
  twitterUser: TwitterUser | null;
  setUser: React.Dispatch<React.SetStateAction<any>>;
  setTwitterUser: React.Dispatch<React.SetStateAction<any>>;
  setToken: React.Dispatch<React.SetStateAction<any>>;
  token: string | null;
  children: React.ReactNode;
}

export function UserProvider({
  children,
  user,
  twitterUser,
  setUser,
  setTwitterUser,
  setToken,
  token,
}: UserProviderProps) {
  useEffect(() => {
    if (token) {
      router.replace(navigationRoutes.home);
    } else router.replace(navigationRoutes.login);
  }, [token]);

  return (
    <UserContext.Provider
      value={{ user, twitterUser, token, setUser, setTwitterUser, setToken }}
    >
      {children}
    </UserContext.Provider>
  );
}
