import React, {
  createContext,
  useContext,
  useRef,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from 'react';
import { BottomSheetBackdrop, BottomSheetBackdropProps, BottomSheetModal, BottomSheetView } from '@gorhom/bottom-sheet';
import { BackHandler, Platform } from 'react-native';
import { Colors } from '@/constants/Colors';

interface BottomSheetContextProps {
  openBottomSheet: (content: ReactNode, height?: number, onClose?: () => void) => void;
  closeBottomSheet: () => void;
}

const BottomSheetContext = createContext<BottomSheetContextProps | undefined>(undefined);

interface BottomSheetProviderProps {
  children: ReactNode;
}

export const BottomSheetProvider: React.FC<BottomSheetProviderProps> = ({ children }) => {
  const sheetRef = useRef<BottomSheetModal>(null);
  const [content, setContent] = useState<ReactNode | null>(null);
  const [sheetHeight, setSheetHeight] = useState<number>(150);
  const [isOpen, setIsOpen] = useState(false);
  const onCloseCallbackRef = useRef<(() => void) | null>(null);

  const snapPoints = [sheetHeight];
  
  const openBottomSheet = useCallback((content: ReactNode, height: number = 300, onClose?: () => void) => {
    setContent(content);
    setSheetHeight(height);
    onCloseCallbackRef.current = onClose || null;  
  }, []);

  const closeBottomSheet = useCallback(() => {
    if (sheetRef.current) {
      sheetRef.current.dismiss();
    }
  }, []);

  const handleSheetChange = useCallback((index: number) => {
    if (index === -1) {
      setIsOpen(false);
      
      setTimeout(() => {
        if (onCloseCallbackRef.current) {
          onCloseCallbackRef.current();
          onCloseCallbackRef.current = null;
        }
      }, 100);
    }
  }, []);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        pressBehavior="close"
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        enableTouchThrough={false}
        opacity={0.3}
        style={[
          props.style,
          {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          },
        ]}
      />
    ),
    []
  );

  useEffect(() => {
    if (!isOpen) {
      const timer = setTimeout(() => {
        if (!isOpen) {
          setContent(null);
        }
      }, 300);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  useEffect(() => {
    if (content && sheetRef.current) {
      requestAnimationFrame(() => {
        sheetRef.current?.present();
        setIsOpen(true);
      });
    }
  }, [content]);
  
  // Handle Android back button
  useEffect(() => {
    if(!isOpen) return;
    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
        if (isOpen) {
          closeBottomSheet();
          return true;
        }
        return false;
      });

      return () => backHandler.remove();
    }
  }, [isOpen, closeBottomSheet]);

  return (
    <BottomSheetContext.Provider value={{ openBottomSheet, closeBottomSheet }}>
      {children}

      <BottomSheetModal
        ref={sheetRef}
        snapPoints={snapPoints}
        enablePanDownToClose={true}
        enableContentPanningGesture={true}
        onChange={handleSheetChange}
        backdropComponent={renderBackdrop}
        backgroundStyle={{
          backgroundColor: Colors.dark.greyBackground,
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          borderColor: '#3B3B3B80',
          borderWidth: 1,
          borderBottomWidth: 0,
        }}
        handleIndicatorStyle={{ 
          backgroundColor: 'white', 
          width: 40 
        }}
      >
        <BottomSheetView
          style={{ padding: 16 }}
        >
          {content}
        </BottomSheetView>
      </BottomSheetModal>
    </BottomSheetContext.Provider>
  );
};

export const useBottomSheet = (): BottomSheetContextProps => {
  const context = useContext(BottomSheetContext);
  if (!context) {
    throw new Error('useBottomSheet must be used within a BottomSheetProvider');
  }
  return context;
};
