import { APP_CONFIG } from "@/config";

export function truncate(
  text: string,
  character_limit: number = APP_CONFIG.MAX_CHAR_LIMIT,
): [string, boolean] {
  // Remove leading and trailing spaces and replace multiple spaces with a single space
  const trimmed = text.trim().replace(/\s\s+/g, " ");

  // If the text is already short enough, return it
  if (trimmed.length <= character_limit) {
    return [trimmed, false];
  }

  // Split the text into words
  const words = trimmed.slice(0, character_limit).split(" ");

  // Remove last word and add elipsis
  const finalText = words.slice(0, words.length - 1).join(" ") + "...";

  return [finalText, true];
}
