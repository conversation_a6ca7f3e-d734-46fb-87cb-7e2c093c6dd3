import React from "react";
import Svg, { Path, Rect, SvgProps } from "react-native-svg";

export const CopyOutlineIcon = (props: SvgProps) => (
  <Svg
    fill="none"
    viewBox="0 0 512 512"
    stroke={props.stroke || '#B0B0B0'}
    {...props}
  >
    <Rect
      width={336}
      height={336}
      x={128}
      y={128}
      fill="none"
      stroke={props.stroke || '#B0B0B0'}
      strokeLinejoin="round"
      strokeWidth={32}
      rx={57}
      ry={57}
    />
    <Path
      fill="none"
      stroke={props.stroke || '#B0B0B0'}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={32}
      d="m383.5 128 .5-24a56.16 56.16 0 0 0-56-56H112a64.19 64.19 0 0 0-64 64v216a56.16 56.16 0 0 0 56 56h24"
    />
  </Svg>
);
