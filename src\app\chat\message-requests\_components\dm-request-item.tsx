import { ImageSourcePropType, StyleSheet, TouchableOpacity, View } from "react-native";
import React, { memo, useMemo } from "react";
import Avatar, { AvatarFallBack, LocalAvatar } from "@/components/ui/avatar";
import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { Nullable } from "@/types/common";
import { cleanMessage } from "@/utils/string-formatter";
import { DMRequestDeleteIcon } from "@/components/icons";
import { leaveChat } from "@/api/client/chat";
import { useUserByIdQuery } from "@/queries/user-queries";
import { router } from "expo-router";
import { getBadgeImage } from "@/utils/check-content";

const AVATAR_SIZE = 42;

interface MessageItemProps {
    groupId: string;
    groupName: string;
    groupImageUrl: string;
    lastMessage: Nullable<string>;
    chatMateId: string;
    ownerUserId: string;
    loggedInUserId: string;
    chatMateName: string;
}

const DMRequestItem = memo(({
    groupId,
    groupName,
    groupImageUrl,
    lastMessage,
    chatMateId,
    ownerUserId,
    loggedInUserId,
    chatMateName
}: MessageItemProps) => {

    const chatMate = useUserByIdQuery(chatMateId || "", !!chatMateId);
    const owner = useUserByIdQuery(ownerUserId || "", !!ownerUserId);
    const typingUser = '';

    const lastMessagePreview = useMemo(() => {
        return lastMessage ? cleanMessage(lastMessage || '') : ''
    }, [lastMessage])

    const messagePreview = useMemo(() => {
        return typingUser ? `${typingUser} is typing...` : lastMessagePreview ? lastMessagePreview : ''
    }, [typingUser, lastMessagePreview])

    const userHandle = useMemo(() => {
        //if owner is logged in user, show chatmate's handle, otherwise show owner's handle
        return ownerUserId === loggedInUserId
            ? chatMate?.data?.user?.twitterHandle
            : owner?.data?.user?.twitterHandle;
    }, [ownerUserId, loggedInUserId, chatMate?.data?.user?.twitterHandle, owner?.data?.user?.twitterHandle])

    const name = useMemo(() => {
        //if owner is logged in user, show chatmate's name, otherwise show group name (owner name)
        return ownerUserId === loggedInUserId
            ? chatMateName
            : groupName;
    }, [ownerUserId, loggedInUserId, chatMateName, groupName])

    const groupProfilePicture = useMemo(() => {
        //if owner is logged in user, show chatmate's profile picture, otherwise show group profile picture (owner profile picture)
        return ownerUserId === loggedInUserId
            ? chatMate?.data?.user?.twitterPicture || ""
            : groupImageUrl || "";
    }, [ownerUserId, loggedInUserId, chatMate?.data?.user?.twitterPicture, groupImageUrl])

    const handleDeleteGroup = () => {
        leaveChat({ groupId: groupId });
    }

    return (
        <TouchableOpacity
            onPress={() => {
                router.push(`/chat/${groupId}`)
            }}
            activeOpacity={0.7}
            style={[
                styles.requestItemBox
            ]}
        >
            <View style={styles.leftSection}>
                {getBadgeImage(groupName) ? (
                    <LocalAvatar avatarSize={AVATAR_SIZE} src={getBadgeImage(groupName) as ImageSourcePropType} />
                ) : groupProfilePicture ? (
                    <Avatar size={AVATAR_SIZE} src={groupProfilePicture} />
                ) : <AvatarFallBack avatarSize={AVATAR_SIZE} />}
            </View>
            <View style={styles.middleSection}>
                <View style={styles.groupNameContainer}>
                    <ThemedText numberOfLines={1} style={styles.groupName}>
                        {name}
                        {
                            userHandle ? (
                                <>
                                    <ThemedText style={styles.dot}> . </ThemedText>
                                    <ThemedText type="defaultGrey">{`@${userHandle}`}</ThemedText>
                                </>
                            ) : null
                        }

                    </ThemedText>
                </View>
                {
                    messagePreview ? (
                        <ThemedText
                            ellipsizeMode="tail"
                            numberOfLines={1}
                            type="defaultGrey"
                        >
                            {messagePreview}
                        </ThemedText>
                    ) : null
                }
            </View>
            <TouchableOpacity style={styles.rightSection} onPress={handleDeleteGroup}>
                <DMRequestDeleteIcon color={Colors.dark.lightGreyText} height={18} width={18} />
            </TouchableOpacity>
        </TouchableOpacity>
    );
}, (prevProps, nextProps) => {
    return (
        //don't re-render if the props are the same
        prevProps.groupId === nextProps.groupId &&
        prevProps.groupName === nextProps.groupName &&
        prevProps.groupImageUrl === nextProps.groupImageUrl &&
        prevProps.chatMateId === nextProps.chatMateId &&
        prevProps.ownerUserId === nextProps.ownerUserId &&
        prevProps.loggedInUserId === nextProps.loggedInUserId &&
        prevProps.chatMateName === nextProps.chatMateName &&
        prevProps.lastMessage === nextProps.lastMessage
    );
});

export default DMRequestItem;

const styles = StyleSheet.create({
    requestItemBox: {
        flexDirection: "row",
        paddingVertical: 15,
        width: '100%'
    },
    leftSection: {
        width: '15%',
    },
    middleSection: {
        gap: 3,
        width: '75%',
        justifyContent: 'center',
    },
    groupName: {
        fontSize: 14,
        color: Colors.dark.offWhite,
        fontWeight: "700",
    },
    rightSection: {
        gap: 3,
        justifyContent: 'center',
        width: '10%',
        alignItems: 'flex-end',
    },
    groupNameContainer: {
        flexDirection: 'row',
        gap: 3,
        alignItems: 'center',
        width: '100%'
    },
    dot: {
        fontSize: 14,
        color: Colors.dark.lightGreyText,
        fontWeight: "700",
    }
});
