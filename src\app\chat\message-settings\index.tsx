import {
    StyleSheet,
    View,
} from "react-native";
import React, { useEffect, useMemo, useState } from "react";
import { ThemedText } from "@/components/ThemedText";
import CustomSwitch from "@/components/ui/switch";
import { CheckBox } from "@/components/ui/checkbox";
import { useQuery } from "@tanstack/react-query";
import { useUser } from "@/stores";
import { v4 } from "uuid";
import { getSettings } from "@/api/client/chat";
import { MessageSettingsSkeleton } from "./_components/skeleton";
import { useChatSettingsMutation } from "@/queries/chat-settings-mutation";
import { toast } from "@/components/toast";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Button } from "@/components/ui/button";


const MessageSettings: React.FC = () => {
    const [enableDirectMessage, setEnableDirectMessage] = useState(false);
    const [onlyTicketHolders, setOnlyTicketHolders] = useState(false);
    const [usersIFollow, setUsersIFollow] = useState(false);
    const { bottom } = useSafeAreaInsets();

    const user = useUser();
    const uuid = useMemo(v4, []);

    useEffect(() => {
        if (!enableDirectMessage) {
            setOnlyTicketHolders(false);
            setUsersIFollow(false);
        } else if (!onlyTicketHolders && !usersIFollow) {
            setOnlyTicketHolders(true);
            setUsersIFollow(false);
        }
    }, [enableDirectMessage])

    useEffect(() => {
        if (!onlyTicketHolders && !usersIFollow) setEnableDirectMessage(false)
        if (onlyTicketHolders || usersIFollow) setEnableDirectMessage(true)
    }, [onlyTicketHolders, usersIFollow])

    const { isLoading, isSuccess } = useQuery({
        queryKey: ["chat", "settings", { userId: user?.user?.id || uuid }],
        queryFn: () =>
            getSettings().then((res) => {
                if ("holders" in res) setOnlyTicketHolders(res.holders);
                if ("followers" in res) setUsersIFollow(res.followers);
                return res;
            }),
    });

    const { mutate: setSettings, } = useChatSettingsMutation({
        onSuccess: () => {
            toast.green("The settings have been saved");
            router.back()
        },
        onError: () => toast.red("Something went wrong"),
    });

    const onMessageSettingsSave = () => {
        setSettings({
            holders: onlyTicketHolders,
            followers: usersIFollow,
        });
    }

    if (isLoading) {
        return <MessageSettingsSkeleton />
    }
    if (isSuccess) {
        return (
            <View style={styles.flex}>
                <View style={[styles.row, styles.margin40]}>
                    <View style={styles.enableDirectMessage}>
                        <ThemedText style={styles.enableDirectMessageText} type="bold">Enable Direct Messages</ThemedText>
                    </View>
                    <CustomSwitch
                        isEnabled={enableDirectMessage}
                        setIsEnabled={setEnableDirectMessage}
                    />
                </View>
                {
                    enableDirectMessage ? (
                        <>
                            <ThemedText style={styles.configHeader} type="defaultSemiBold">Customize who can DM you:</ThemedText>
                            <ThemedText type="bold" style={styles.margin8}>ONLY TICKET HOLDERS</ThemedText>
                            <View style={[styles.row, styles.margin30]}>
                                <View style={styles.subtext}>
                                    <ThemedText type="defaultGrey">Ticket holders can send you DM requests.</ThemedText>
                                </View>
                                <CheckBox isDisabled={!enableDirectMessage} value={onlyTicketHolders} onValueChange={setOnlyTicketHolders} />
                            </View>
                            <ThemedText type="bold" style={styles.margin8}>USERS I FOLLOW</ThemedText>
                            <View style={styles.row}>
                                <View style={styles.subtext}>
                                    <ThemedText type="defaultGrey">Followed users can send you DM requests.</ThemedText>
                                </View>
                                <CheckBox isDisabled={!enableDirectMessage} value={usersIFollow} onValueChange={setUsersIFollow} />
                            </View>
                        </>
                    ) : null
                }
                <Button
                    onPress={onMessageSettingsSave}
                    style={[styles.saveButton, { bottom: bottom }]}
                >
                    <ThemedText type="bold">Save</ThemedText>
                </Button>
            </View>
        );
    }
};

export default MessageSettings;

const styles = StyleSheet.create({
    flex: {
        flex: 1,
        paddingHorizontal: 20,
        paddingVertical: 40
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    enableDirectMessage: {
        flex: 0.90
    },
    enableDirectMessageText: {
        fontSize: 16
    },
    configHeader: {
        marginBottom: 30,
        fontSize: 16
    },
    margin8: {
        marginBottom: 8
    },
    margin40: {
        marginBottom: 40,
    },
    margin30: {
        marginBottom: 30,
    },
    subtext: {
        flex: 0.95
    },
    saveButton: {
        marginBottom: 20,
        position: 'absolute',
        alignSelf: 'center',
        width: '100%'
    }
});
