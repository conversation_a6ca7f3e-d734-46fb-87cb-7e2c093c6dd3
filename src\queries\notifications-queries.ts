import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import {
  getNotificaitons,
  getUnseenNotifications,
} from "@/api/client/notifications";

export const useNotificationsInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["notifications"],
    queryFn: ({ pageParam }) => {
      return getNotificaitons({
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      });
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.numberOfResults;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.notifications.length;
      }, 0);
      const lastPageLength = pages[pages.length - 1].notifications.length;
      const isLastPageLengthLessThanPageSize =
        lastPageLength < lastPageParam.pageSize;

      if (
        !isLastPageLengthLessThanPageSize &&
        total !== currentLength &&
        (total > currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    refetchOnMount: "always",
  });
};

export const useUnseenNotificationsQuery = () => {
  return useQuery({
    queryKey: ["notifications", "unseen"],
    queryFn: getUnseenNotifications,
  });
};
