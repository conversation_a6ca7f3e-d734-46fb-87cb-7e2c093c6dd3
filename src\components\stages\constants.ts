import { <PERSON><PERSON><PERSON>, <PERSON>VA<PERSON>, COQ, NOCHIL<PERSON> } from "@/environments/tokens";

export const ROLES = {
  HOST: "HOST",
  COHOST: "COHOST",
  SPEAKER: "SPEAKER",
  LISTENER: "LISTENER",
} as const;

export const ROLE_NAMES = {
  [ROLES.HOST]: "Host",
  [ROLES.COHOST]: "Co-Host",
  [ROLES.SPEAKER]: "Speaker",
  [ROLES.LISTENER]: "Listener",
} as const;

export const ROLE_ORDER = [
  ROLES.HOST,
  ROLES.COHOST,
  ROLES.SPEAKER,
  ROLES.LISTENER,
] as const;

export const INVITED_ROLES = {
  [ROLES.COHOST]: ROLES.COHOST,
  [ROLES.SPEAKER]: ROLES.SPEAKER,
} as const;

export type Role = (typeof ROLES)[keyof typeof ROLES];

export type InvitedRole = (typeof INVITED_ROLES)[keyof typeof INVITED_ROLES];

export const MAX_COHOSTS = 2;
export const MAX_SPEAKERS = 8;

export const tokens = [ARENA, AVAX, NOCHILL, COQ];
