import {
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
  createMaterialTopTabNavigator,
} from "@react-navigation/material-top-tabs";
import { withLayoutContext, router } from "expo-router";
import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import {
  Dimensions,
  StyleSheet,
} from "react-native";
import { Colors } from "@/constants/Colors";
import { WalletCurrencyCards } from "../_components/wallet-currency-cards";
import { SafeAreaView } from "react-native-safe-area-context";

const { Navigator } = createMaterialTopTabNavigator();

const { height, width } = Dimensions.get("window");

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

export default function TabLayout() {
  return (
    <>
      <WalletCurrencyCards />
      <MaterialTopTabs
        screenOptions={{
          tabBarStyle: styles.tabBarStyle,
          tabBarItemStyle: styles.tabBarItemStyle,
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
        }}
      >
        <MaterialTopTabs.Screen name="holding" options={{ title: "Holding" }} />
        <MaterialTopTabs.Screen
          name="ticket-holders"
          options={{ title: "Ticket Holders" }}
        />
        <MaterialTopTabs.Screen
          name="activity"
          options={{ title: "Activity" }}
        />
      </MaterialTopTabs>
    </>
  );
}

const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: "transparent",
  },
  tabBarItemStyle: {
    padding: 0,
  },
  tabBarLabelStyle: {
    fontSize: 13,
    textTransform: "none",
  },
  tabBarIndicatorStyle: {
    backgroundColor: Colors.dark.primary,
  },
});
