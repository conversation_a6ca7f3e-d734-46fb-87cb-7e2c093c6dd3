import { swapTokens } from "@/environments/tokens";
import { truncate as truncateContent } from "@/utils/truncate";
import { ImageSourcePropType } from "react-native";

interface CheckContentProps {
  content: string | null;
  characterLimit?: number;
  truncate?: boolean;
}

export function checkContent({
  content,
  characterLimit,
  truncate = true,
}: CheckContentProps): [string, boolean, string | null] {
  if (!content || content.trim().length === 0) {
    return ["", false, null];
  }
  const [truncatedContent, isTruncated] = truncate
    ? truncateContent(content, characterLimit)
    : [content, false];

  let youtubeEmbed = null;

  const urlRegex =
    /(((https?:\/\/)|(www\.))[^<\s]+[\w\/?&=#.%+-]*|arena\.social(?:\/[^\s<]*)?|((www\.)|([a-zA-Z0-9\-]+?\.)+)(com|ch|de|net|org|it|at|eu|biz|io|xyz|tech|tr)(\/[^\s,<.\s@]*)?(\?[^\s<]*)?(#[^\s<]*)?)/g;

  let processedContent = truncatedContent.replace(urlRegex, (url) => {
    if (getYouTubeId(url)) {
      youtubeEmbed = "https://www.youtube.com/embed/" + getYouTubeId(url);
    }

    if (url.startsWith("https://arena.social/")) {
      const remainingUrl = url.slice("https://arena.social/".length);
      return `<a href="#" data-internal="true" data-path="/${remainingUrl}">${url}</a>`;
    } else if (url === "https://arena.social" || url === "arena.social") {
      return `<a href="#" data-internal="true" data-path="/">${url}</a>`;
    } else if (url.startsWith("arena.social/")) {
      const remainingUrl = url.slice("arena.social/".length);
      return `<a href="#" data-internal="true" data-path="/${remainingUrl}">${url}</a>`;
    }

    return `<a href="${url.startsWith("http") ? url : "https://" + url}" data-external="true">${url}</a>`;
  });

  return [processedContent, isTruncated, youtubeEmbed];
}

export function insertSwapLinks(
  content: string,
  threadId: string,
  currencySymbols: string[],
): [
    contentWithSwapLinks: string,
    tokenSwap: { tokenName: string; tokenURL: string } | null,
  ] {
  let tokenSwap = null;

  const tokenRegex = new RegExp(`\\$(${currencySymbols.join("|")})`, "g");

  const tokenMatch = tokenRegex.exec(content);

  if (tokenMatch) {
    tokenSwap = {
      tokenName: tokenMatch[1],
      tokenURL: `/exchange?ticker=${tokenMatch[1]}&threadid=${threadId}`,
    };
  }

  const updatedContent = content.replace(
    tokenRegex,
    (_, token) =>
      `<a href="/exchange?ticker=${token}&threadid=${threadId}">$${token}</a>`,
  );
  return [updatedContent, tokenSwap];
}

// YouTube URL formats
// https://youtube.com/shorts/:id
// https://youtu.be/:id
// https://www.youtube.com/watch?v=:id
// https://www.youtube.com/live/:id?si=:si-id
function getYouTubeId(url: string) {
  // Regular expression to match different YouTube URL formats
  const pattern =
    /(?:youtu\.be\/|youtube\.com\/(?:watch\?v=|shorts\/|live\/))([^?&\/]+)/;

  // Search for matches using the regular expression
  const match = url.match(pattern);

  // If a match is found, return the ID
  if (match && match[1]) {
    return match[1];
  } else {
    return null;
  }
}

export function getBadgeImage(groupName: string): ImageSourcePropType | null {
  if (groupName === "OG Badge Arena") {
    return require("../../assets/badges/badge-type-1.png");
  }
  if (groupName === "Dokyo Badge Arena") {
    return require("../../assets/badges/badge-type-3.png");
  }
  if (groupName === "DeGods Badge Arena") {
    return require("../../assets/badges/badge-type-2.png");
  }
  if (groupName === "SappySeals Badge Arena") {
    return require("../../assets/badges/badge-type-4.png");
  }
  if (groupName === "Steady Badge Arena") {
    return require("../../assets/badges/badge-type-8.png");
  }
  if (groupName === "Gurs Badge Arena") {
    return require("../../assets/coins/gurs.png");
  }
  return null;  
}
