import React from "react";
import { Text, View } from "react-native";

import {
  BookmarkOutlineIcon,
  ChatBubblesOutlineIcon,
  HeartOutlineIcon,
  RepostOutlineIcon,
  TipOutlineIcon,
} from "@/components/icons";
import { Skeleton } from "@/components/ui/skeleton";

export const PostLoadingSkeleton = () => {
  return (
    <View className="flex w-full flex-row space-x-3 border-b border-dark-gray p-6">
      <View className="flex-shrink-0">
        <Skeleton circle width={42} height={42} />
      </View>
      <View className="flex-1 flex-col space-y-2">
        <View className="flex flex-row items-center">
          <Skeleton width={96} height={16} />
          <Text className="text-gray-text">・</Text>
          <Skeleton width={64} height={16} />
          <Text className="text-gray-text">・</Text>
          <Skeleton width={64} height={16} />
        </View>
        <View className="flex flex-col text-sm">
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
        </View>

        <View className="mt-1 w-full flex-row items-center space-x-4">
          <View className="flex-row space-x-1 items-center">
            <ChatBubblesOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton width={20} height={12} />
          </View>

          <View className="flex-row space-x-1 items-center">
            <RepostOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton width={20} height={12} />
          </View>

          <View className="flex-row space-x-1 items-center">
            <HeartOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton width={20} height={12} />
          </View>

          <View className="flex-row space-x-1 items-center">
            <BookmarkOutlineIcon className="h-5 w-5 text-gray-text" />
            <Skeleton width={20} height={12} />
          </View>
          <View className="items-center rounded">
            <TipOutlineIcon className="h-5 w-5 text-gray-text" />
          </View>
        </View>
      </View>
    </View>
  );
};

export const MainPostLoadingSkeleton = () => {
  return (
    <View className="border-b border-dark-gray p-6">
      <View className="flex w-full flex-col space-y-3">
        <View className="w-full flex-row space-x-3 items-center">
          <View className="flex-shrink-0">
            <Skeleton circle width={42} height={42} />
          </View>
          <View className="flex-1 flex-row text-sm leading-4 text-[#878787]">
            <Skeleton width={96} height={16} />
            <Text className="text-gray-text">・</Text>
            <Skeleton width={64} height={16} />
            <Text className="text-sm text-gray-text">・</Text>
            <Skeleton width={64} height={16} />
          </View>
        </View>
        <View className="flex flex-col">
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
          <Skeleton width="100%" height={14} style={{ marginBottom: 4 }} />
        </View>

        <View className="mt-1 flex flex-row items-center">
          <View className="flex flex-row items-center mr-4">
            <ChatBubblesOutlineIcon className="h-5 w-5 text-gray-text" />
            <View className="w-1" />
            <Skeleton width={24} height={12} />
          </View>

          <View className="flex flex-row items-center mr-4">
            <RepostOutlineIcon className="h-5 w-5 text-gray-text" />
            <View className="w-1" />
            <Skeleton width={24} height={12} />
          </View>

          <View className="flex flex-row items-center mr-4">
            <HeartOutlineIcon className="h-5 w-5 text-gray-text" />
            <View className="w-1" />
            <Skeleton width={24} height={12} />
          </View>

          <View className="flex flex-row items-center mr-4">
            <BookmarkOutlineIcon className="h-5 w-5 text-gray-text" />
            <View className="w-1" />
            <Skeleton width={24} height={12} />
          </View>

          <View className="ml-auto flex flex-row items-center rounded">
            <TipOutlineIcon className="h-5 w-5 text-gray-text" />
          </View>
        </View>
      </View>
    </View>
  );
};
