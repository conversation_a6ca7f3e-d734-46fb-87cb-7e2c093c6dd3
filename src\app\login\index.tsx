import ArenaLogo from '@/components/ArenaLogo';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useBottomSheet } from '@/components/ui/BottomSheetContext';
import { Colors } from '@/constants/Colors';
import { dynamicClient } from '@/dynamicClient';
import React from 'react';
import { Dimensions, Image, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import LoginWithEmail from './components/LoginWithEmail';
import XLoginButton from './components/XLoginButton';
import { useDynamicAuth } from './hooks/useDynamicAuth';
import LoginEmailModal from './views/login-email-model';

const { height } = Dimensions.get('window');

export default function LoginPage() {
  const [shouldLogin, setShouldLogin] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  useDynamicAuth(shouldLogin, setIsLoading);

  const { openBottomSheet, closeBottomSheet } = useBottomSheet();

  const handleLoginWithEmailPress = () => {
    openBottomSheet(
      <LoginEmailModal closeBottomSheet={closeBottomSheet} />,
      300,
    );
  };

  React.useEffect(() => {
    // Start the login process only after dynamicClient is initialized
    setShouldLogin(false);
  }, []);

  const login = async () => {
    if (isLoading) return;
    setIsLoading(true);
    dynamicClient.auth.social
      .connect({ provider: 'twitter' })
      .then(() => {
        setShouldLogin(true);
      })
      .catch(err => {
        console.error(err);
      });
  };

  return (
    <SafeAreaView>
      <ThemedView style={styles.head}>
        <Image source={require('@assets/images/hero1.png')} />
        <ThemedText style={styles.arenaLogo}>
          <ArenaLogo />
        </ThemedText>
        <Image source={require('@assets/images/hero2.png')} />
      </ThemedView>
      <View style={styles.body}>
        <ThemedText style={styles.taglinePrimary}>
          A Next Gen SocialFi Experience
        </ThemedText>
        <ThemedText style={styles.taglineSecondary}>Connect.</ThemedText>
        <ThemedText style={styles.taglineSecondary}>Engage.</ThemedText>
        <ThemedText style={styles.taglineSecondary}>Monetize.</ThemedText>
        <XLoginButton
          onPress={login}
          style={styles.XLoginButton}
          isLoading={isLoading}
        />
        <LoginWithEmail onPress={handleLoginWithEmailPress} />
        <ThemedText style={styles.footerText}>
          By clicking on "Enter The Arena With X" {'\n'}
          you agree to our terms of use and privacy policy.
        </ThemedText>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  head: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: height / 2.3,
  },
  arenaLogo: {
    marginBottom: -9,
  },
  body: {
    alignItems: 'center',
    paddingVertical: 30,
  },
  taglinePrimary: {
    color: Colors.dark.lightgrey,
    marginBottom: 15,
  },
  taglineSecondary: {
    fontSize: 32,
  },
  footerText: {
    color: '#808080',
    textAlign: 'center',
    fontSize: 11,
  },
  XLoginButton: {
    marginVertical: 25,
    marginBottom: 20,
  },
});
