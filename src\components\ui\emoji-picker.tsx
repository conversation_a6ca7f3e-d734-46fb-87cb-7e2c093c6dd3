import { Colors } from "@/constants/Colors"
import EmojiPicker, { type EmojiType } from 'rn-emoji-keyboard'

type TEmojiSelector = {
    isEmojiKeyboardOpen: boolean;
    setIsEmojiKeyboardOpen: (isEmojiKeyboardOpen: boolean) => void;
    onEmojiSelected: (emoji: EmojiType) => void;
}

export const EmojiSelector = (
    {
        isEmojiKeyboardOpen,
        setIsEmojiKeyboardOpen,
        onEmojiSelected
    }: TEmojiSelector) => {
    return (
        <EmojiPicker
            onEmojiSelected={onEmojiSelected}
            open={isEmojiKeyboardOpen}
            onClose={() => setIsEmojiKeyboardOpen(false)}
            enableRecentlyUsed
            enableSearchBar
            theme={{
                knob: Colors.dark.grey,
                container: Colors.dark.secondaryBackground,
                header: Colors.dark.white,
                skinTonesContainer: Colors.dark.secondaryBackground,
                category: {
                    icon: Colors.dark.white,
                    iconActive: Colors.dark.brandOrange,
                    container: Colors.dark.secondaryBackgroundDark,
                    containerActive: Colors.dark.secondaryBackground,
                },
                search: {
                    background: Colors.dark.secondaryBackgroundDark,
                    text: Colors.dark.white,
                    placeholder: Colors.dark.offWhite,
                    icon: Colors.dark.white,
                }
            }}
        />
    )
}