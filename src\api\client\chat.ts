import { axios } from "@/lib/axios";
import {
  ChatMessagesResponse,
  ChatPinnedMessagesResponse,
  ConvervationByProfileResponse,
  GroupsResponse,
  HeaderGroupResponse,
  PinConversationRequest,
  PinMessageRequest,
  PostTippingPartyNotifs,
  SendMessageRequest,
} from "@/queries/types/chats";

interface GetConversationsParams {
  page: number;
  pageSize: number;
}

export const getConversations = async ({
  page,
  pageSize,
}: GetConversationsParams) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<GroupsResponse>(
    `/chat/conversations?${searchParams.toString()}`,
  );

  return response.data;
};

export const getDirectMessages = async ({
  page,
  pageSize,
}: GetConversationsParams) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
  });

  const response = await axios.get<GroupsResponse>(
    `/chat/direct-messages?${searchParams.toString()}`,
  );

  return response.data;
};

export const searchDMConversations = async ({
  page,
  pageSize,
  searchString,
}: GetSearchConversationsParams) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
    searchString,
  });

  const response = await axios.get<GroupsResponse>(
    `/chat/search-dms?${searchParams.toString()}`,
  );

  return response.data;
};

interface GetSearchConversationsParams {
  page: number;
  pageSize: number;
  searchString: string;
}

export const searchRoomConversations = async ({
  page,
  pageSize,
  searchString,
}: GetSearchConversationsParams) => {
  const searchParams = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString(),
    searchString,
  });

  const response = await axios.get<GroupsResponse>(
    `/chat/search-rooms?${searchParams.toString()}`,
  );

  return response.data;
};

interface GetConversationByProfileParams {
  userId: string;
}

export const getConversationByProfile = async ({
  userId,
}: GetConversationByProfileParams) => {
  const searchParams = new URLSearchParams({
    userId,
  });

  const response = await axios.get<ConvervationByProfileResponse>(
    `/chat/group/by/user?${searchParams.toString()}`,
  );
  return response.data;
};

interface GetGroupParams {
  groupId: string;
  twitterHandle: string;
}

export const getGroup = async ({ groupId, twitterHandle }: GetGroupParams) => {
  const searchParams = new URLSearchParams({
    groupId: groupId,
    twitterHandle: twitterHandle,
  });

  const response = await axios.get<HeaderGroupResponse>(
    `/chat/group?${searchParams.toString()}`,
  );
  return response.data;
};

interface GetMessagesBeforeParams {
  groupId: string;
  timeFrom?: number;
}

export const getMessagesBefore = async ({
  groupId,
  timeFrom,
}: GetMessagesBeforeParams) => {
  const searchParams = new URLSearchParams({
    groupId,
    ...(timeFrom ? { timeFrom: timeFrom.toString() } : {}),
  });

  const response = await axios.get<ChatMessagesResponse>(
    `/chat/messages/b?${searchParams.toString()}`,
  );
  return response.data;
};

interface GetMessagesAfterParams {
  groupId: string;
  timeFrom: number;
}

export const getMessagesAfter = async ({
  groupId,
  timeFrom,
}: GetMessagesAfterParams) => {
  const searchParams = new URLSearchParams({
    groupId,
    timeFrom: timeFrom.toString(),
  });

  const response = await axios.get<ChatMessagesResponse>(
    `/chat/messages/a?${searchParams.toString()}`,
  );
  return response.data;
};

export const postSendMessage = async (request: SendMessageRequest) => {
  const response = await axios.post(`/chat/message`, request);
  return response.data;
};

export const getUpdateStatus = async ({ userId }: { userId: string }) => {
  const searchParams = new URLSearchParams({
    userId,
  });

  const response = await axios.get(`/chat/status?${searchParams.toString()}`);
  return response.data;
};

export const postPinConversation = async ({
  groupId,
  isPinned,
}: PinConversationRequest) => {
  const response = await axios.post(`/chat/group/pin`, {
    groupId,
    isPinned,
  });
  return response.data;
};

interface RequestUserSearchData {
  searchString: string;
}

export const getRequestUsersSearch = async ({
  searchString,
}: RequestUserSearchData) => {
  const searchParams = new URLSearchParams({
    searchString,
  });

  const response = await axios.get(`/chat/requests?${searchParams.toString()}`);
  return response.data;
};

export const getSettings = async () => {
  const response = await axios.get(`/chat/settings`);
  return response.data;
};

export interface SetSettingsRequest {
  holders?: boolean;
  followers?: boolean;
}

export const setSettings = async (settings: SetSettingsRequest) => {
  const response = await axios.patch(`/chat/settings`, settings);
  return response.data;
};

export const leaveChat = async ({ groupId }: { groupId: string }) => {
  const searchParams = new URLSearchParams({
    groupId,
  });

  const response = await axios.get(
    `/chat/leave-chat?${searchParams.toString()}`,
  );
  return response.data;
};

export const acceptChat = async ({ groupId }: { groupId: string }) => {
  const searchParams = new URLSearchParams({
    groupId,
  });

  const response = await axios.get(
    `/chat/accept-chat?${searchParams.toString()}`,
  );
  return response.data;
};

export interface ReactToMessageData {
  messageId: string;
  groupId: string;
  reaction: string;
}

export const reactToMessage = async ({
  messageId,
  groupId,
  reaction,
}: ReactToMessageData) => {
  const response = await axios.post(`/chat/react`, {
    messageId,
    groupId,
    reaction,
  });

  return response.data;
};

export interface UnreactToMessageData {
  messageId: string;
  groupId: string;
}

export const unreactToMessage = async ({
  messageId,
  groupId,
}: UnreactToMessageData) => {
  const response = await axios.post(`/chat/unreact`, {
    messageId,
    groupId,
  });

  return response.data;
};

export const getMessagesAround = async ({
  groupId,
  messageId,
}: {
  groupId: string;
  messageId: string;
}) => {
  const response = await axios.get(
    `/chat/messages/around/${groupId}/${messageId}`,
  );

  return response.data;
};

export const postPinMessage = async ({
  groupId,
  messageId,
  isPinned,
}: PinMessageRequest) => {
  const response = await axios.post(`/chat/message/pin`, {
    groupId: groupId,
    messageId: messageId,
    isPinned: isPinned,
  });
  return response.data;
};

export const getPinnedMessages = async ({ groupId }: { groupId: string }) => {
  const response = await axios.get<ChatPinnedMessagesResponse>(
    `/chat/messages/pinned/${groupId}`,
  );
  return response.data;
};

export const postTippingPartyNotify = async ({
  currency,
  txHash,
  txData,
}: PostTippingPartyNotifs) => {
  const response = await axios.post("chat/tipping-party-notification", {
    currency,
    txHash,
    txData,
  });
  return response.data;
};
