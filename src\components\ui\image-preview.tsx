import { Gesture, GestureDetector, GestureHandlerRootView } from "react-native-gesture-handler";
import { Modal, View, StyleSheet } from "react-native";
import { Colors } from "@/constants/Colors";
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from "react-native-reanimated";
import { useMemo } from "react";
import BackButton from "../navigation/BackButton";
import { ThemedText } from "../ThemedText";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from 'expo-image';
import { isAnimatedGif } from "@/utils";

type TImagePreview = {
    visible: boolean;
    onRequestClose: () => void;
    imageUrl: string;
    presentationStyle?: "overFullScreen" | "fullScreen" | "pageSheet" | "formSheet";
    animationType?: "none" | "slide" | "fade";
    backgroundColor?: string;
    title?: string;
}

const DEFAULT_ANIMATION_TYPE = "fade";
const DEFAULT_BG_COLOR = Colors.dark.background;
const DEFAULT_PRESENTATION_STYLE = "overFullScreen";
const AnimatedImage = Animated.createAnimatedComponent(Image);

const MAX_SCALE = 2;
const MIN_SCALE = 1;

export const ImagePreview = (
    {
        visible,
        onRequestClose,
        presentationStyle = DEFAULT_PRESENTATION_STYLE,
        animationType = DEFAULT_ANIMATION_TYPE,
        backgroundColor = DEFAULT_BG_COLOR,
        imageUrl,
        title
    }: TImagePreview) => {

    const scale = useSharedValue(1);
    const savedScale = useSharedValue(1);
    const offset = useSharedValue({ x: 0, y: 0 });
    const start = useSharedValue({ x: 0, y: 0 });

    const pinchGesture = useMemo(
        () =>
            Gesture.Pinch()
                .onUpdate(e => {
                    scale.value = savedScale.value * e.scale;
                })
                .onEnd(() => {
                    savedScale.value = scale.value;
                    if (savedScale.value < MIN_SCALE) {
                        //do not allow zooming out below original/min scale
                        scale.value = withSpring(MIN_SCALE, {
                            stiffness: 60,
                            overshootClamping: true,
                        });
                        savedScale.value = MIN_SCALE;
                    } else if (savedScale.value > MAX_SCALE) {
                        //do not allow zooming beyond max scale
                        scale.value = withSpring(MAX_SCALE, {
                            stiffness: 60,
                            overshootClamping: true,
                        });
                        savedScale.value = MAX_SCALE;
                    }
                }),
        [scale, savedScale],
    );

    const panGesture = useMemo(
        () =>
            Gesture.Pan()
                .averageTouches(true)
                .enabled(true)
                .onUpdate(e => {
                    offset.value = {
                        x: e.translationX + start.value.x,
                        y: e.translationY + start.value.y,
                    };
                })
                .onEnd(() => {
                    //if user take off finger while moving image in x and y direction, take image to its original place.
                    offset.value = {
                        x: withSpring(0, {
                            stiffness: 60,
                            overshootClamping: true,
                        }),
                        y: withSpring(0, {
                            stiffness: 60,
                            overshootClamping: true,
                        }),
                    };
                    start.value = {
                        x: 0,
                        y: 0,
                    };
                }),
        [offset, start],
    );

    const composedGesture = Gesture.Simultaneous(pinchGesture, panGesture);

    const animatedStyle = useAnimatedStyle(() => ({
        //change UI styles in UI thread.
        //this callback will be directly converted to worklet
        transform: [
            { scale: scale.value },
            { translateX: offset.value.x },
            { translateY: offset.value.y },
        ],
    }));

    return (
        <Modal
            transparent={presentationStyle === "overFullScreen"}
            visible={visible}
            presentationStyle={presentationStyle}
            animationType={animationType}
            onRequestClose={onRequestClose}
            supportedOrientations={["portrait"]}
            hardwareAccelerated
        >
            <GestureHandlerRootView style={{ flex: 1 }}>
                <SafeAreaView style={{ flex: 1 }}>
                    <View style={[styles.container, { backgroundColor }]}>
                        <View style={styles.header}>
                            <BackButton onPress={onRequestClose} />
                            {
                                title ? (
                                    <ThemedText style={{ fontSize: 16, fontWeight: 'bold', marginLeft: 10 }}>{title}</ThemedText>
                                ) : null
                            }
                        </View>
                        <GestureDetector gesture={composedGesture}>
                            <AnimatedImage
                                source={{ uri: imageUrl, isAnimated: isAnimatedGif(imageUrl) }}
                                style={[animatedStyle, styles.image]}
                                contentFit="contain"
                            />
                        </GestureDetector>
                    </View>
                </SafeAreaView>
            </GestureHandlerRootView>
        </Modal>
    )
}

export const styles = StyleSheet.create({
    container: {
        flex: 1,
        height: '100%',
        width: '100%',
    },
    image: {
        height: '90%',
        width: '100%',
        alignSelf: 'center',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
    }
})