import { useCallback, useEffect, useState } from "react";
import io, { Socket } from "socket.io-client";

import { env } from "@/env";
import { useUser } from "@/stores";

export function useSocket() {
  const { token, user } = useUser();
  const [socket, setSocket] = useState<Socket | null>(null);

  const setSeen = useCallback((groupId: string, date: number): void => {
    socket?.emit("set-conv-seen", { groupId, date });
  }, [socket])

  useEffect(() => {
    const socketIo = io(env.EXPO_PUBLIC_SOCKET_URL, {
      auth: {
        token,
      },
      forceNew: true,
      transports: ["websocket", "polling"],
      reconnection: true,
      reconnectionAttempts: 10,
      upgrade: true,
    });

    socketIo.on("error", (error) => {
      console.error("Socket error: ", error);
    });

    socketIo.on("connect", () => {
      console.log("Socket connected");
      setSocket(socketIo);
    });

    socketIo.on("disconnect", () => {
      console.log("Socket disconnected");
      setSocket(null);
    });

    socketIo.connect();
    if (user && token) {
      socketIo?.emit("subscribe", `user-${user.id}`);
    }

    return function () {
      socketIo.disconnect();
    };
  }, [token, user]);

  return { socket, setSeen };
}
