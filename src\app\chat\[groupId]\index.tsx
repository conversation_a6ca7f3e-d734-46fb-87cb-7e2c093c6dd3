import {
  Keyboard,
  LayoutChangeEvent,
  StyleSheet,
  View,
} from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import ChatHeader from "./_components/chat-header";
import ChatFooter from "./_components/chat-footer";
import { GroupProvider, useGroup } from "./_contexts/group-context";
import AccountSuspended from "./_components/account-suspended";
import ChatRoom from "./_components/chat-room";
import { Nullable } from "@/types/common";
import { Colors } from "@/constants/Colors";
import AddAttachmentContainer from "./_components/add-attachment-container";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import PinnedMessages from "./_components/pinned-messages";
import { useUser } from "@/stores";

const ChatConversation = () => {
  const [headerHeight, setHeaderHeight] = useState<Nullable<number>>(null);
  const [footerHeight, setFooterHeight] = useState<Nullable<number>>(null);
  const { top, bottom } = useSafeAreaInsets();
  const { setIsAttachImageOpen, isAttachImageOpen, data } = useGroup();
  const { group } = data || {};
  const { user } = useUser();

  const onHeaderLayout = useCallback((event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    setHeaderHeight(height);
  }, []);

  const onFooterLayout = useCallback((event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    setFooterHeight(height);
  }, []);

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setIsAttachImageOpen?.(false);
    });
    return () => {
      showSubscription.remove();
    };
  }, []);

  useEffect(() => {
    if (isAttachImageOpen) {
      Keyboard.dismiss()
    }
  }, [isAttachImageOpen])

  if (group?.isDirect && group?.chatMateId !== user?.id && group?.ownerUserId !== user?.id) {
    //user is not part of the dm group, so we don't render the chat
    return null;
  }

  return (
    <View
      style={[styles.container, {
        marginTop: top,
        marginBottom: bottom,
      }]}>
      <ChatHeader onLayout={onHeaderLayout} />
      <ChatRoom footerHeight={footerHeight} />
      <AccountSuspended headerHeight={headerHeight} />
      <PinnedMessages headerHeight={headerHeight} />
      <AddAttachmentContainer footerHeight={footerHeight} />
      <ChatFooter onLayout={onFooterLayout} />
    </View>
  )
}

const ChatContainer = () => {
  return (
    <GroupProvider>
      <ChatConversation />
    </GroupProvider>
  );
};

export default ChatContainer;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.dark.background,
  },
  text: {
    fontSize: 24,
    fontWeight: "600",
  },
  conversationBox: {
    marginTop: 60,
    marginBottom: 67,
    flex: 1,
    position: "relative",
    paddingHorizontal: 10,
    gap: 7,
  },
  square: {
    width: "60%",
    height: 100,
    margin: 5,
  },
});
