import { getCookie, set<PERSON>ookie } from '@/cookies/secure-store';
import { env } from '@/env';
import { axios } from '@/lib/axios';
import { OAuth2SchemaType } from '@/schemas';

export async function login({ code, state }: OAuth2SchemaType) {
  try {
    const ref = await getCookie('__ref');

    const queryParams = new URLSearchParams({
      authCode: code,
      authState: state,
      referrer: ref?.value || '',
      callbackUrl: `${env.EXPO_PUBLIC_APP_DOMAIN}twitter/auth`,
    });

    const { data } = await axios.get(
      `/twitter/callback?${queryParams.toString()}`,
    );

    if (data.errorCode) {
      throw data;
    }

    await axios.post(`/term-of-use/approve`, undefined, {
      headers: {
        Authorization: `Bearer ${data.token}`,
      },
    });

    // 30 days
    const expires = new Date(Date.now() + 30 * 1000 * 60 * 60 * 24);

    await set<PERSON>ookie('token', data.token, expires);

    await setCookie(
      'user',
      JSON.stringify({
        ...data.user,
        loggedInAt: new Date(),
      }),
      expires,
    );

    await setCookie('twitterUser', JSON.stringify(data.twitterUser), expires);

    //Set the Bearer Token here, after login, the _layout file will not run
    if (data.token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${data.token}`;
    }

    return { data };
  } catch (error: any) {
    return { error: error.message };
  }
}
