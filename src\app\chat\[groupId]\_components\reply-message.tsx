import { StyleSheet, Image } from "react-native"
import { ThemedText } from "@/components/ThemedText"
import { Colors } from "@/constants/Colors"
import { View } from "react-native"
import { GroupMessageTypeEnum } from "@/queries/types"
import { memo, useMemo } from "react"
import { ImageOutlineIcon } from "@/components/icons"
import { stringToColor } from "@/utils/string-formatter"

type ReplyMessageProps = {
    messageToReplyToUserId: string,
    messageToReplyToUserName: string,
    messageToReplyToMessage: string,
    userId: string | null
    type: "chat-footer" | "chat-room"
    isMyMessage?: boolean
    attachmentType?: GroupMessageTypeEnum
    attachmentUrl?: string
}

const ReplyMessage =
    ({
        messageToReplyToUserId,
        messageToReplyToUserName,
        messageToReplyToMessage,
        userId,
        type,
        isMyMessage,
        attachmentType,
        attachmentUrl
    }: ReplyMessageProps) => {

        const isImage = useMemo(() => attachmentType === GroupMessageTypeEnum.Image, [attachmentType])
        const isVideo = useMemo(() => attachmentType === GroupMessageTypeEnum.Video, [attachmentType])

        return (
            <View
                style={[styles.replyContainer, {
                    backgroundColor: type === "chat-footer" ? Colors.dark.darkGrey : isMyMessage ? Colors.dark.brandOrangeDark : Colors.dark.darkGrey,
                    borderLeftColor: type === "chat-footer" ? Colors.dark.grey : isMyMessage ? Colors.dark.brandOrangeLight : Colors.dark.grey
                }]}
            >
                <View style={styles.leftContainer}>
                    <View style={styles.userNameContainer}>
                        <ThemedText type="bold" style={[styles.usernameText, { color: stringToColor(messageToReplyToUserId) }]} numberOfLines={1}>
                            {messageToReplyToUserId === userId ? "You" : messageToReplyToUserName}
                        </ThemedText>
                    </View>
                    <View style={styles.messageContainer}>
                        {
                            isImage ? (
                                <ImageOutlineIcon
                                    width={15}
                                    height={15}
                                    color={type === "chat-footer" ? "rgba(157, 157, 157, 1)" : "rgba(237, 237, 237, 1)"} />
                            ) : null
                        }
                        <ThemedText style={[styles.messageText, { marginLeft: isImage ? 5 : 0 }]} type={type === "chat-footer" ? "defaultGrey" : "default"} numberOfLines={3}>
                            {messageToReplyToMessage ? messageToReplyToMessage : isImage ? "Image" : isVideo ? "Video" : ''}
                        </ThemedText>
                    </View>
                </View>
                <View style={styles.rightContainer}>
                    {
                        isImage ? (
                            <Image
                                source={{ uri: attachmentUrl }}
                                style={{ flex: 1, width: '100%', height: '100%' }}
                                resizeMode='cover'
                            />
                        ) : null
                    }
                </View>
            </View>
        )
    }

export default memo(ReplyMessage, (prevProps, nextProps) => {
    return prevProps.messageToReplyToUserId === nextProps.messageToReplyToUserId &&
        prevProps.messageToReplyToUserName === nextProps.messageToReplyToUserName &&
        prevProps.messageToReplyToMessage === nextProps.messageToReplyToMessage &&
        prevProps.type === nextProps.type &&
        prevProps.userId === nextProps.userId &&
        prevProps.isMyMessage === nextProps.isMyMessage &&
        prevProps.attachmentType === nextProps.attachmentType &&
        prevProps.attachmentUrl === nextProps.attachmentUrl
})

const styles = StyleSheet.create({
    replyContainer: {
        width: '100%',
        paddingHorizontal: 10,
        paddingVertical: 5,
        borderRadius: 10,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderLeftWidth: 3,
    },
    userNameContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    usernameText: {
        fontSize: 12,
    },
    messageText: {
        fontSize: 12
    },
    messageContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    leftContainer: {
        width: '85%',
        gap: 7
    },
    rightContainer: {
        height: '100%',
        width: '15%',
        alignItems: 'flex-end',
    }
})