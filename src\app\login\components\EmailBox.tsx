import { ThemedText } from '@/components/ThemedText';
import React, { memo } from 'react';
import {
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

interface EmailBoxProps {
  email: string;
  setEmail: (email: string) => void;
  handleEmailContinue: () => Promise<void>;
}

const EmailBox: React.FC<EmailBoxProps> = ({
  email,
  setEmail,
  handleEmailContinue,
}) => {
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (email: string) => {
    setEmail(email.toLocaleLowerCase());
  };
  return (
    <View style={styles.container}>
      <ThemedText style={styles.title}>Log-in using recovery email</ThemedText>
      <ThemedText style={styles.description}>
        Please enter the email you set for recovery.
      </ThemedText>
      <TextInput
        style={styles.input}
        placeholder="<EMAIL>"
        placeholderTextColor="#B5B5B5"
        value={email}
        onChangeText={handleEmailChange}
        keyboardType="email-address"
      />
      <TouchableOpacity
        style={[styles.button, { opacity: isValidEmail(email) ? 1 : 0.5 }]}
        disabled={!isValidEmail(email)}
        onPress={handleEmailContinue}
      >
        <Text style={styles.buttonText}>Continue</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    backgroundColor: '#0F0F0FE6',
    width: '90%',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#F4F4F4',
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    color: '#B5B5B5',
    marginBottom: 4,
  },
  input: {
    height: 44,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#808080',
    backgroundColor: '#1A1A1A',
    paddingHorizontal: 24,
    color: '#F4F4F4',
    fontSize: 14,
    marginTop: 20,
    marginBottom: 20,
  },
  button: {
    height: 44,
    borderRadius: 40,
    backgroundColor: '#D64C05',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 60,
  },
  buttonText: {
    color: '#F4F4F4',
    fontSize: 14,
    fontWeight: '600',
  },
});

export default memo(EmailBox);
