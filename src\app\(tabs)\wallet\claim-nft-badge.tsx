import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Button } from "@/components/ui/button";
import { Colors } from "@/constants/Colors";
import { useFocusEffect, useNavigation } from "expo-router";
import { Image, ScrollView, StyleSheet } from "react-native";

const ClaimNftBadge = () => {
  const navigation = useNavigation();

  useFocusEffect(() => {
    navigation.setOptions({ tabBarStyle: { display: "none" } });

    return () => navigation.setOptions({ tabBarStyle: undefined });
  });

  const isFailed = false; //Request failed
  const isSuccess = false; //Request success
  const claimCompleted = false; //Claim completed after successfull request

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.imageContainer}>
        <Image
          style={styles.image}
          source={require("@assets/images/claim-nft-badge-profile.png")}
        />
      </ThemedView>
      <ThemedView style={styles.textContainer}>
        <ThemedText type="default">
          {isSuccess
            ? "Steady NFT found"
            : claimCompleted
            ? "Congratulations!!"
            : "Connect the Avalanche wallet that holds your Steady NFT"}
        </ThemedText>
        <ThemedView
          style={[styles.greyTextGroup, isFailed && styles.failedBoxGap]}
        >
          <ThemedText type="defaultGrey">
            {isSuccess
              ? "Press the mint button to claim the badge."
              : isFailed
              ? "The wallet you provided doesn’t hold any Steady NFTs: Please connect a wallet that holds at least one Steady NFT and try again."
              : claimCompleted
              ? "You have claimed the Steady Badge. From now on, you will see it next to your username in your profile."
              : "Please click the connect button where you will be asked to sign a message to prove that you are the rightful owner of the NFT."}
          </ThemedText>
          {isSuccess ? null : isFailed ? (
            <ThemedView style={styles.errorAddressHolder}>
              <ThemedText style={styles.errorAddressText}>
                ******************************************
              </ThemedText>
            </ThemedView>
          ) : claimCompleted ? null : (
            <ThemedText type="defaultGrey">
              Only Metamask and Phantom wallets are supported. After signing the
              message, you will receive a special badge on your Arena profile.
            </ThemedText>
          )}
        </ThemedView>
      </ThemedView>

      <Button
      >
        {
          isSuccess
            ? "Mint"
            : isFailed
            ? "Retry"
            : claimCompleted
            ? "Back to profile"
            : "Connect"
        }
        </Button>
    </ThemedView>
  );
};

export default ClaimNftBadge;

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 24,
    flex: 1,
    gap: 24,
  },
  imageContainer: {
    backgroundColor: Colors.dark.greyBackground,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 66,
    paddingHorizontal: 74,
    borderRadius: 10,
  },
  image: {
    width: "100%",
    aspectRatio: 1,
  },
  textContainer: {
    gap: 8,
    flexGrow: 1,
  },
  greyTextGroup: {
    gap: 15,
  },
  errorAddressHolder: {
    borderWidth: 1,
    borderColor: "rgba(221, 9, 9, 1)",
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  errorAddressText: {
    fontSize: 12,
    fontWeight: "400",
    color: "rgba(221, 9, 9, 1)",
    fontFamily: "InterRegular",
  },
  failedBoxGap: {
    gap: 24,
  },
});
