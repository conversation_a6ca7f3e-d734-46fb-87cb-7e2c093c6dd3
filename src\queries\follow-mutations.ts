import {
  DefaultError,
  MutationOptions,
  useMutation,
} from "@tanstack/react-query";

import { postFollowUser, postUnfollowUser } from "@/api/client/follow";

import { FollowUserData } from "./types";

type FollowMutationType = MutationOptions<
  unknown,
  DefaultError,
  FollowUserData,
  any
>;

export const useFollowMutation = (options?: FollowMutationType) => {
  return useMutation({
    mutationFn: postFollowUser,
    ...options,
  });
};

export const useUnfollowMutation = (options?: FollowMutationType) => {
  return useMutation({
    mutationFn: postUnfollowUser,
    ...options,
  });
};
