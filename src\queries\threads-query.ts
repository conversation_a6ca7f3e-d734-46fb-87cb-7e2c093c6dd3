import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import {
  getAnswersByThread,
  getBookmarks,
  getNestedAnswersByThread,
  getThreadById,
  getThreads,
  getThreadsByUser,
  getThreadsGIFs,
  getTrendingThreads,
  getUploadedSize,
} from "@/api/client/threads";

export const useThreadsInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["home", "threads", "my-feed"],
    queryFn: ({ pageParam }) => {
      return getThreads(pageParam);
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.count;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.threads.length;
      }, 0);

      if (
        total !== currentLength &&
        (total > currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    staleTime: 60 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
};

export const useTrendingThreadsInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["home", "threads", "trending-feed"],
    queryFn: ({ pageParam }) => {
      return getTrendingThreads(pageParam);
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.count;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.threads.length;
      }, 0);

      if (
        total !== currentLength &&
        (total > currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    staleTime: 60 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
};

export const useBookmarksInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["threads", "bookmarks"],
    queryFn: ({ pageParam }) => {
      return getBookmarks(pageParam);
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.count;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.threads.length;
      }, 0);
      const lastPageLength = pages[pages.length - 1].threads.length;
      const isLastPageLengthLessThanPageSize =
        lastPageLength < lastPageParam.pageSize;

      if (
        !isLastPageLengthLessThanPageSize &&
        total !== currentLength &&
        (total > currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    refetchOnMount: "always",
  });
};

export const useThreadsByUserIdInfiniteQuery = (userId: string) => {
  return useInfiniteQuery({
    queryKey: ["threads", "user", userId],
    queryFn: ({ pageParam }) => {
      return getThreadsByUser({
        userId,
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      });
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.count;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.threads.length;
      }, 0);

      if (
        total !== currentLength &&
        (total > currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const useThreadByIdQuery = (threadId: string) => {
  return useQuery({
    queryKey: ["threads", threadId],
    queryFn: () => getThreadById(threadId),
  });
};

export const useThreadAnswersInfiniteQuery = (threadId: string) => {
  return useInfiniteQuery({
    queryKey: ["threads", "answers", threadId],
    queryFn: ({ pageParam }) => {
      return getAnswersByThread({ threadId, ...pageParam });
    },
    initialPageParam: {
      page: 1,
      pageSize: 12,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.count;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.threads.length;
      }, 0);

      if (
        total !== currentLength &&
        (total > currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
    refetchOnMount: "always",
  });
};
export const useThreadNestedAnswersInfiniteQuery = (threadId?: string) => {
  return useInfiniteQuery({
    queryKey: ["threads", "nested", threadId],
    queryFn: ({ pageParam }) => {
      if (!threadId) return null;
      return getNestedAnswersByThread({ threadId, ...pageParam });
    },
    initialPageParam: {
      page: 1,
      pageSize: 50,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage) {
        const total = lastPage.count;
        const currentLength = pages.reduce((prev, curr) => {
          if (!curr) return prev;

          return prev + curr.threads.length;
        }, 0);

        if (
          total !== currentLength &&
          (total >= currentLength + lastPageParam.pageSize ||
            (total < currentLength + lastPageParam.pageSize &&
              total - currentLength < lastPageParam.pageSize))
        ) {
          return { ...lastPageParam, page: lastPageParam.page + 1 };
        }
      }

      return undefined;
    },
    refetchOnMount: "always",
    enabled: Boolean(threadId),
  });
};

export const useGIFsQuery = (search?: string) => {
  return useQuery({
    queryKey: ["threads", "gifs", search],
    queryFn: () => getThreadsGIFs(search),
  });
};

export const useTotalUploadedQuery = () => {
  return useQuery({
    queryKey: ["threads", "uploads"],
    queryFn: () => getUploadedSize(),
  });
};
