import { useEffect } from "react";
import { clamp, useMotionValue, useScroll, useTransform } from "framer-motion";

export const useBoundedScroll = (threshold: number) => {
  const { scrollY } = useScroll();
  const scrollYBounded = useMotionValue(0);
  const scrollYBoundedProgress = useTransform(
    scrollYBounded,
    [0, threshold],
    [0, 1],
  );

  useEffect(() => {
    return scrollY.on("change", (current) => {
      if (current < 0) return;

      const previous = scrollY.getPrevious();
      if (!previous) return;

      const diff = current - previous;
      const newScrollYBounded = scrollYBounded.get() + diff;

      scrollYBounded.set(clamp(newScrollYBounded, 0, threshold));
    });
  }, [threshold, scrollY, scrollYBounded]);

  return { scrollYBounded, scrollYBoundedProgress };
};
