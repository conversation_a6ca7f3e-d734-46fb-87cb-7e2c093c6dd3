import { useMemo } from "react";

import { parseISO } from "date-fns";
import { Href, router } from "expo-router";
import { Pressable, StyleSheet, Text, View } from "react-native";

import { formatTimeDistance } from "@/utils";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useQueryClient } from "@tanstack/react-query";
import { NotificationType } from "@/queries/types/notifications";
import { AtOutlineIcon, ChatBubblesOutlineIcon, HeartOutlineIcon, PersonAddOutlineIcon, RepostOutlineIcon, TicketOutlineIcon, TipOutlineIcon } from "@/components/icons";

interface NotificationProps extends NotificationType {}

export const NotificationItem = ({
  createdOn,
  title,
  text,
  isSeen,
  link,
}: NotificationProps) => {
  const queryClient = useQueryClient();
  const date = parseISO(createdOn);

  const isTipNotification =
    title.toLowerCase() === "You got tipped".toLowerCase();

  const Icon = useMemo(() => {
    if (title.toLowerCase() === "New Trade".toLowerCase())
      return TicketOutlineIcon;

    if (isTipNotification) return TipOutlineIcon;

    if (title.includes("replied:")) return ChatBubblesOutlineIcon;

    if (title.includes("liked")) return HeartOutlineIcon;

    if (title.includes("reposted") || title.includes("quoted"))
      return RepostOutlineIcon;

    if (title.includes("mentioned you")) return AtOutlineIcon;

    return PersonAddOutlineIcon;
  }, [isTipNotification, title]);

  const Container = ({ children }: { children: React.ReactNode }) => {
    const className =
      "relative flex-row items-center gap-[10px] px-6 py-4 hover:bg-[#212121] hover:bg-opacity-[0.2] select-none sm:select-auto";

    if (link) {
      return (
        <Pressable
          className={className}
          onPress={() => {
            queryClient.refetchQueries({
              queryKey: ["threads"],
            });
            router.push(link as Href<string>);
          }}
        >
          {children}
        </Pressable>
      );
    }

    return <div className={className}>{children}</div>;
  };

  return (
    <Container>
      <Icon className="h-6 w-6 flex-shrink-0 text-off-white" />
      <ThemedView className="flex-1 overflow-hidden">
        <ThemedText style={styles.interSemiBold} className="text-sm font-semibold leading-4 text-off-white">
          {isTipNotification ? text : title}
        </ThemedText>
        {text && !isTipNotification && (
          <ThemedText numberOfLines={1} ellipsizeMode="tail" className="mt-1 whitespace-nowrap text-sm leading-4 text-gray-text">
            {text}
          </ThemedText>
        )}
      </ThemedView>
      <ThemedView className="ml-2 flex w-14 flex-shrink-0 justify-end ">
        <ThemedText className="text-xs text-gray-text">{formatTimeDistance(date)}</ThemedText>
      </ThemedView>
      {!isSeen && (
        <ThemedView className="absolute left-[10px] h-1 w-1 rounded-full top-[75%] bg-[#EB540A]" />
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  interSemiBold: {
    fontFamily: "InterSemiBold",
  },
});
