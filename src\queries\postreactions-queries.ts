import { useInfiniteQuery } from "@tanstack/react-query";

import {
  getPostReactionsLikes,
  getPostReactionsQuotes,
  getPostReactionsReposts,
} from "@/api/client/postreactions";

export const usePostReactionsLikesInfiniteQuery = ({
  threadId,
}: {
  threadId: string;
}) => {
  return useInfiniteQuery({
    queryKey: ["postreactions", "likes", threadId],
    queryFn: ({ pageParam }) => {
      return getPostReactionsLikes({
        threadId,
        likePage: pageParam.page,
        likePerPage: pageParam.pageSize,
      });
    },
    initialPageParam: {
      page: 1,
      pageSize: 50,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.likeCount;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.likedUsers.length;
      }, 0);

      if (
        total !== currentLength &&
        lastPage.likedUsers.length !== 0 &&
        (total >= currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const usePostReactionsRepostsInfiniteQuery = ({
  threadId,
}: {
  threadId: string;
}) => {
  return useInfiniteQuery({
    queryKey: ["postreactions", "reposts", threadId],
    queryFn: ({ pageParam }) => {
      return getPostReactionsReposts({
        threadId,
        repostPage: pageParam.page,
        repostPerPage: pageParam.pageSize,
      });
    },
    initialPageParam: {
      page: 1,
      pageSize: 20,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.repostCount;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.repostedUsers.length;
      }, 0);

      if (
        total !== currentLength &&
        lastPage.repostedUsers.length !== 0 &&
        (total >= currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const usePostReactionsQuotesInfiniteQuery = ({
  threadId,
}: {
  threadId: string;
}) => {
  return useInfiniteQuery({
    queryKey: ["postreactions", "quotes", threadId],
    queryFn: ({ pageParam }) => {
      return getPostReactionsQuotes({
        threadId,
        quotePage: pageParam.page,
        quotePerPage: pageParam.pageSize,
      });
    },
    initialPageParam: {
      page: 1,
      pageSize: 50,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      const total = lastPage.quotesCount;
      const currentLength = pages.reduce((prev, curr) => {
        return prev + curr.quotes.length;
      }, 0);

      if (
        total !== currentLength &&
        lastPage.quotes.length !== 0 &&
        (total >= currentLength + lastPageParam.pageSize ||
          (total < currentLength + lastPageParam.pageSize &&
            total - currentLength < lastPageParam.pageSize))
      ) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};
