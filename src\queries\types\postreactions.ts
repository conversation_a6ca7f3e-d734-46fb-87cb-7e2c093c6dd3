import { Thread } from "@/types";

export type User = {
  ranking: number;
  threadCount: number;
  followerCount: number;
  followingsCount: number;
  twitterFollowers: number;
  id: string;
  createdOn: string;
  twitterId: string;
  twitterHandle: string;
  twitterName: string;
  twitterPicture: string;
  lastLoginTwitterPicture: string | null;
  bannerUrl: string | null;
  address: string;
  ethereumAddress: string | null;
  solanaAddress: string | null;
  prevAddress: string | null;
  addressConfirmed: boolean;
  twitterDescription: string;
  signedUp: boolean;
  subscriptionCurrency: string;
  subscriptionCurrencyAddress: string | null;
  subscriptionPrice: string;
  keyPrice: string;
  subscriptionsEnabled: boolean;
  userConfirmed: boolean;
  twitterConfirmed: boolean;
  flag: number;
  ixHandle: string;
  handle: string;
  isFollowing: boolean;
};

interface ParentThread {
  displayStatus: number;
  id: string;
  content: null | string;
  contentUrl: string;
  threadType: string;
  userId: string;
  userName: string;
  userHandle: string;
  userPicture: string;
  createdDate: string;
  answerCount: number;
  likeCount: number;
  bookmarkCount: number;
  repostCount: number;
  repostId: string;
  answerId: string;
  isDeleted: boolean;
  privacyType: number;
  answerPrivacyType: number;
  language: string;
  isPinned: boolean;
  paywall: boolean;
  price: string;
  tipAmount: number;
  tipCount: number;
  currency: string;
  currencyAddress: string;
  currencyDecimals: number;
  likes: {
    id: number;
    createdOn: string;
    threadId: string;
    userId: string;
  }[];
}

export interface PostReactionsLikesResponse {
  likedUsers: User[];
  likeCount: number;
  parentThread: ParentThread;
}

export interface PostReactionsRepostsResponse {
  repostedUsers: User[];
  repostCount: number;
  parentThread: ParentThread;
}

export interface PostReactionsQuotesResponse {
  parentQuote: Thread;
  quotes: Thread[];
  quotesCount: number;
}
