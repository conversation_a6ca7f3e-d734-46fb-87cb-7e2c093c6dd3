import {
    View,
    StyleSheet
} from "react-native";
import React, { memo } from "react";
import { Skeleton } from "@/components/ui/skaleton";


export const MessageSettingsSkeleton = memo(() => {
    return (
        <View style={styles.container}>
            <View style={styles.topRow}>
                <Skeleton width={"60%"} height={20} />
                <Skeleton width={"20%"} height={20} />
            </View>
            <Skeleton width={"60%"} height={20} />
            <Skeleton width={"50%"} height={15} style={styles.marginTop30} />
            <View style={styles.middleRow}>
                <Skeleton width={"70%"} height={15} style={styles.marginTop10} />
                <Skeleton width={"15%"} height={20} />
            </View>
            <Skeleton width={"50%"} height={15} style={styles.marginTop30} />
            <View style={styles.middleRow}>
                <Skeleton width={"70%"} height={15} style={styles.marginTop10} />
                <Skeleton width={"15%"} height={20} />
            </View>
        </View>
    );
});

const styles = StyleSheet.create({
    container: {
        paddingTop: 20,
        paddingHorizontal: 25,
    },
    topRow: {
        flexDirection: 'row',
        marginVertical: 10,
        justifyContent: 'space-between',
        marginBottom: 40,
        alignItems: 'center',
    },
    middleRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    marginTop30: {
        marginTop: 30,
    },
    marginTop10: {
        marginTop: 10,
    },
});