import { checkEmail } from '@/api/client/login';
import { toast } from '@/components/toast';
import { dynamicClient } from '@/dynamicClient';
import React, { useCallback, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import EmailBox from '../components/EmailBox';
import OTPBox from '../components/OTPBox';
import { useEmailAuth } from '../hooks/useEmailAuth';

export default function LoginEmailModal({
  closeBottomSheet,
}: {
  closeBottomSheet: () => void;
}) {
  const [email, setEmail] = useState('');
  const [showOTP, setShowOTP] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [emailToken, setEmailToken] = useState<string | null>(null);

  useEmailAuth(emailToken, closeBottomSheet);

  const handleEmailContinue = useCallback(async () => {
    try {
      const response = await checkEmail(email);
      if (response.isExists) {
        setShowOTP(true);
        toast.green('Verification code sent to your email');
      } else {
        toast.danger('Email is not registered');
      }
    } catch (error) {
      console.error('Error checking email:', error);
      toast.danger('Something went wrong. Please try again.');
    }
  }, [email, setShowOTP]);

  const onSendOTP = async () => {
    await dynamicClient.auth.email.sendOTP(email);
  };

  const handleOTPVerification = async (otp: string) => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      await dynamicClient.auth.email.verifyOTP(otp);
      const token = dynamicClient.auth.token;
      setEmailToken(token);
      toast.green('OTP verified successfully');
    } catch (error) {
      console.error('Error verifying OTP:', error);
      toast.danger('Invalid OTP. Please try again.');
      setIsLoading(false);
    }
  };

  const resendOTP = async () => {
    try {
      await dynamicClient.auth.email.resendOTP();
      toast.green('OTP resent successfully');
      console.log('OTP resent successfully');
    } catch (error) {
      console.error('Error resending OTP:', error);
    }
  };

  return (
    <View style={styles.overlay}>
      {showOTP ? (
        <OTPBox
          handleSendOTP={onSendOTP}
          handleResendOTP={resendOTP}
          handleOTPVerification={handleOTPVerification}
        />
      ) : (
        <EmailBox
          email={email}
          setEmail={setEmail}
          handleEmailContinue={handleEmailContinue}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#0F0F0FE6',
    alignItems: 'center',
  },
});
