import { Modal, TouchableOpacity, View } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { TipOutlineIcon } from "@/components/icons";
import { Button } from "@/components/ui/button";
import { Colors } from "@/constants/Colors";

interface TippingInfoModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

export const TippingInfoPopup = ({ isOpen, setIsOpen }: TippingInfoModalProps) => (
  <Modal transparent visible={isOpen} animationType="fade">
    <TouchableOpacity
      activeOpacity={1}
      style={{ flex: 1, justifyContent: 'flex-end', backgroundColor: 'rgba(0,0,0,0.5)' }}
      onPress={() => setIsOpen(false)}
    >
      <View
        style={{
          backgroundColor: Colors.dark.secondaryBackground,
          paddingVertical: 20,
          paddingHorizontal: 20,
          elevation: 5, 
          width: '91%',
          borderRadius: 16,
          borderColor: 'rgba(59,59,59,0.30)',
          borderWidth: 1,
          shadowColor: 'rgba(255,255,255,0.75)',
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.5,
          shadowRadius: 20,
          alignSelf: 'center',
          alignItems: 'center',
          bottom: 80,
        }}
        // Prevent closing when pressing the sheet itself
        onStartShouldSetResponder={() => true}
        onTouchEnd={e => e.stopPropagation()}
      >
        <TipOutlineIcon color={Colors.dark.offWhite} height={32} width={32} className="mb-2" />
        <ThemedText type="bold" className="text-xl text-off-white mb-2">
          Tipping Information
        </ThemedText>
        <ThemedText className="text-center text-base text-grey m-6">
          A 2% fee is deducted from every tip sent on the platform. This fee is withdrawn
          from the total tipped amount, ensuring that senders do not incur any extra charges.
        </ThemedText>
        <Button onPress={() => setIsOpen(false)} buttonStyle={{ width: '100%' }}>
          <ThemedText type="bold">Close</ThemedText>
        </Button>
      </View>
    </TouchableOpacity>
  </Modal>
);
