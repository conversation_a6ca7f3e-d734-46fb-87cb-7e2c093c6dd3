import React, { useEffect, useMemo, useRef } from "react";
import {
  View,
  StyleSheet,
  Animated,
  Easing,
  StyleProp,
  ViewStyle,
  DimensionValue,
} from "react-native";
import MaskedViewBase from "@react-native-masked-view/masked-view";
import { LinearGradient } from "expo-linear-gradient";
import { useColorScheme } from "nativewind";
import type { ComponentType } from "react";

const MaskedView = MaskedViewBase as unknown as ComponentType<{
  maskElement: JSX.Element;
  children?: React.ReactNode;
}>;

type SkeletonProps = {
  circle?: boolean;
  width: DimensionValue;
  height: DimensionValue;
  style?: StyleProp<ViewStyle>;
  borderRadius?: number;
};

export const Skeleton = React.memo(function SkeletonComponent({
  circle = false,
  width,
  height,
  style,
  borderRadius,
}: SkeletonProps) {
  const { colorScheme } = useColorScheme();
  const shimmerTranslate = useRef(new Animated.Value(-1)).current;

  const { baseColor, highlightColor } = useMemo(() => {
    return colorScheme === "dark"
      ? { baseColor: "#2f2f2f", highlightColor: "#3d3d3d" }
      : { baseColor: "#e1e1e1", highlightColor: "#f5f5f5" };
  }, [colorScheme]);

  useEffect(() => {
    Animated.loop(
      Animated.timing(shimmerTranslate, {
        toValue: 1,
        duration: 1300,
        easing: Easing.inOut(Easing.ease),
        useNativeDriver: true,
      })
    ).start();
  }, [shimmerTranslate]);

  const shimmerWidth = typeof width === "number" ? width * 1.2 : 200;

  const translateX = shimmerTranslate.interpolate({
    inputRange: [-1, 1],
    outputRange: [-shimmerWidth, shimmerWidth],
  });

  const resolvedRadius = circle ? 9999 : borderRadius ?? 4;

  return (
    <MaskedView
      maskElement={
        <View
          style={[
            {
              width,
              height,
              borderRadius: resolvedRadius,
              backgroundColor: "black",
            },
            style,
          ]}
        />
      }
    >
      <View
        style={[
          {
            width,
            height,
            backgroundColor: baseColor,
            borderRadius: resolvedRadius,
            overflow: "hidden",
          },
          style,
        ]}
      >
        <Animated.View
          style={[
            StyleSheet.absoluteFill,
            {
              transform: [{ translateX }],
            },
          ]}
        >
          <LinearGradient
            colors={[baseColor, highlightColor, baseColor]}
            start={[0, 0.5]}
            end={[1, 0.5]}
            style={{ flex: 1, width: shimmerWidth }}
          />
        </Animated.View>
      </View>
    </MaskedView>
  );
});
