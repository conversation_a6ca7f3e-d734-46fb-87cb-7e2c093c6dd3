import { useState } from "react";

import * as ImagePicker from "expo-image-picker";
import { useGlobalSearchParams } from "expo-router";
import {
  Image,
  Pressable,
  StyleProp,
  StyleSheet,
  View,
  ViewStyle,
} from "react-native";

import { User } from "@/types";
import { toast } from "@/components/toast";
import Avatar from "@/components/ui/avatar";
import { Colors } from "@/constants/Colors";
import { PencilOutlineIcon } from "@/components/icons";
import { useQueryClient } from "@tanstack/react-query";
import { useUserByHandleQuery } from "@/queries/user-queries";
import { useUpdateBannerMutation } from "@/queries/profile-mutations";
import { upload } from "@/utils";
import { v4 as uuid } from "uuid";

interface EditButtonStyleProps {
  containerStyle?: StyleProp<ViewStyle>;
  onPress?: () => void;
}

const EditButton: React.FC<EditButtonStyleProps> = ({
  containerStyle,
  onPress,
}) => {
  return (
    <Pressable onPress={onPress} style={containerStyle}>
      <PencilOutlineIcon width={24} height={24} color="white" />
    </Pressable>
  );
};

export const BannerPicture = () => {
  const queryClient = useQueryClient();
  const params = useGlobalSearchParams() as { userHandle: string };
  const [open, setOpen] = useState(false);
  const [previewURL, setPreviewURL] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);

  const { data, isLoading } = useUserByHandleQuery(params.userHandle);
  const { mutateAsync: updateBanner } = useUpdateBannerMutation({
    onSuccess: (data, { bannerUrl }) => {
      toast.green("Banner picture updated!");
      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (old: { user: User }) => {
          return {
            ...old,
            user: {
              ...old.user,
              bannerUrl,
            },
          };
        }
      );
      // resetUploading();
      setOpen(false);
    },
  });

  const handleChange = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    const selectedFile = result?.assets?.[0];

    if (selectedFile) {
      if (selectedFile?.fileSize && selectedFile?.fileSize > 5 * 1024 * 1024) {
        toast.danger("Uploaded image file cannot exceed 5 MB");
        return;
      }
    }

    const file = {
      name: `${uuid()}.jpg`,
      type: "image/jpeg",
      lastModified: Date.now(),
      lastModifiedDate: new Date(),
      webkitRelativePath: "",
      size: selectedFile?.fileSize,
      uri: selectedFile?.uri
    };

    try {
      const res = await upload({
        file,
        onProgressChange: (progress) => {
          setProgress(progress);
        },
      });

      await updateBanner({ bannerUrl: res.url });
    } catch (error) {
      console.error(error);
      toast.danger("File upload failed");
      resetUploading();
      setOpen(false);
    }
  };

  const resetUploading = () => {
    setIsUploading(false);
    setPreviewURL(null);
  };

  return (
    <View>
      <Image style={styles.bannerImage} source={{ uri: data?.user.bannerUrl }} />
      <View style={styles.editButtonContainer}>
        <EditButton
          containerStyle={styles.editContainer}
          onPress={handleChange}
        />
      </View>
      <View style={styles.avatarContainer}>
        <Avatar style={styles.avatarStyle} src={data?.user.twitterPicture} size={85} />
        {/* <EditButton containerStyle={[styles.editContainer, { marginTop: 0 }]} /> */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  editButtonContainer: {
    alignItems: "flex-end",
    paddingHorizontal: 20,
  },
  editContainer: {
    backgroundColor: Colors.dark.primary,
    borderRadius: 50,
    width: 42,
    height: 42,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -28,
  },
  avatarContainer: {
    paddingHorizontal: 20,
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
  },
  bannerImage: {
    minHeight: 130
  },
  avatarStyle: {
    borderWidth: 1,
    borderColor: "#E8E8E8"
  }
});
