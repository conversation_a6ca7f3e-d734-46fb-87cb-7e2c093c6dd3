export const env = {
  EXPO_PUBLIC_API_URL: process.env.EXPO_PUBLIC_API_URL ?? "",
  EXPO_PUBLIC_MAINNET_RPC_URL: process.env.EXPO_PUBLIC_MAINNET_RPC_URL ?? "",
  EXPO_PUBLIC_SOCKET_URL: process.env.EXPO_PUBLIC_SOCKET_URL ?? "",
  EXPO_PUBLIC_APP_DOMAIN: process.env.EXPO_PUBLIC_APP_DOMAIN ?? "",
  EXPO_PUBLIC_HTTP_BASIC_AUTH: process.env.EXPO_PUBLIC_HTTP_BASIC_AUTH ?? "",
  EXPO_PUBLIC_FIREBASE_VAPID_KEY:
    process.env.EXPO_PUBLIC_FIREBASE_VAPID_KEY ?? "",
  EXPO_PUBLIC_AVAX_CHAINID: process.env.EXPO_PUBLIC_AVAX_CHAINID ?? 43114,
  EXPO_PUBLIC_WALLETCONNECT_PROJECT_ID:
    process.env.EXPO_PUBLIC_WALLETCONNECT_PROJECT_ID ??
    "e80b2f49716ba2901d09ba0745983ce4",
  EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY:
    process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY ??
    "pk_live_51PJSikJD7G42nY3U5Yk0q30TseH7V99GFKJIf72bdsJEPiEBtgUqrLU7ifTOqyUjnhzP2ivMEv7ReNz7zdQyjQaE00Dh4sTVBv",
  EXPO_PUBLIC_DYNAMIC_ENVIRONMENT_ID:
    process.env.EXPO_PUBLIC_DYNAMIC_ENVIRONMENT_ID ?? "",
  EXPO_PUBLIC_COINGECKO_API:
    process.env.EXPO_PUBLIC_COINGECKO_API ??
    "https://api.coingecko.com/api/v3/simple/price?" +
      "ids=avalanche-2,coq-inu,ket,gursonavax,avax-has-no-chill," +
      "sausagers-meat,kimbo,joe,tech,solana,bonk,dogwifcoin," +
      "a-gently-used-2001-honda,moutai,harambe-2&vs_currencies=usd",
};
