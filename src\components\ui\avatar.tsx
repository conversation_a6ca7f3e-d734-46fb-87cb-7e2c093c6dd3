import React from 'react';
import { Image, ImageSourcePropType, ImageStyle, StyleProp, StyleSheet, View } from 'react-native';

interface AvatarProps {
  src: string | undefined;
  size: number;
  style?: StyleProp<ImageStyle>;
  borderSize?: number;
  borderColor?: string;
}

export const AvatarFallBack = ({ avatarSize }: { avatarSize: number }) => {
  return (
    <View style={{ width: avatarSize, height: avatarSize, borderRadius: avatarSize / 2, backgroundColor: '#5E5E5E' }} />
  )
}

export const LocalAvatar = ({ avatarSize, src }: { avatarSize: number, src: ImageSourcePropType }) => {
  return (
    <View style={{ width: avatarSize, height: avatarSize, borderRadius: avatarSize / 2 }}>
      <Image source={src} style={{ width: avatarSize, height: avatarSize, borderRadius: avatarSize / 2 }} />
    </View>
  )
}

const Avatar: React.FC<AvatarProps> = ({ src, size, style, borderSize = 0, borderColor = 'transparent' }) => {
  const containerStyle = [
    styles.container,
    {
      width: size + borderSize * 2,
      height: size + borderSize * 2,
      borderRadius: (size + borderSize * 2) / 2,
      borderWidth: borderSize,
      borderColor: borderColor,
    },
  ];

  const imageStyle = [
    styles.image,
    {
      width: size,
      height: size,
      borderRadius: size / 2,
    },
    style,
  ];

  return (
    <View style={containerStyle}>
      <Image source={{uri: src}} style={imageStyle} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    resizeMode: 'cover',
  },
});

export default Avatar;
