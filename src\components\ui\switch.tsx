import React, { useState } from "react";
import { View, Switch, StyleSheet, ViewStyle } from "react-native";
import { Colors } from "@/constants/Colors";

interface SwitchProps {
  enabledTrackColor?: string;
  disabledTrackColor?: string;
  enabledThumbColor?: string;
  disabledThumbColor?: string;
  ios_backgorundColor?: string;
  isEnabled: boolean;
  setIsEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  containerStyle?: ViewStyle;
  switchStyle?: ViewStyle;
}

const CustomSwitch = ({
  enabledTrackColor = Colors.dark.brandOrange,
  disabledTrackColor = "#767577",
  enabledThumbColor = Colors.dark.white,
  disabledThumbColor = "#f4f3f4",
  ios_backgorundColor = "#3e3e3e",
  isEnabled,
  setIsEnabled,
  containerStyle,
  switchStyle,
}: SwitchProps) => {
  const toggleSwitch = () => setIsEnabled((previousState) => !previousState);

  return (
    <View style={[styles.container, containerStyle]}>
      <Switch
        style={[styles.switch, switchStyle]}
        trackColor={{ false: disabledTrackColor, true: enabledTrackColor }}
        thumbColor={isEnabled ? enabledThumbColor : disabledThumbColor}
        ios_backgroundColor={ios_backgorundColor}
        onValueChange={toggleSwitch}
        value={isEnabled}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 10,
    width: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  switch: {
    transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }],
  },
});

export default CustomSwitch;
