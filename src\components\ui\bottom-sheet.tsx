import { Colors } from '@/constants/Colors';
import { screenHeight } from '@/constants/Device';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { Platform, BackHandler } from 'react-native';

type TBottomSheetProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  children: React.ReactNode;
  height?: number;
};

export const BottomSheet = ({
  open,
  setOpen,
  children,
  height = screenHeight * 0.5,
}: TBottomSheetProps) => {
  const refRBSheet = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => [height], [height]);

  const handleSheetChange = useCallback(
    (index: number) => {
      if (index === -1) {
        setOpen(false);
      }
    },
    [setOpen]
  );

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        pressBehavior="close"
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        enableTouchThrough={false}
        opacity={0.3}
        style={[
          props.style,
          {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          },
        ]}
      />
    ),
    []
  );

  useEffect(() => {
    if (open) {
      requestAnimationFrame(() => {
        refRBSheet.current?.present();
      });
    } else {
      refRBSheet.current?.dismiss();
    }
  }, [open]);

  // Handle Android back button
  useEffect(() => {
    if (!open) return;
    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener(
        'hardwareBackPress',
        () => {
          if (open) {
            refRBSheet.current?.close();
            setOpen(false);
            return true;
          }
          return false;
        }
      );

      return () => backHandler.remove();
    }
  }, [open, setOpen]);

  return (
    <BottomSheetModal
      ref={refRBSheet}
      snapPoints={snapPoints}
      enablePanDownToClose
      enableContentPanningGesture
      animateOnMount
      onChange={handleSheetChange}
      backdropComponent={renderBackdrop}
      backgroundStyle={{
        backgroundColor: Colors.dark.greyBackground,
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        borderColor: '#3B3B3B80',
        borderWidth: 1,
        borderBottomWidth: 0,
      }}
      handleIndicatorStyle={{
        backgroundColor: 'white',
        width: 40,
      }}
    >
      {children}
    </BottomSheetModal>
  );
};
