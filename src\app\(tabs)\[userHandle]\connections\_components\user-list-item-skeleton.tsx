import { View } from "react-native";
import { Skeleton } from "@/components/ui/skaleton";

export const UserListItemSkeleton = () => {
  return (
    <View className="flex-row justify-between items-center px-6 py-4">
      <View className="flex-row items-center gap-4">
        {/* Avatar Skeleton */}
        <Skeleton height={42} width={42} style={{ borderRadius: 21 }} />
        <View className="gap-1">
          {/* Name Skeleton */}
          <Skeleton height={16} width={120} style={{ borderRadius: 4, marginBottom: 4 }} />
          {/* Handle Skeleton */}
          <Skeleton height={14} width={80} style={{ borderRadius: 4 }} />
        </View>
      </View>
      {/* Button Skeleton */}
      <Skeleton height={32} width={80} style={{ borderRadius: 16 }} />
    </View>
  );
};
