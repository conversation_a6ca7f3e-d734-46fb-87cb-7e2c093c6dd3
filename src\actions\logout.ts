import { removeCookie } from "@/cookies/secure-store";
import { dynamicClient } from "@/dynamicClient";

export async function logout() {
  try {
    await removeCookie("token");
    await remove<PERSON><PERSON>ie("user");
    await remove<PERSON>ookie("twitterUser");
    dynamicClient.auth.logout();

    return { success: true };
  } catch (error) {
    console.error(error);
    return { error: "Couldn't log out" };
  }
}
