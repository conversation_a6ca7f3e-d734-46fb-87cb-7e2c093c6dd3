import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  StyleSheet,
} from "react-native";
import { withLayoutContext } from "expo-router";

import { Colors } from "@/constants/Colors";
import { ParamListBase, TabNavigationState } from "@react-navigation/native";
import {
  createMaterialTopTabNavigator,
  MaterialTopTabNavigationEventMap,
  MaterialTopTabNavigationOptions,
} from "@react-navigation/material-top-tabs";

import { SearchProvider, useSearch } from "../context/search-context";
import { ExploreSearch } from "../_components/explore-search";
import useThrottle from "@/hooks/use-throttle";
import { useUsersSearchQuery } from "@/queries/user-queries";
import { ThemedView } from "@/components/ThemedView";
import { ProfileItemCard } from "../_components/profile-item-card";
import { ThemedText } from "@/components/ThemedText";

const { Navigator } = createMaterialTopTabNavigator();

const { height, width } = Dimensions.get("window");

export const MaterialTopTabs = withLayoutContext<
  MaterialTopTabNavigationOptions,
  typeof Navigator,
  TabNavigationState<ParamListBase>,
  MaterialTopTabNavigationEventMap
>(Navigator);

function SearchUsersLayout() {
  const { searchValue } = useSearch();

  const throttledSearchValue = useThrottle(searchValue);
  const { data: searchData, isLoading: isSearchDataLoading } =
    useUsersSearchQuery(throttledSearchValue);

  return (
    <>
      <ExploreSearch />
      {!throttledSearchValue && (
        <MaterialTopTabs
          screenOptions={{
            tabBarStyle: styles.tabBarStyle,
            tabBarItemStyle: styles.tabBarItemStyle,
            tabBarLabelStyle: styles.tabBarLabelStyle,
            tabBarIndicatorStyle: styles.tabBarIndicatorStyle,
            tabBarActiveTintColor: Colors.dark.offWhite,
            tabBarInactiveTintColor: Colors.dark.darkGrey,
          }}
        >
          <MaterialTopTabs.Screen name="top" options={{ title: "Top" }} />
          <MaterialTopTabs.Screen name="new" options={{ title: "New" }} />
          <MaterialTopTabs.Screen
            name="activity"
            options={{ title: "Activity" }}
          />
          <MaterialTopTabs.Screen name="badges" options={{ title: "Badges" }} />
        </MaterialTopTabs>
      )}

      {throttledSearchValue && isSearchDataLoading && (
        <ThemedView className="mt-2">
          <ActivityIndicator size="small" color={Colors.dark.offWhite} />
        </ThemedView>
      )}

      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.users.length > 0 && (
          <ThemedView className="px-5">
            <FlatList
              data={searchData?.users || []}
              renderItem={({ item }) => <ProfileItemCard user={item} />}
              keyExtractor={(item, index) => item.id || index.toString()}
            />
          </ThemedView>
        )}

      {throttledSearchValue &&
        !isSearchDataLoading &&
        searchData &&
        searchData.users.length === 0 && (
          <ThemedView className="mt-10 flex-row w-full items-center justify-center">
            <ThemedView className="max-w-64 text-center">
              <ThemedText className="text-sm font-semibold text-[#EDEDED]">
                No users found!
              </ThemedText>
            </ThemedView>
          </ThemedView>
        )}
    </>
  );
}

export default function TabLayout() {
  return (
    <SearchProvider>
      <SearchUsersLayout />
    </SearchProvider>
  );
}

const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: "transparent",
  },
  tabBarItemStyle: {
    padding: 0,
  },
  tabBarLabelStyle: {
    fontSize: 14,
    fontWeight: "400",
    fontFamily: "InterSemiBold",
    textTransform: "none",
  },
  tabBarIndicatorStyle: {
    backgroundColor: Colors.dark.primary,
  },
});
