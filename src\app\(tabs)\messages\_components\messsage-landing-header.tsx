import { StyleSheet, View } from 'react-native'
import React, { memo } from 'react'
import { ThemedText } from '@/components/ThemedText'
import { globalStyles } from '@/app/globalStyles'

interface MessageLandingProps {
    headerTitle: string,
    HeaderRight?: React.ReactNode
}

const MessageLandingHeader: React.FC<MessageLandingProps> = memo(({ headerTitle, HeaderRight }) => {
    return (
        <View style={styles.headerWrapper}>
            <ThemedText style={styles.headerTitle}>{headerTitle}</ThemedText>
            {HeaderRight}
        </View>
    )
})

export default MessageLandingHeader

const styles = StyleSheet.create({
    headerWrapper: {
        ...globalStyles.rowSpaceBetween,
        flexDirection: 'row',
    },
    headerTitle: {
        fontSize: 26,
        fontFamily: "InterSemiBold"
    }
})