import { useMemo } from "react";

import { Href, router } from "expo-router";
import { Image, Pressable, StyleSheet, View } from "react-native";

import Avatar from "@/components/ui/avatar";
import { Colors } from "@/constants/Colors";
import { divideBigInt, formatAvax } from "@/utils";
import { ThemedText } from "@/components/ThemedText";
import { abbreviateNumber } from "@/utils/abbreviate-number";
import { ArrowDownFilledIcon, ArrowUpFilledIcon } from "@/components/icons";
import { User } from "@/queries/types/top-users-response";

interface UserListItemProps {
  user: User;
  className?: string;
}

export const ProfileItemCard = ({ user, className }: UserListItemProps) => {
  const tickerPrice = useMemo(() => {
    if (user.stats?.keyPrice && !user.keyPrice) {
      return "0";
    }

    return formatAvax(user.stats?.keyPrice || user.keyPrice);
  }, [user.stats?.keyPrice, user.keyPrice]);

  const [isNegative, percentageIncrease] = useMemo(() => {
    const keyPrice = BigInt(user.stats?.keyPrice || 0);
    const lastKeyPrice = BigInt(user.lastKeyPrice || 0);

    const percentage = lastKeyPrice
      ? 100 * (divideBigInt(keyPrice, lastKeyPrice) - 1)
      : keyPrice
      ? 100
      : 0;

    return [percentage < 0, abbreviateNumber(percentage, 2)];
  }, [user.stats?.keyPrice, user.lastKeyPrice]);

  return (
    <Pressable
      onPress={() => router.push(`/${user.twitterHandle}` as Href<string>)}
      style={styles.header}
    >
      <View style={styles.headerProfile}>
        <Avatar size={42} src={user.twitterPicture} />
        <View>
          <ThemedText type="defaultSemiBold" style={styles.username}>
            {user.twitterName}
          </ThemedText>
          <ThemedText style={styles.xHandle}>{user.twitterHandle}</ThemedText>
        </View>
      </View>
      <View style={styles.qtyCnt}>
        <View style={styles.avaxValueCnt}>
          <Image
            style={styles.avaxLogo}
            source={require("@assets/coins/avax.png")}
          />
          <ThemedText style={styles.avaxValue}> {tickerPrice}</ThemedText>
        </View>
        <View style={styles.percentageCnt}>
          {percentageIncrease !== "0" && (
            <>
              {isNegative ? (
                <ArrowDownFilledIcon color={"#f44336"} height={14} width={14} />
              ) : (
                <ArrowUpFilledIcon color={"#40B877"} height={14} width={14} />
              )}
            </>
          )}
          <ThemedText
            style={[styles.percentage, isNegative && { color: "#f44336" }]}
          >
            {percentageIncrease}%
          </ThemedText>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 18,
    width: "100%",
  },
  headerProfile: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },
  username: {
    fontSize: 14,
    color: Colors.dark.white,
    fontWeight: "400",
  },
  xHandle: {
    fontSize: 14,
    fontWeight: "400",
    color: Colors.dark.greyText,
  },
  avaxValue: {
    fontSize: 14,
    fontWeight: "900",
    fontFamily: "InterSemiBold",
    color: Colors.dark.offWhite,
  },
  avaxLogo: {
    width: 16,
    aspectRatio: 1,
  },
  avaxValueCnt: {
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  percentageCnt: {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-end",
  },
  percentage: {
    color: "#40B877",
    fontSize: 14,
    fontWeight: "400",
  },
  qtyCnt: {
    gap: 2,
    alignItems: "center",
  },
});
