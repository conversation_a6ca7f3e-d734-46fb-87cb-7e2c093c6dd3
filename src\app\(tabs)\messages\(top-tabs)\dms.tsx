import { View, StyleSheet, TouchableOpacity } from "react-native";
import { DMRequestsIcon } from "@/components/icons";
import { Colors } from "@/constants/Colors";
import { ThemedText } from "@/components/ThemedText";
import { useDirectMessagesInfiniteQuery, useSearchDMConversationsInfiniteQuery } from "@/queries/chat-queries";
import { useMemo } from "react";
import { useUser } from "@/stores";
import { Href, router } from "expo-router";
import GroupList from "../_components/group-list";
import { useMessaging } from "../_contexts/messaging-context";
import { useSocket } from "@/hooks/use-socket";
import useThrottle from "@/hooks/use-throttle";
import { paddingHorizontal } from "./_layout";
import CustomLinearGradient from "@/components/ui/linear-gradient";
import { ChatbubbleOutlineIcon } from "@/components/icons-v2";

const DMs = () => {
    const { user } = useUser();
    const { selectedGroup, setSelectedGroup } = useMessaging();
    const { setSeen } = useSocket();
    const { searchValue } = useMessaging();
    const throttledSearchValue = useThrottle(searchValue).trim();

    const {
        data: directMessagesData,
        isLoading: directMessagesIsLoading,
        hasNextPage: directMessagesHasNextPage,
        fetchNextPage: fetchNextDirectMessagesPage,
        refetch: refetchDirectMessages,
        isFetchingNextPage: directMessagesIsFetchingNextPage
    } = useDirectMessagesInfiniteQuery();

    const {
        data: searchDirectMessagesData,
        isLoading: isSearchDirectMessagesLoading,
        hasNextPage: searchDirectMessagesHasNextPage,
        fetchNextPage: fetchNextSearchDirectMessagesPage,
        refetch: refetchSearchDirectMessages,
        isFetchingNextPage: searchDirectMessagesIsFetchingNextPage,
    } = useSearchDMConversationsInfiniteQuery(throttledSearchValue);

    const isLoading = directMessagesIsLoading || isSearchDirectMessagesLoading;

    const directMessages = useMemo(() => {
        return directMessagesData?.pages.map((page) => page.groups).flat()?.filter((group) => group.isDirect && !group.isTemporary);
    }, [directMessagesData]);

    const filteredDirectMessages = useMemo(() => {
        return directMessages?.filter((group) => !(group.isRequest && group.lastUserId !== user?.id));
    }, [directMessages, user]);

    const searchDirectMessages = useMemo(() => {
        return searchDirectMessagesData?.pages.map((page) => page.groups).flat()?.filter((group) => group.isDirect && !group.isTemporary);
    }, [searchDirectMessagesData]);

    const filteredSearchDirectMessages = useMemo(() => {
        return searchDirectMessages?.filter((group) => !(group.isRequest && group.lastUserId !== user?.id));
    }, [searchDirectMessages, user]);

    const requestCount = useMemo(() => {
        //don't show the message requests to the owner of that message request.
        const count = directMessages?.filter(
            (group) =>
                group?.isRequest &&
                group?.lastUserId !== user?.id
        )
            .length;
        return count;
    }, [directMessages, user]);

    return (
        <View style={styles.container}>
            {requestCount && requestCount > 0 && !throttledSearchValue && !selectedGroup && !isLoading ? (
                <TouchableOpacity
                    style={styles.messageRequestContainer}
                    onPress={() => router.push("/chat/message-requests" as Href<string>)}
                >
                    <View style={styles.messageRequestIconContainer}>
                        <DMRequestsIcon color={Colors.dark.offWhite} height={24} width={24} />
                    </View>
                    <View style={styles.messageRequestTextContainer}>
                        <ThemedText type="defaultSemiBold">Message Requests</ThemedText>
                        <ThemedText type="greyText">{requestCount} pending message {requestCount === 1 ? 'request' : 'requests'}</ThemedText>
                    </View>
                </TouchableOpacity>
            ) : null}
            <GroupList
                isLoading={throttledSearchValue ? isSearchDirectMessagesLoading : directMessagesIsLoading}
                data={throttledSearchValue ? filteredSearchDirectMessages : filteredDirectMessages}
                refetch={throttledSearchValue ? refetchSearchDirectMessages : refetchDirectMessages}
                hasNextPage={throttledSearchValue ? searchDirectMessagesHasNextPage : directMessagesHasNextPage}
                fetchNextPage={throttledSearchValue ? fetchNextSearchDirectMessagesPage : fetchNextDirectMessagesPage}
                isFetchingNextPage={throttledSearchValue ? searchDirectMessagesIsFetchingNextPage : directMessagesIsFetchingNextPage}
                setSeen={setSeen}
                emptyListText={"No Dms found!"}
                selectedGroupId={selectedGroup?.id || ""}
                setSelectedGroup={setSelectedGroup}
            />
            <CustomLinearGradient
                onPress={() => {
                    router.push("/chat/new-dm" as Href<string>);
                }}
                style={styles.chatBubbleButton}
            >
                <ChatbubbleOutlineIcon
                    style={{ height: 30, width: 30 }}
                    color={Colors.dark.offWhite}
                />
            </CustomLinearGradient>
        </View>
    )
}

export default DMs;

const styles = StyleSheet.create({
    container: {
        width: '100%',
        height: '100%',
        marginTop: 15
    },
    messageRequestContainer: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 15,
        paddingHorizontal: paddingHorizontal
    },
    messageRequestIconContainer: {
        width: '15%',
    },
    messageRequestTextContainer: {
        width: '85%',
        gap: 4
    },
    chatBubbleButton: {
        position: 'absolute',
        height: 55,
        width: 55,
        borderRadius: 55 / 2,
        bottom: 30,
        right: 25,
        alignItems: 'center',
        justifyContent: 'center',
    },
})