import { useState, useEffect } from "react";
import { Keyboard } from "react-native";

const useKeyboardHeight = () => {
    const [keyboardHeight, setKeyboardHeight] = useState(0);

    const handleKeyboardDidShow = (e: any) => {
        setKeyboardHeight(e.endCoordinates.height);
    };
    const handleKeyboardDidHide = () => {
        setKeyboardHeight(0);
    };

    useEffect(() => {
        const showSubscription = Keyboard.addListener('keyboardDidShow', handleKeyboardDidShow);
        const hideSubscription = Keyboard.addListener('keyboardDidHide', handleKeyboardDidHide);
        return () => {
            showSubscription.remove();
            hideSubscription.remove();
        };
    }, []);

    return { keyboardHeight };
};

export default useKeyboardHeight;