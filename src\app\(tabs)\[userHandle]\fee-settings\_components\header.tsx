import { View, Text, Pressable } from "react-native";
import { ArrowBackOutlineIcon } from "@/components/icons";
import { router } from "expo-router";

const Header = () => {
  return (
    <View className="flex-row items-center h-12  mb-4">
      <Pressable onPress={() => router.back()}>
        <ArrowBackOutlineIcon className="text-off-white" width={24} height={24} />
      </Pressable>
      <Text className="flex-1 mr-2 text-center text-lg font-semibold text-off-white">Ticket Management</Text>
    </View>
  );
};

export { Header };
