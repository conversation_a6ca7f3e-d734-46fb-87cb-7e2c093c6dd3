import { ClipPath, Defs, G, Path, Rect, Svg, SvgProps } from "react-native-svg";

export const XLogo = (props: SvgProps) => {
  return (
    <Svg
      width="11"
      height="12"
      viewBox="0 0 11 12"
      fill="none"
      {...props}
    >
      <G clipPath="url(#clip0_991_25087)">
        <Path
          d="M6.54649 5.07857L10.6415 0H9.67111L6.11542 4.40965L3.27551 0H0L4.29451 6.66818L0 11.9938H0.970438L4.72533 7.3371L7.72449 11.9938H11L6.54626 5.07857H6.54649ZM5.21735 6.72692L4.78222 6.06292L1.3201 0.779407H2.81064L5.60461 5.04337L6.03973 5.70737L9.67157 11.2499H8.18103L5.21735 6.72718V6.72692Z"
          fill="currentColor"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_991_25087">
          <Rect width="11" height="12" fill="white" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};
