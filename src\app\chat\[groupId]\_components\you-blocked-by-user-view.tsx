import { ThemedText } from "@/components/ThemedText";
import { Button } from "@/components/ui/button";
import { Colors } from "@/constants/Colors";
import { router } from "expo-router";
import { memo } from "react";
import { LayoutChangeEvent, StyleSheet, View } from "react-native";


type TYouBlockedByUserView = {
    onLayout: (event: LayoutChangeEvent) => void;
}

export const YouBlockedByUserView = memo(({ onLayout }: TYouBlockedByUserView) => {
    return (
        <View onLayout={onLayout} style={styles.container}>
            <ThemedText style={styles.text}>
                You were blocked by this user.
            </ThemedText>
            <Button
                variant="outline"
                onPress={() => {
                    router.back();
                }}
                style={styles.button}
            >
                <ThemedText type="bold">Back</ThemedText>
            </Button>
        </View>
    )
}, (prevProps, nextProps) => {
    return prevProps.onLayout === nextProps.onLayout
})

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderTopWidth: 1,
        borderTopColor: Colors.dark.darkGrey,
        marginTop: 10
    },
    text: {
        lineHeight: 24
    },
    button: {
        width: '100%',
        marginTop: 20
    }
});