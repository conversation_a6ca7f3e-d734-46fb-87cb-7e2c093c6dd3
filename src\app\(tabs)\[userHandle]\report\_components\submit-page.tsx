import { View, Pressable } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThreadUser, User } from "@/types";

export const SubmitPage = ({
  user,
  handleReport,
}: {
  user: User | ThreadUser;
  handleReport: (blockUser: boolean) => void;
}) => (
  <View className="px-6">
    <View className="flex flex-col gap-2 mb-4">
      <ThemedText type="bold" className="text-xl text-off-white">
        Thanks for helping make The Arena better for everyone
      </ThemedText>
      <ThemedText className="text-sm text-gray-text">
        We know it wasn't easy, so we appreciate you taking the time to
        answer those questions.
      </ThemedText>
    </View>

    <View className="mt-4 flex flex-col gap-2 py-2">
      <ThemedText type="bold" className="text-base text-white">
        What's happening now
      </ThemedText>
      <ThemedText className="text-sm text-gray-text">
        We received your report. We'll hide the reported post from your
        timeline in the meantime.
      </ThemedText>
    </View>
    <View className="flex flex-col gap-2 py-2">
      <ThemedText type="bold" className="text-base text-white">What's next</ThemedText>
      <ThemedText className="text-sm text-gray-text">
        It'll take a few days for our team to review your report.
        We'll notify you if we found a rule violation and we'll let
        you know the actions we're taking as a result.
      </ThemedText>
    </View>
    <View className="flex flex-col gap-2 py-2">
      <ThemedText type="bold" className="text-base text-white">
        Additional things you can do in the meantime
      </ThemedText>
      <ThemedText className="text-sm text-gray-text">
        Block @{user.twitterHandle} from following or messaging you. They will
        be able to see your public posts, but will no longer be able to engage
        with them. You also won't see any posts or notifications from @
        {user.twitterHandle}.
      </ThemedText>
    </View>

    <View className="mt-4">
      <Pressable
        className="w-full rounded-full border border-gray-text py-3"
        onPress={() => handleReport(true)}
      >
        <ThemedText type="bold" className="text-sm text-center text-white">
          Block @{user.twitterHandle}
        </ThemedText>
      </Pressable>
    </View>
  </View>
);
