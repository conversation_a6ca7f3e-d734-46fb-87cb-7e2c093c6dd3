import { User } from "@/types";

interface Trade {
  id: string;
  createdOn: string;
  blockNum: string;
  index: number;
  transactionId: string;
  trader: string;
  subject: string;
  isBuy: boolean;
  shareAmount: string;
  amount: string;
  protocolAmount: string;
  subjectAmount: string;
  referralAmount: string;
  supply: string;
  buyPrice: string;
  subjectId: string;
  traderId: string;
  subjectUser: User;
  traderUser: User;
}

export interface TradeTradesResponse {
  trades: Trade[];
}
