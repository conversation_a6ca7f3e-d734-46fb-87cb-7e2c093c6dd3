import { ComponentProps } from "react";

export const LogoIcon = (props: ComponentProps<"svg">) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 32 42"
    fill="none"
    {...props}
  >
    <g fill="currentColor" clipPath="url(#a)">
      <path d="M31.463 15.472V42h-.678V17.454c0-8.154-6.64-14.794-14.794-14.794-8.153 0-14.776 6.623-14.776 14.794V42H.537V15.472C.537 6.919 7.455 0 16.009 0c8.552 0 15.454 6.919 15.454 15.472Z" />
      <path d="M28.768 16.984v22.513h-.678V18.705c0-6.675-5.423-12.099-12.099-12.099-6.675 0-12.1 5.406-12.1 12.082v20.791h-.677V16.967c0-7.058 5.72-12.777 12.777-12.777 7.058 0 12.777 5.719 12.777 12.777v.017Z" />
      <path d="M26.091 18.497v18.479h-.678V19.939c0-5.197-4.224-9.422-9.422-9.422S6.57 14.742 6.57 19.94v17.037h-.678v-18.48c0-5.58 4.52-10.1 10.1-10.1s10.083 4.52 10.083 10.1h.017Z" />
      <path d="M23.396 20.01v14.463h-.678V21.19a6.733 6.733 0 0 0-6.727-6.727c-3.703 0-6.728 3.024-6.728 6.727v13.282h-.678V20.009a7.412 7.412 0 0 1 7.406-7.405 7.412 7.412 0 0 1 7.405 7.405Z" />
      <path d="M20.72 21.521V31.97h-.679v-9.526a4.055 4.055 0 0 0-4.05-4.05 4.043 4.043 0 0 0-4.05 4.05v9.526h-.679V21.521a4.733 4.733 0 0 1 4.729-4.728c2.608 0 4.711 2.12 4.711 4.729h.017Z" />
      <path d="M18.025 23.034v6.415h-.678v-5.772c0-.747-.608-1.356-1.356-1.356-.747 0-1.356.609-1.356 1.356v5.772h-.678v-6.415c0-1.13.922-2.034 2.034-2.034 1.113 0 2.034.921 2.034 2.034Z" />
    </g>
    <defs>
      <clipPath id="a">
        <rect
          width="30.9263"
          height="42"
          fill="white"
          transform="translate(0.536621)"
        />
      </clipPath>
    </defs>
  </svg>
);
