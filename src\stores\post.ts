import { create } from "zustand";

import { Thread } from "@/types";

interface Store {
  type: "reply" | "quote" | null;
  thread: Thread | null;
}

interface Actions {
  setReply: (thread: Thread) => void;
  setQuote: (thread: Thread) => void;
  reset: () => void;
}

export const usePostStore = create<Store & Actions>((set) => ({
  type: null,
  thread: null,
  setReply: (thread) => set({ type: "reply", thread }),
  setQuote: (thread) => set({ type: "quote", thread }),
  reset: () => set({ type: null, thread: null }),
}));
