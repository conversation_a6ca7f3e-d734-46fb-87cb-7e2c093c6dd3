import { globalStyles } from "@/app/globalStyles";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Colors } from "@/constants/Colors";
import { Image, StyleSheet } from "react-native";
import { CardWithDropdown } from "./card-dropdown";
import { CustomTextInput } from "@/components/ui/text-input";
import { CopyOutlineIcon } from "@/components/icons";

export const ExportPrivateKeys = () => {
  const dropdownItems = [
    {
      id: "1",
      walletName: "Current Wallet",
      walletDets: "This is your active wallet in the Arena!",
    },
    {
      id: "2",
      walletName: "Wallet Number 2",
      walletDets: "This wallet was created on 10/16/2023",
    },
  ];
  return (
    <ThemedView darkColor="transparent">
      <ThemedText style={[globalStyles.bottomSheetHeader, styles.header]}>
        Current wallet private key
      </ThemedText>
      <ThemedText style={styles.subText}>
        Export your private key to use it in your wallet of choice. Do not share
        this information with anyone!
      </ThemedText>
      <CardWithDropdown dropdownItems={dropdownItems} />
      <ThemedView style={styles.barcodeWrapper} darkColor="transparent">
        <Image source={require("@assets/images/sample-bar-code.png")} />
      </ThemedView>

      <CustomTextInput
        editable={false} //For disabling the input
        placeholder="0xc0ffee254729296a45a38856AC7..."
        labelText="WALLET ADDRESS"
        inputStyle={styles.disabledInput}
        containerStyle={styles.disabledInputContainer}
        IconRight={
          <CopyOutlineIcon
            width={20}
            height={20}
            color={Colors.dark.lightGreyText}
          />
        }
        iconColor={Colors.dark.lightgrey}
      />
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {},
  header: {
    marginBottom: 12,
  },
  subText: {
    color: "rgba(157, 157, 157, 1)",
    fontSize: 14,
    fontWeight: "400",
  },
  barcodeWrapper: {
    justifyContent: "flex-end",
    alignItems: "center",
    paddingVertical: 30,
  },
  disabledInputContainer: {},
  disabledInput: {
    backgroundColor: Colors.dark.disableInputColor,
    borderWidth: 0,
  },
});
