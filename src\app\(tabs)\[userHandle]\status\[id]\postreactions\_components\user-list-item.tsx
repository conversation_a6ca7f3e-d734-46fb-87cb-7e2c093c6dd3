"use client";

import { InfiniteData, useQueryClient } from "@tanstack/react-query";

import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import {
  PostReactionsLikesResponse,
  PostReactionsRepostsResponse,
  User,
} from "@/queries/types/postreactions";
import { useUser } from "@/stores";
import { useFollowMutation, useUnfollowMutation } from "@/queries/follow-mutations";
import { Href, Link } from "expo-router";
import Avatar from "@/components/ui/avatar";
import { View, Text } from "react-native";

export const UserListItem = ({
  user,
  threadId,
}: {
  user: User;
  threadId: string;
}) => {
  const { user: me } = useUser();
  const queryClient = useQueryClient();
  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${user.twitterName}!`);
      const previousPostReactionsLikes = queryClient.getQueriesData({
        queryKey: ["postreactions", "likes", threadId],
      });
      const previousPostReactionsReposts = queryClient.getQueriesData({
        queryKey: ["postreactions", "reposts", threadId],
      });

      queryClient.setQueryData<InfiniteData<PostReactionsLikesResponse>>(
        ["postreactions", "likes", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                likedUsers: page.likedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: true,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      queryClient.setQueryData<InfiniteData<PostReactionsRepostsResponse>>(
        ["postreactions", "reposts", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                repostedUsers: page.repostedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: true,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      return { previousPostReactionsLikes, previousPostReactionsReposts };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to follow ${user.twitterName}.`);
      queryClient.setQueryData(
        ["postreactions", "likes", threadId],
        context?.previousPostReactionsLikes,
      );
      queryClient.setQueryData(
        ["postreactions", "reposts", threadId],
        context?.previousPostReactionsReposts,
      );
    },
  });

  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      const previousPostReactionsLikes = queryClient.getQueriesData({
        queryKey: ["postreactions", "likes", threadId],
      });
      const previousPostReactionsReposts = queryClient.getQueriesData({
        queryKey: ["postreactions", "reposts", threadId],
      });

      queryClient.setQueryData<InfiniteData<PostReactionsLikesResponse>>(
        ["postreactions", "likes", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                likedUsers: page.likedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: false,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      queryClient.setQueryData<InfiniteData<PostReactionsRepostsResponse>>(
        ["postreactions", "reposts", threadId],
        (old) => {
          if (!old) return old;

          return {
            ...old,
            pages: old.pages.map((page) => {
              return {
                ...page,
                repostedUsers: page.repostedUsers.map((f) => {
                  if (f.id === user.id) {
                    return {
                      ...f,
                      isFollowing: false,
                    };
                  }
                  return f;
                }),
              };
            }),
          };
        },
      );

      return { previousPostReactionsLikes, previousPostReactionsReposts };
    },
    onError(err, variables, context) {
      toast.danger(`Failed to unfollow ${user.twitterName}.`);
      queryClient.setQueryData(
        ["postreactions", "likes", threadId],
        context?.previousPostReactionsLikes,
      );
      queryClient.setQueryData(
        ["postreactions", "reposts", threadId],
        context?.previousPostReactionsReposts,
      );
    },
  });

  function handleFollow() {
    if (user.isFollowing) {
      unfollow({ userId: user.id });
    } else {
      follow({ userId: user.id });
    }
  }

  return (
    <View className="flex-row w-full justify-between space-x-4 px-6 py-4">
      <View className="flex-row flex-1 items-center space-x-[10px] overflow-hidden">
        <Link href={`/${user.twitterHandle}` as Href<string>}>
          <Avatar src={user.twitterPicture} size={42} />
        </Link>
        <View className="flex flex-1 flex-col space-y-1">
          <View className="flex-row space-x-1.5">
            <Link
              href={`/${user.twitterHandle}` as Href<string>}
              className="text-sm leading-4 text-[#F4F4F4] flex-shrink"
                numberOfLines={1}
                 ellipsizeMode="tail"
            >
              <Text> {user.twitterName} </Text>
            </Link>
          </View>
          <Link
            href={`/${user.twitterHandle}` as Href<string>}
            className="text-sm leading-4 text-[#808080] flex-shrink"
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            <Text>@{user.twitterHandle}</Text>
          </Link>
        </View>
      </View>
      { me?.id !== user.id && (
        <View className="flex-col items-end justify-center space-y-[2px]">
          <Button
            variant={user.isFollowing ? "outline" : "secondary"}
            onPress={handleFollow}
            style={{ width: 96, paddingVertical: 6}}
          >
            {user.isFollowing ? "Unfollow" : "Follow"}
        </Button>
        </View>
      )}
    </View>
  );
};
