import { FlatList, StyleSheet, Text, TouchableOpacity, useWindowDimensions, View } from "react-native";
import { Reaction } from "@/queries/types/chats";
import { abbreviateNumber } from "@/utils/abbreviate-number";
import { Colors } from "@/constants/Colors";
import { memo, useCallback, useMemo, useRef, useState } from "react";
import { getEmojiFromUnicode } from "@/utils/string-formatter";
import { SceneMap, TabBar, TabView } from "react-native-tab-view";
import { useQueries } from "@tanstack/react-query";
import { getUserById } from "@/api/client/user";
import { User } from "@/queries/types/top-users-response";
import Avatar from "@/components/ui/avatar";
import { BottomSheet } from "@/components/ui/bottom-sheet";

type TReactionList = {
    reactions: Reaction[];
    loggedinUserId: string;
    isMyMessage: boolean;
    unreactToMessage: () => void;
}

const prefix = 'tab_';
const alltabkey = `${prefix}all`;

const ReactionList = memo((
    {
        reactions,
        loggedinUserId,
        isMyMessage,
        unreactToMessage
    }: TReactionList) => {
    const layout = useWindowDimensions();
    const [index, setIndex] = useState(0);
    const [isBottomSheetOpen, setBottomSheetOpen] = useState(false);

    const sortReactions = (a: Reaction, b: Reaction) => {
        if (a.userId === loggedinUserId) {
            return -1;
        }
        if (b.userId === loggedinUserId) {
            return 1;
        }
        return 0;
    };

    const sortedReactions = useMemo(() => reactions?.sort(sortReactions), [reactions]);

    const groupedReactions = useMemo(() => {
        const reactions = sortedReactions?.reduce(
            (acc, reaction) => {
                if (!acc[reaction.reaction]) {
                    acc[reaction.reaction] = [reaction];
                } else {
                    acc[reaction.reaction] = [...acc[reaction.reaction], reaction]?.sort(
                        sortReactions,
                    );
                }
                return acc;
            },
            {} as Record<string, Reaction[]>,
        )
        return Object.entries(reactions);
    }, [sortedReactions]);

    const { data: users, isLoading: isLoadingUsers } = useQueries({
        queries: sortedReactions?.map((reactions) => ({
            queryKey: ["user", "id", reactions.userId],
            queryFn: () => {
                return getUserById({ userId: reactions.userId });
            },
        })),
        combine: (results) => {
            return {
                data: results
                    .map((result) => result.data?.user)
                    .reduce(
                        (acc, curr) => {
                            if (curr && !acc[curr.id]) {
                                acc[curr.id] = curr;
                            }
                            return acc;
                        },
                        {} as Record<string, User>,
                    ),
                isLoading: results.some((result) => result.isLoading),
            };
        },
    });

    const routes = useMemo(() => {
        const totalReactionsCount = groupedReactions?.reduce((acc, [_, reactions]) => acc + reactions.length, 0);
        return [
            {
                key: alltabkey,
                title: `Reactions ${abbreviateNumber(totalReactionsCount)}`
            },
            ...groupedReactions?.map(([emoji, reactions]) => ({
                key: `${prefix}${emoji}`,
                title: `${getEmojiFromUnicode(emoji)} ${abbreviateNumber(reactions.length)}`
            }))
        ];
    }, [groupedReactions]);

    const renderUser = useCallback((
        user: User,
        isMe: boolean,
        reaction: string
    ) => {

        return (
            <TouchableOpacity style={styles.userItem} disabled={!isMe} onPress={() => {
                setBottomSheetOpen(false);
                unreactToMessage();
            }}>
                <Avatar src={user.twitterPicture} size={42} />
                <View style={styles.userInfo}>
                    <Text style={styles.userName} numberOfLines={1}>{isMe ? 'You' : user.twitterName}</Text>
                    {
                        isMe ? (
                            <Text style={[styles.userName, { color: Colors.dark.grey }]}>Click to remove</Text>
                        ): null
                    }
                </View>
                <Text style={[styles.userName, { fontSize: 18 }]}>{getEmojiFromUnicode(reaction)}</Text>
            </TouchableOpacity>
        )
    }, [unreactToMessage]);

    const renderUserList = useCallback((
        isLoading: boolean,
        reactions: Reaction[],
        usersArray: Record<string, User>,
        userId: string
    ) => {
        return (
            <View style={{ flex: 1 }}>
                {!isLoading && reactions?.length > 0 && Object.keys(usersArray).length > 0 ?
                    <FlatList
                        data={reactions}
                        keyExtractor={(item) => item.id}
                        renderItem={({ item }) => {
                            const user = usersArray[item.userId];
                            const isMe = user.id === userId;
                            return renderUser(user, isMe, item.reaction);
                        }}
                    />
                    : null}
            </View>
        )
    }, []);

    // Generate scene map dynamically
    const sceneMap = useMemo(() => {
        const scenes: Record<string, () => React.ReactNode> = {};
        scenes[alltabkey] = () => renderUserList(isLoadingUsers, sortedReactions, users, loggedinUserId);
        groupedReactions?.forEach(([emoji, reactions]) => {
            scenes[`${prefix}${emoji}`] = () => renderUserList(isLoadingUsers, reactions, users, loggedinUserId);
        });
        return scenes;
    }, [groupedReactions, isLoadingUsers, sortedReactions, users, loggedinUserId, renderUserList]);

    const renderTabBar = useCallback((props: any) => (
        <TabBar
            {...props}
            indicatorStyle={{ backgroundColor: Colors.dark.brandOrange }}
            style={{ backgroundColor: Colors.dark.secondaryBackground }}
        />
    ), []);

    const onReactionPress = () => {
        setBottomSheetOpen(true);
    }

    return groupedReactions?.length > 0 ? (
        <>
            <TouchableOpacity onPress={onReactionPress} activeOpacity={0.9} style={[styles.reactions, isMyMessage ? { right: 10 } : { left: 10 }]}>
                {
                    groupedReactions?.map(([reaction, reactions]) => (  
                        <Text key={reaction} style={styles.message}>{getEmojiFromUnicode(reaction)}</Text>
                    ))
                }
                <Text style={[styles.message, { color: Colors.dark.grey }]}>{abbreviateNumber(sortedReactions?.length)}</Text>
            </TouchableOpacity>
            <BottomSheet open={isBottomSheetOpen} setOpen={setBottomSheetOpen}>
            {
                    Object.keys(sceneMap).length > 0 && routes?.length > 0 ? (
                        <View style={styles.container}>
                            <TabView
                                renderTabBar={renderTabBar}
                                navigationState={{ index, routes }}
                                renderScene={SceneMap(sceneMap)}
                                onIndexChange={setIndex}
                                initialLayout={{ width: layout.width, height: 0 }}
                            />
                        </View>
                    ) : null
                }
            </BottomSheet>
        </>
    ) : null

}, (prevProps, nextProps) => {
    return (
        JSON.stringify(prevProps.reactions) === JSON.stringify(nextProps.reactions) &&
        prevProps.loggedinUserId === nextProps.loggedinUserId &&
        prevProps.isMyMessage === nextProps.isMyMessage
    )
})

export default ReactionList;

const styles = StyleSheet.create({
    reactions: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 5,
        position: 'absolute',
        bottom: -18,
        backgroundColor: Colors.dark.secondaryBackgroundDark,
        borderRadius: 20,
        paddingVertical: 3,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: Colors.dark.background,
    },
    message: {
        fontSize: 11,
        fontFamily: "InterSemiBold",
        fontWeight: "600",
    },
    container: {
        flex: 1,
    },
    userItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
        paddingVertical: 15,
    },
    userName: {
        fontSize: 14,
        color: Colors.dark.text
    },
    userInfo: {
        flexDirection: 'column',
        gap: 2,
        width: '70%'
    }
})