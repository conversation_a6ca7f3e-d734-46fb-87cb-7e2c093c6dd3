import React, { useState } from "react";
import { View, TextInput, TouchableOpacity, StyleProp, ViewStyle } from "react-native";
import { SearchFilledIcon, CloseOutlineIcon } from "@/components/icons";
import { Colors } from "@/constants/Colors";

interface SearchProps {
  placeholder: string;
  value?: string;
  onChangeText: (text: string) => void;
  onClear?: () => void;
  containerStyle?: StyleProp<ViewStyle>;
}

export const Search = ({
  placeholder,
  value = "",
  onChangeText,
  onClear,
  containerStyle,
}: SearchProps) => {
  const handleClear = () => {
    if (onClear) {
      onClear();
    } else {
      onChangeText("");
    }
  };

  const [isFocused, setIsFocused] = useState(false);

  return (
    <View className="relative" style={containerStyle}>
      <View
        className="flex-row items-center rounded-full h-[47px] px-4 bg-transparent"
        style={{
          borderColor: isFocused ? Colors.dark.white : '#333333',
          borderWidth: isFocused ? 2 : 1,
        }}
      >
        <SearchFilledIcon
          width={16}
          height={16}
          fill={Colors.dark.greyText}
          className="mr-2"
        />
        <TextInput
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={Colors.dark.greyText}
          className="flex-1 text-[#F4F4F4] h-full"
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />
        {value.length > 0 && (
          <TouchableOpacity
            onPress={handleClear}
            className="rounded-full border border-[#333333] p-1 bg-[#141414]"
          >
            <CloseOutlineIcon
              width={14}
              height={14}
              color={Colors.dark.offWhite}
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};
