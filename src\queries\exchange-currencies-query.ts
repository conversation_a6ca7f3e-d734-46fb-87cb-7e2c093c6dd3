import { getExchangeCurrencies } from "@/api/client/currency";
import { useQuery } from "@tanstack/react-query";

export interface ExchangeCurrency {
  symbol: string;
  systemRate: string;
  name: string;
  image: string;
  categories: string[];
}

export const useExchangeCurrenciesQuery = () => {
  return useQuery({
    queryKey: ["currency", "exchange"],
    queryFn: async () => {
      const data = await getExchangeCurrencies();

      return data.map((currency) => ({
        symbol: currency.symbol,
        systemRate: currency.systemRate,
        name: currency.name,
        image: currency.photoURL || "",
        categories: ["exchange"],
      }));
    },
  });
};
