import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import {
  getConversationByProfile,
  getConversations,
  getDirectMessages,
  getGroup,
  getMessagesBefore,
  getPinnedMessages,
  searchDMConversations,
  searchRoomConversations,
} from "@/api/client/chat";

export const useConversationsInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["chat", "conversations"],
    queryFn: ({ pageParam }) => {
      return getConversations(pageParam);
    },
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const useSearchRoomConversationsInfiniteQuery = (
  searchString: string,
) => {
  return useInfiniteQuery({
    queryKey: ["chat", "search-room-conversations", searchString],
    queryFn: ({ pageParam }) => {
      return searchRoomConversations({ ...pageParam, searchString });
    },
    enabled: !!searchString,
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const useDirectMessagesInfiniteQuery = () => {
  return useInfiniteQuery({
    queryKey: ["chat", "direct-messages"],
    queryFn: ({ pageParam }) => {
      return getDirectMessages(pageParam);
    },
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const useSearchDMConversationsInfiniteQuery = (searchString: string) => {
  return useInfiniteQuery({
    queryKey: ["chat", "search-dm-conversations", searchString],
    queryFn: ({ pageParam }) => {
      return searchDMConversations({ ...pageParam, searchString });
    },
    enabled: !!searchString,
    initialPageParam: {
      page: 1,
      pageSize: 25,
    },
    getNextPageParam: (lastPage, pages, lastPageParam) => {
      if (lastPage.numberOfPages > lastPageParam.page) {
        return { ...lastPageParam, page: lastPageParam.page + 1 };
      }

      return undefined;
    },
  });
};

export const useGroupByIdQuery = ({ groupId, twitterHandle }: { groupId: string, twitterHandle: string }) => {
  return useQuery({
    queryKey: ["chat", "group", groupId],
    queryFn: () => getGroup({ groupId, twitterHandle }),
  });
};

export const useGroupByUserIdQuery = ({ userId }: { userId?: string }) => {
  return useQuery({
    queryKey: ["chat", "group", "profile", userId],
    queryFn: () => {
      if (!userId) {
        return {
          group: null,
        };
      }
      return getConversationByProfile({ userId });
    },
    enabled: !!userId,
  });
};

export const useMessagesBeforeQuery = ({
  groupId,
  timeFrom,
}: {
  groupId: string;
  timeFrom?: number;
}) => {
  return useQuery({
    queryKey: ["chat", "messages", "b", { groupId, timeFrom }],
    queryFn: () => getMessagesBefore({ groupId, timeFrom }),
  });
};

export const usePinnedMessagesQuery = ({ groupId }: { groupId: string  }) => {
  return useQuery({
    queryKey: ["chat", "messages", "pinned", groupId],
    queryFn: () => getPinnedMessages({ groupId }),
    enabled: !!groupId,
  });
};
