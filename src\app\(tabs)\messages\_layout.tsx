import { StyleSheet } from 'react-native'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';

const MessageLayout = () => {
    return (
        <SafeAreaView style={styles.stackStyles} >
            <Stack>
                <Stack.Screen
                    name="(top-tabs)"
                    options={{ headerShown: false }}
                />
            </Stack>
        </SafeAreaView>
    );
}

export default MessageLayout;

const styles = StyleSheet.create({
    stackStyles: {
        flex: 1
    }
})