// "use client";

// import { useEffect, useState } from "react";
// import { useQueryClient } from "@tanstack/react-query";
// import { v4 as uuid } from "uuid";
// import { useCookies } from "react-cookie";

// import { Label } from "@/components/ui/label";
// import { Switch } from "@/components/ui/switch";
// import {
//   useCloudMessagingSettings,
//   useSaveCloudMessagingSettingsMutation,
// } from "@/queries";
// import { CloudMessagingSettingsResponse } from "@/queries/types/cloud-messaging";
// import { generateToken } from "@/lib/firebase";

// export default function PushNotificationsSettings() {
//   const queryClient = useQueryClient();

//   const [cookies, setCookie] = useCookies(["__uv"]);

//   const [isMounted, setIsMounted] = useState(false);
//   const [permission, setPermission] = useState<NotificationPermission>();

//   // const { data, isLoading } = useCloudMessagingSettings();
//   const { mutateAsync: saveSettings, isPending } =
//     useSaveCloudMessagingSettingsMutation({
//       onMutate: (variables) => {
//         queryClient.setQueryData(
//           ["cloud-messaging", "settings"],
//           (oldData: CloudMessagingSettingsResponse) => {
//             if (!oldData) return oldData;

//             return {
//               settings: {
//                 ...oldData.settings,
//                 notificationsEnabled: variables.settings.notificationsEnabled,
//               },
//             };
//           },
//         );
//       },
//     });

//   const handleNotificationsSwitch = async (checked: boolean) => {
//     await saveSettings({
//       settings: {
//         notificationsEnabled: checked,
//       },
//     });
//   };

//   const toggleNotification = async () => {
//     if (typeof window !== "undefined" && "Notification" in window) {
//       if (window.Notification.permission === "granted") {
//         if (!data?.settings.notificationsEnabled) {
//           await generateToken(cookies.__uv);
//           setPermission(window.Notification.permission);
//         }
//       } else {
//         const permission = await window.Notification.requestPermission();

//         if (permission === "granted") {
//           await generateToken(cookies.__uv);
//           setPermission(window.Notification.permission);
//         }
//       }
//     }
//   };

//   useEffect(() => {
//     const uv = uuid() + "-" + Date.now().toString(16);
//     cookies.__uv ||
//       setCookie("__uv", uv, {
//         expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
//       });
//   }, [cookies, setCookie]);

//   useEffect(() => {
//     if (!("Notification" in window)) {
//       console.error("This browser does not support push notifications");
//     } else {
//       setPermission(Notification.permission);
//     }
//     setIsMounted(true);
//   }, []);

//   if (!isMounted) {
//     return null;
//   }

//   if (permission === "denied") {
//     return (
//       <div className="mx-auto mt-20 max-w-80 px-6 text-center">
//         <h4 className="text-lg font-semibold text-off-white">
//           Turn on notifications?
//         </h4>
//         <p className="mt-2 text-sm text-light-gray-text">
//           To get notifications from The Arena, you’ll need to allow them in your
//           browser settings first.
//         </p>
//       </div>
//     );
//   }

//   return (
//     <div className="px-6 py-10">
//       <Label className="flex w-full flex-col gap-1">
//         <div className="flex w-full items-center justify-between gap-2">
//           <h4 className="text-base font-medium normal-case leading-5 text-off-white">
//             Push notifications
//           </h4>
//           <Switch
//             checked={
//               permission === "granted" && data?.settings.notificationsEnabled
//             }
//             onCheckedChange={handleNotificationsSwitch}
//             disabled={isLoading || isPending}
//             onClick={toggleNotification}
//           />
//         </div>
//         <p className="text-xs font-normal normal-case text-light-gray-text">
//           Get push notifications to find out what’s going on when you’re not on
//           The Arena.
//         </p>
//       </Label>
//     </div>
//   );
// }
