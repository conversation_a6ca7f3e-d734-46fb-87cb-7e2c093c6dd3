import { useMemo } from 'react';

import { Href, Link, router } from 'expo-router';
import { Image, ScrollView, StyleSheet, View } from 'react-native';

import { logout } from '@/actions/logout';
import { ThemedText } from '@/components/ThemedText';
import Avatar from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Colors } from '@/constants/Colors';
import { useBalancesQuery } from '@/queries/balance-queries';
import { useUser } from '@/stores';
import { formatEther } from '@/utils';

import { navigationRoutes } from '@/navigationRoutes';
import { MenuItem } from './menu-item';

const links = [
  {
    name: 'Token Portal',
    navigateTo: '/token-portal',
  },
  {
    name: 'Bookmarks',
    navigateTo: '/bookmarks',
  },
  {
    name: 'Settings & Support',
    navigateTo: '/settings-support',
  },
  {
    name: '<PERSON><PERSON> & <PERSON>arn',
    navigateTo: '/refer-earn',
  },
  {
    name: 'ArenaBook',
    navigateTo: '/arena-book',
  },
];

export const MenuModal = () => {
  const { user } = useUser();

  const { data: balances } = useBalancesQuery({
    address: user?.address,
  });

  const balance = useMemo(() => {
    if (!balances) return '0.00';

    const formattedEther = formatEther(balances.AVAX.toString());

    if (parseFloat(formattedEther) >= 1) {
      return parseFloat(formattedEther).toFixed(2);
    }

    return formattedEther;
  }, [balances]);

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.scrollContainer}>
          <View style={styles.head}>
            <Link href={`/${user?.twitterHandle}` as Href<string>}>
              <View style={styles.profileDets}>
                <Avatar src={user?.twitterPicture} size={45} />
                <View>
                  <ThemedText style={styles.accName}>
                    {user?.twitterName}
                  </ThemedText>
                  <ThemedText style={styles.accHandle}>
                    {user?.twitterHandle}
                  </ThemedText>
                </View>
              </View>
            </Link>
            <View style={styles.showAvaxContainer}>
              <Image
                style={styles.avaxLogo}
                source={require('@assets/coins/avax.png')}
              />
              <ThemedText style={styles.avaxValue}>{balance}</ThemedText>
            </View>
          </View>
          <View style={styles.body}>
            <MenuItem
              key={`profile-${user?.twitterHandle}`}
              item={{
                name: 'Profile',
                navigateTo: `/${user?.twitterHandle}` as Href<string>,
              }}
            />
            {links.map((item, index) => (
              <MenuItem key={`${item.name}-${index}`} item={item} />
            ))}
          </View>
        </View>
      </ScrollView>
      <View style={styles.footer}>
        <Button
          textStyle={styles.buttonText}
          variant="outline"
          onPress={async () => {
            await logout();
            router.push(navigationRoutes.login);
          }}
        >
          Log Out
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    backgroundColor: 'rgba(15, 15, 15, 0.95)',
    borderWidth: 1,
    borderColor: 'rgba(59, 59, 59, 0.3)',
    shadowColor: 'rgba(0, 0, 0, 0.25)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 4,
    gap: 32,
    paddingVertical: 80,
  },
  scrollContainer: { gap: 32, paddingTop: 5 },
  head: {
    paddingHorizontal: 24,
    gap: 32,
  },
  profileDets: { gap: 8 },
  accName: {
    fontWeight: '500',
    fontSize: 14,
    color: Colors.dark.offWhite,
  },
  accHandle: {
    fontWeight: '400',
    fontSize: 12,
    color: Colors.dark.greyText,
  },
  showAvaxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: 'rgba(234, 84, 10, 1)',
    alignSelf: 'flex-start',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  avaxLogo: {
    width: 16,
    aspectRatio: 1,
  },
  avaxValue: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.dark.offWhite,
  },
  body: {
    borderTopWidth: 1,
    borderColor: 'rgba(59, 59, 59, 1)',
  },
  footer: {
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  button: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.dark.greyText,
    paddingVertical: 12,
    paddingHorizontal: 80,
  },
  buttonText: {
    fontWeight: '600',
  },
});
