{"name": "arena_mobile_app", "main": "polyfills.ts", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "eas-build-android-dev": "npx eas-cli build --platform android --profile development", "eas-build-android-preview": "npx eas-cli build --platform android --profile preview", "eas-build-android-prod": "npx eas-cli build --platform android --profile production", "build:ios": "eas build -p ios", "build:android": "expo build:android"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@dynamic-labs/client": "^4.0.0-alpha.5", "@dynamic-labs/react-native-extension": "^4.0.0-alpha.5", "@ethersproject/shims": "^5.7.0", "@expo/cli": "^0.22.26", "@expo/vector-icons": "^14.0.2", "@gorhom/bottom-sheet": "^4.6.4", "@gorhom/portal": "^1.0.14", "@hookform/resolvers": "^3.9.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-masked-view/masked-view": "^0.2.8", "@react-navigation/drawer": "^6.7.2", "@react-navigation/material-top-tabs": "^6.6.13", "@react-navigation/native": "^6.0.2", "@solana-mobile/mobile-wallet-adapter-protocol": "^2.1.3", "@solana-mobile/mobile-wallet-adapter-protocol-web3js": "^2.1.3", "@solana/spl-token": "^0.4.8", "@solana/web3.js": "^1.95.3", "@tanstack/react-query": "^5.51.15", "@types/uuid": "^10.0.0", "axios": "^1.7.2", "buffer": "^6.0.3", "clsx": "^2.1.1", "date-fns": "^3.6.0", "ethers": "^6.13.2", "expo": "~50.0.14", "expo-application": "~5.8.4", "expo-auth-session": "~5.4.0", "expo-av": "~13.10.6", "expo-clipboard": "~5.0.1", "expo-constants": "15.4.6", "expo-crypto": "~12.8.1", "expo-dev-client": "~3.3.12", "expo-document-picker": "~11.10.1", "expo-file-system": "~16.0.9", "expo-font": "~11.10.3", "expo-image": "~1.10.6", "expo-image-picker": "~14.7.1", "expo-linear-gradient": "~12.7.2", "expo-linking": "~6.2.2", "expo-router": "~3.4.10", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.26.5", "expo-status-bar": "~1.11.1", "expo-system-ui": "~2.9.4", "expo-updates": "~0.24.13", "expo-web-browser": "~12.8.2", "framer-motion": "^12.7.4", "html-entities": "^2.6.0", "nativewind": "^2.0.11", "react": "18.2.0", "react-dom": "18.2.0", "react-error-boundary": "^5.0.0", "react-native": "0.73.6", "react-native-gesture-handler": "~2.14.0", "react-native-get-random-values": "~1.8.0", "react-native-pager-view": "6.2.3", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "~3.6.2", "react-native-render-html": "^6.3.4", "react-native-root-toast": "^3.6.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-tab-view": "^3.5.2", "react-native-video": "^6.10.1", "react-native-video-player": "^0.16.2", "react-native-web": "~0.19.10", "react-native-webview": "13.6.4", "react-qr-code": "^2.0.15", "react-virtuoso": "^4.12.6", "rn-emoji-keyboard": "^1.7.0", "socket.io-client": "^4.7.5", "tailwindcss": "3.3.0", "uuid": "^10.0.0", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/node": "^22.14.1", "@types/react": "~18.2.45", "@types/react-native-video-player": "^0.10.7", "@types/react-test-renderer": "^18.0.7", "jest": "^29.2.1", "jest-expo": "~50.0.4", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}