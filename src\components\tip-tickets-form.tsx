import React, { useState } from "react";
import { View, Text, FlatList, TouchableOpacity, Alert } from "react-native";
import Avatar from "./ui/avatar";
import { But<PERSON> } from "./ui/button";
import { CustomTextInput } from "./ui/text-input";
import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { z } from "zod";
import { Controller, useForm } from "react-hook-form";
import { SearchOutlineIcon } from "@/components/icons";

// Define the Ticket type
interface Ticket {
  id: string;
  subjectUser: {
    twitterPicture: string;
    twitterName: string;
    twitterHandle: string;
  };
  amount: number;
}

const MOCK_TICKETS: Ticket[] = [
  {
    id: "1",
    subjectUser: {
      twitterPicture: "https://randomuser.me/api/portraits/men/32.jpg",
      twitterName: "<PERSON> Do<PERSON>",
      twitterHandle: "john_doe",
    },
    amount: 3.25,
  },
  {
    id: "2",
    subjectUser: {
      twitterPicture: "https://randomuser.me/api/portraits/women/44.jpg",
      twitterName: "<PERSON>",
      twitterHandle: "jane_smith",
    },
    amount: 1.5,
  },
];

function formatNumericValue(val: number | string) {
  return Number(val).toLocaleString(undefined, { maximumFractionDigits: 2 });
}

const TipInput = z.object({
  tipAmount: z
    .string()
    .min(1, {
      message: "Tip amount is required",
    })
    .refine((v) => !isNaN(parseFloat(v.replace(/,/g, ""))), {
      message: "Tip amount must be a number",
    }),
});
type TipInputType = z.infer<typeof TipInput>;

export default function TipTicketsForm() {
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [amount, setAmount] = useState("");
  const [search, setSearch] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);

  const filteredTickets = MOCK_TICKETS.filter(
    t =>
      t.subjectUser.twitterName.toLowerCase().includes(search.toLowerCase()) ||
      t.subjectUser.twitterHandle.toLowerCase().includes(search.toLowerCase())
  );

  const form = useForm<TipInputType>({
    defaultValues: {
      tipAmount: "",
    },
    resolver: undefined, // Add zodResolver(TipInput) if you want validation
    reValidateMode: "onChange",
  });

  return (
    <View>
      <ThemedText className="mb-1 mt-4 text-xs text-brand-lightgrey">
        BALANCE: {selectedTicket ? formatNumericValue(selectedTicket.amount) : 0}
      </ThemedText>
      <View className="relative mb-1">
        {selectedTicket ? (
          <View className="flex-row items-center rounded-lg border border-[#B5B5B5] bg-transparent h-[50px] px-3 mb-3">
            <Avatar src={selectedTicket.subjectUser.twitterPicture} size={28} />
            <Text className="text-white ml-2 font-semibold">
              {selectedTicket.subjectUser.twitterName}
            </Text>
            <TouchableOpacity
              onPress={() => {
                setSelectedTicket(null);
                setSearch("");
                setShowDropdown(false);
              }}
              className="ml-2 p-1"
            >
              <Text className="text-[#aaa] text-lg">×</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <CustomTextInput
            inputStyle={{ paddingLeft: 40, paddingRight: 12, height:50, marginBottom: 12 }}
            placeholder="Search the Arena"
            placeHolderColor="#888"
            value={search}
            onChangeText={text => {
              setSearch(text);
              setShowDropdown(!!text);
              setSelectedTicket(null);
            }}
            editable={true}
            IconLeft={<SearchOutlineIcon width={22} height={22} style={{ marginBottom: 12 }} color="#888" />}
          />
        )}
        {showDropdown && !selectedTicket && (
          <View className="bg-dark-bk rounded-lg border border-[#333] mt-[50px] max-h-[150px] absolute left-0 right-0 z-10">
            {filteredTickets.length > 0 ? (
              <FlatList
                data={filteredTickets}
                keyExtractor={item => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    className="flex-row items-center p-2.5 border-b border-[#333]"
                    onPress={() => {
                      setSelectedTicket(item);
                      setSearch("");
                      setShowDropdown(false);
                    }}
                  >
                    <Avatar src={item.subjectUser.twitterPicture} size={24} />
                    <View className="ml-2 flex-1">
                      <Text className="text-white font-semibold">{item.subjectUser.twitterName}</Text>
                      <Text className="text-[#aaa] text-xs">@{item.subjectUser.twitterHandle}</Text>
                    </View>
                    <Text className="ml-auto text-xs text-[#B5B5B5]">{formatNumericValue(item.amount)} tickets</Text>
                  </TouchableOpacity>
                )}
              />
            ) : (
              <Text className="text-[#aaa] p-4">
                Nothing found
              </Text>
            )}
          </View>
        )}
      </View>

        <Controller
        name="tipAmount"
        control={form.control}
        render={({ field: { name, onChange, value, onBlur, ref } }) => (
          <CustomTextInput
            labelText="Tip amount"
            placeholder="How much do you want to tip?"
            value={value}
            onChangeText={(text) => {
              let value: string | number = text;
              if (value === "") {
                onChange(value);
                return;
              }

              if (!value.includes(".")) {
                value = parseFloat(value.replace(/,/g, ""));
                if (isNaN(value)) return;
                value = formatNumericValue(value);
              }

              onChange(value);
            }}
            onBlur={onBlur}
            errorMessage={form.formState.errors.tipAmount?.message}
            inputStyle={{ paddingRight: 64 }}
            IconRight={
              <TouchableOpacity
                onPress={() => {
                  if (!selectedTicket) return;
                  const max = selectedTicket.amount.toString();
                  const formatted = formatNumericValue(max);
                  form.setValue("tipAmount", formatted);
                }}
                className="bg-black rounded px-2 py-0.5 mr-1"
              >
                <Text className="text-white underline font-bold">Max</Text>
              </TouchableOpacity>
            }
          />
        )}
      />

      <Button
        onPress={() => {
          if (!selectedTicket) return;
          Alert.alert("Tip Sent", `You tipped ${amount} tickets to ${selectedTicket.subjectUser.twitterName}`)
        }}
        disabled={!selectedTicket}
        style={{ marginTop: 16 }}
      >
        Send tip
      </Button>
    </View>
  );
} 
