import { useMemo, useRef, useState } from "react";

import RenderHTML from "react-native-render-html";
import { Href, Link, router, useLocalSearchParams } from "expo-router";
import { Image, Pressable, View } from "react-native";

import { User } from "@/types";
import { useUser } from "@/stores";
import { checkContent } from "@/utils";
import { toast } from "@/components/toast";
import { Colors } from "@/constants/Colors";
import { useGroupByUserIdQuery } from "@/queries";
import { formatAvax } from "@/utils/format-price";
import { TipOutlineIcon } from "@/components/icons";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useQueryClient } from "@tanstack/react-query";
import { useAirdropQuery } from "@/queries/airdrop-queries";
import { abbreviateNumber } from "@/utils/abbreviate-number";
import CustomRenderHtml from "@/components/custom-render-html";
import CustomLinearGradient from "@/components/ui/linear-gradient";
import { useBottomSheet } from "@/components/ui/BottomSheetContext";
import { useFollowMutation, useUnfollowMutation } from "@/queries/follow-mutations";
import { useIsUserBlockedQuery, useUserByHandleQuery } from "@/queries/user-queries";
import { useSharesHoldingsQuery, useSharesStatsQuery } from "@/queries/shares-queries";
import { TradesUsersTrendingResponse } from "@/queries/types/trade-users-trending-response";
import { Skeleton } from "@/components/ui/skaleton";

import { XLogo } from "./x-logo-svg";
import { UserBadges } from "./user-badges";
import BuyTicketModal from "./buy-ticket-modal";
import React from "react";
import ProfileHeaderSkeleton from "./profile-header-skeleton";
import BlockUnblockConfirmSheet from "./block-unblock-confirm-sheet";

export const formatPrice = (price?: string) => {
  if (!price) return "";
  return formatAvax(price);
};

export default function ProfileHeader() {
  const { user } = useUser();
  const { openBottomSheet, closeBottomSheet } = useBottomSheet();

  const navigateToEditProfile = () =>
    router.push(`/${user?.twitterHandle}/edit` as Href<string>);

  const buyTicket = () => {
    openBottomSheet(<BuyTicketModal />, 350);
  };
  const queryClient = useQueryClient();
  const params = useLocalSearchParams() as { userHandle: string };
  const navigateToTipUser = () =>
    router.push(`/(modals)/tip-modal?userHandle=${params.userHandle}` as Href<string>);

  const [isUnblockOpen, setIsUnblockOpen] = useState(false);

  const tabRef = useRef<HTMLDivElement>(null);
  const [isSticked, setIsSticked] = useState(false);
  const [noDuration, setNoDuration] = useState(false);

  const { data, isLoading: isUserDataLoading } = useUserByHandleQuery(
    params.userHandle
  );

  const isMe = useMemo(() => {
    if (!user || !data) return false;

    if (user?.id === data?.user?.id) {
      return true;
    }

    return false;
  }, [data, user]);

  const { data: airdropData, isLoading: isAirdropLoading } = useAirdropQuery({
    // disable the query if it is not the current user's profile
    enabled: isMe,
  });

  const { data: statsData, isLoading: isStatsDataLoading } =
    useSharesStatsQuery({
      userId: data?.user?.id,
    });
  const { data: holdingsData, isLoading: isHoldingsLoading } =
    useSharesHoldingsQuery();
  const { data: isBlocked, isLoading: isBlockedLoading } =
    useIsUserBlockedQuery(data?.user?.id);
  const { data: groupData } = useGroupByUserIdQuery({ userId: data?.user?.id });

  const { mutateAsync: follow } = useFollowMutation({
    onMutate: async () => {
      toast.green(`You're now following ${data?.user.twitterName}!`);
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", params.userHandle],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        params.userHandle,
      ]);
      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);

      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (
          old:
            | {
              user: User;
            }
            | undefined
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: true,
            },
          };
        }
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === data?.user.id) {
                return {
                  ...u,
                  following: true,
                };
              }
              return u;
            }),
          };
        }
      );

      return { previousUser, previousTrendingUsers };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to follow ${data?.user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        context.previousUser
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers
      );
    },
  });
  const { mutateAsync: unfollow } = useUnfollowMutation({
    onMutate: async () => {
      await queryClient.cancelQueries({
        queryKey: ["user", "handle", params.userHandle],
      });
      await queryClient.cancelQueries({
        queryKey: ["trade", "users", "trending"],
      });

      const previousUser = queryClient.getQueryData([
        "user",
        "handle",
        params.userHandle,
      ]);
      const previousTrendingUsers = queryClient.getQueryData([
        "trade",
        "users",
        "trending",
      ]);

      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        (
          old:
            | {
              user: User;
            }
            | undefined
        ) => {
          if (!old) {
            return old;
          }

          return {
            ...old,
            user: {
              ...old.user,
              following: false,
            },
          };
        }
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        (old: TradesUsersTrendingResponse) => {
          if (!old) return old;

          return {
            ...old,
            users: old.users.map((u) => {
              if (u.id === data?.user.id) {
                return {
                  ...u,
                  following: false,
                };
              }
              return u;
            }),
          };
        }
      );

      return { previousUser, previousTrendingUsers };
    },
    onError: (err, variables, context) => {
      toast.danger(`Failed to unfollow ${data?.user.twitterName}.`);
      queryClient.setQueryData(
        ["user", "handle", params.userHandle],
        context.previousUser
      );
      queryClient.setQueryData(
        ["trade", "users", "trending"],
        context?.previousTrendingUsers
      );
    },
  });

  const ticketPrice = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatPrice(
      statsData?.stats?.keyPrice || data?.user?.keyPrice
    );

    return formattedEther;
  }, [
    statsData?.stats?.keyPrice,
    data?.user?.keyPrice,
    isUserDataLoading,
    isStatsDataLoading,
  ]);

  const volume = useMemo(() => {
    if (isUserDataLoading || isStatsDataLoading) return "";

    const formattedEther = formatPrice(statsData?.stats?.volume ?? "0");

    return formattedEther;
  }, [isStatsDataLoading, isUserDataLoading, statsData?.stats?.volume]);

  const isHoldingUserTicket = useMemo(() => {
    if (isHoldingsLoading && !holdingsData && isUserDataLoading) return false;

    if (!data?.user?.id) return false;

    if (!holdingsData?.holdings) return false;

    try {
      const holding = holdingsData?.holdings.find((h) => {
        if (!h) return false;
        if (!h.subjectUser) return false;
        if (!data?.user?.id || !h?.subjectUser?.id) return false;
        return h?.subjectUser?.id === data?.user?.id;
      });

      return (
        Boolean(holding) &&
        Boolean(holding?.amount) &&
        parseInt(holding ? holding.amount : "0") > 0
      );
    } catch (error) {
      console.log("error", error);
      return false;
    }
  }, [data, holdingsData, isHoldingsLoading, isUserDataLoading]);

  const profileDescription = useMemo(() => {
    const [content] = checkContent({
      content: data?.user?.twitterDescription ?? "",
      truncate: false,
    });

    return content;
  }, [data?.user?.twitterDescription]);

  const followersCount = abbreviateNumber(data?.user?.followerCount ?? 0);
  const followingsCount = abbreviateNumber(data?.user?.followingsCount ?? 0);
  const twitterFollowers = abbreviateNumber(data?.user?.twitterFollowers ?? 0);

  const handleFollow = () => {
    if (!data) return;

    if (data?.user?.following) {
      unfollow({ userId: data.user.id });
    } else {
      follow({ userId: data.user.id });
    }
  };

  const goBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push("/home" as Href<string>);
    }
  };

  const shareProfile = () => { };

  if (isUserDataLoading || isStatsDataLoading || isHoldingsLoading) {
    return <ProfileHeaderSkeleton />;
  }

  return (
    <View>
      <View className="aspect-[3/1]">
        <Image
          className="w-full h-full object-cover"
          source={{ uri: data?.user?.bannerUrl }}
        />
      </View>
      <View className="px-5">
        <View className="flex-row items-center gap-[25px] mb-2.5">
          <View className="h-[90px] -mt-10 rounded-full overflow-hidden border border-white">
            <Image
              className="h-full aspect-square"
              source={{ uri: data?.user?.twitterPicture }}
            />
          </View>
          <Pressable onPress={() => router.push(`/${params.userHandle}/connections?tab=followers` as Href<string>)}>
            <ThemedText className="text-[14px] text-gray-text">Followers</ThemedText>
            <ThemedText>{followersCount}</ThemedText>
          </Pressable>
          <Pressable onPress={() => router.push(`/${params.userHandle}/connections?tab=following` as Href<string>)}>
            <ThemedText className="text-[14px] text-gray-text">Following</ThemedText>
            <ThemedText>{followingsCount}</ThemedText>
          </Pressable>
          {isMe ? (
            <View>
              <ThemedText className="text-[14px] text-gray-text">Rank</ThemedText>
              <ThemedText>{airdropData?.airdrop?.rank || "N/A"}</ThemedText>
            </View>
          ) : (
            <Link href={`https://twitter.com/${data?.user?.twitterHandle}`}>
              <View>
                <View className="py-1 text-gray-text mt-1">
                  <XLogo color={Colors.dark.greyText} />
                </View>

                <ThemedText>{twitterFollowers}</ThemedText>
              </View>
            </Link>
          )}
        </View>
        <View className="mb-5 mt-2">
          <ThemedView className="flex-row items-center gap-x-1.5 text-off-white">
            <ThemedText type="bold" className="text-xm font-medium">
              {data?.user?.twitterName}
            </ThemedText>
            {statsData?.badges && <UserBadges badges={statsData?.badges} />}
          </ThemedView>
          <ThemedText className="text-xs text-gray-text">
            @{data?.user?.twitterHandle}
          </ThemedText>
        </View>
        <ThemedView className="mb-5">
          <CustomRenderHtml html={profileDescription} />
        </ThemedView>
        <View className="flex-row justify-between items-center">
          <View className="flex flex-row gap-x-3 justify-center">
            {isMe ? (
              <>
                <CustomLinearGradient
                  onPress={buyTicket}
                  style={{ borderWidth: 0.5, borderRadius: 20, width: '48%', alignItems: 'center', paddingVertical: 6, marginBottom: 20 }}
                  colors={["#D64C05", "#FF5626"]}
                >
                  <View className="flex flex-row gap-x-1 justify-center">
                    <ThemedText type="bold" className="text-sm font-semibold">
                      {isHoldingUserTicket ? "Trade Ticket" : "Buy Ticket"}
                    </ThemedText>
                    <Image  className="brightness-110 grayscale mt-1 ml-0.5 h-3 w-3" source={require("@assets/coins/avax_grey.png")} />
                    <ThemedText type ="bold" className="text-xs font-medium leading-5 text-off-white">{ticketPrice}</ThemedText>
                  </View>
                </CustomLinearGradient>
                <Pressable
                  onPress={navigateToEditProfile}
                  className="border border-[#AAAAAA] rounded-[20px] items-center py-1.5 mb-5 w-[48%]"
                >
                  <ThemedText>Edit Profile</ThemedText>
                </Pressable>
              </>
            ) : (
              <>
                <CustomLinearGradient
                  onPress={isBlocked ? undefined : buyTicket}
                  style={{ borderWidth: 0.5, borderRadius: 20, width: '45%', alignItems: 'center', paddingVertical: 8, marginBottom: 20, ...(isBlocked ? { opacity: 0.5 } : {}) }}
                  colors={["#D64C05", "#FF5626"]}
                >
                  <View className="flex flex-row gap-x-1 justify-center">
                    <ThemedText type="bold" className="text-sm font-semibold">
                      {isHoldingUserTicket ? "Trade Ticket" : "Buy Ticket"}
                    </ThemedText>
                    <Image  className="brightness-110 grayscale mt-1 ml-0.5 h-3 w-3" source={require("@assets/coins/avax_grey.png")} /> 
                    <ThemedText type ="bold"  className="text-xs  leading-5 text-off-white">{ticketPrice}</ThemedText>
                  </View>
                </CustomLinearGradient>

                {isBlocked ? (
                  <Pressable
                    onPress={() => setIsUnblockOpen(true)}
                    className={`border rounded-[20px] items-center py-1.5 mb-5 w-[32%] border-[#AAAAAA] bg-[#FF3B30]`}
                  >
                    <ThemedText type="bold" className="text-sm font-semibold text-white">
                      Blocked
                    </ThemedText>
                  </Pressable>
                ) : (
                  <Pressable
                    onPress={handleFollow}
                    className={`border rounded-[20px] items-center py-1.5 mb-5 w-[32%] ${!data?.user?.following ? 'border-brand-orange' : 'border-[#AAAAAA]'}`}
                  >
                    <ThemedText type="bold" className="text-sm font-semibold">
                      {data?.user?.following ? "Unfollow" : "Follow"}
                    </ThemedText>
                  </Pressable>
                )}

                <Pressable
                  className={`rounded-full border border-[#AAAAAA] self-start p-2 items-center justify-center${isBlocked ? ' opacity-50' : ''}`}
                  onPress={isBlocked ? undefined : navigateToTipUser}
                  disabled={isBlocked}
                >
                  <TipOutlineIcon
                    color={Colors.dark.lightgrey}
                    width={18}
                    height={18}
                  />
                </Pressable>
                <BlockUnblockConfirmSheet
                  open={isUnblockOpen}
                  mode="unblock"
                  username={data?.user?.twitterHandle || ""}
                  userId={data?.user?.id || ""}
                  onClose={() => setIsUnblockOpen(false)}
                />
              </>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}
