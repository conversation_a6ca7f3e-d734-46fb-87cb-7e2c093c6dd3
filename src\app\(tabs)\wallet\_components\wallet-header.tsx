import { EllipsisHorizontalFilledIcon } from "@/components/icons";
import { ThemedText } from "@/components/ThemedText";
import Avatar from "@/components/ui/avatar";
import { useBottomSheet } from "@/components/ui/BottomSheetContext";
import { Colors } from "@/constants/Colors";
import { Pressable, StyleSheet, View } from "react-native";
import { MoreOptionsModal } from "./more-options-modal";
import { useUser } from "@/stores";

export const WalletHeader = () => {
  const { user } = useUser();
  const { openBottomSheet } = useBottomSheet();

  const moreOptions = () => {
    openBottomSheet(<MoreOptionsModal />, 380);
  };

  return (
    <View style={styles.header}>
      <View style={styles.headerProfile}>
        <Avatar
          size={35}
          src={user?.twitterPicture}
        />
        <View>
          <ThemedText type="defaultSemiBold" style={styles.username}>
            {user?.twitterName}
          </ThemedText>
          <ThemedText style={styles.xHandle}>@{user?.twitterHandle}</ThemedText>
        </View>
      </View>
      <Pressable onPress={moreOptions}>
        <EllipsisHorizontalFilledIcon
          height={20}
          width={20}
          fill={Colors.dark.white}
        />
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20
  },
  headerProfile: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },
  username: {
    fontSize: 14,
    color: Colors.dark.white,
  },
  xHandle: {
    fontSize: 12,
    color: Colors.dark.lightgrey,
  },
});
