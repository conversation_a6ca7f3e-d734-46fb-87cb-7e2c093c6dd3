export interface Me {
  id: string;
  twitterId: string;
  twitterHandle: string;
  twitterName: string;
  twitterPicture: string;
  bannerUrl: string | null;
  address: string;
  prevAddress: string | null;
  addressConfirmed: boolean;
  twitterDescription: string;
  signedUp: boolean;
  subscriptionCurrency: string;
  subscriptionCurrencyAddress: string | null;
  subscriptionPrice: string;
  keyPrice: string;
  lastKeyPrice: string;
  subscriptionsEnabled: boolean;
  userConfirmed: boolean;
  twitterConfirmed: boolean;
  flag: number;
  ixHandle: string;
  handle: string;
  threadCount: number;
  followerCount: number;
  followingsCount: number;
  twitterFollowers: number;
  createdOn: string;
  isMod: boolean;
  ethereumAddress: string | null;
  solanaAddress: string | null;
}
