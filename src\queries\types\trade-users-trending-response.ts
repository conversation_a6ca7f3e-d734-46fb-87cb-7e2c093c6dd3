interface Badge {
  id: number;
  userId: string;
  badgeType: number;
  order: number;
}

interface Stats {
  buys: number;
  sells: number;
  feesPaid: string;
  feesEarned: string;
  amountSpent: string;
  amountEarned: string;
  referralsEarned: string;
  distributedToShareholders: string;
  badgeType: number;
  id: string;
  userId: string;
  supply: string;
  volume: string;
  keyPrice: string;
}

export interface User {
  ranking: number;
  threadCount: number;
  followerCount: number;
  followingsCount: number;
  twitterFollowers: number;
  id: string;
  createdOn: string;
  twitterId: string;
  twitterHandle: string;
  twitterName: string;
  twitterPicture: string;
  bannerUrl: string | null;
  address: string;
  ethereumAddress: string | null;
  prevAddress: string | null;
  addressConfirmed: boolean;
  twitterDescription: string;
  signedUp: boolean;
  subscriptionCurrency: string;
  subscriptionCurrencyAddress: null | string;
  subscriptionPrice: string;
  keyPrice: string;
  lastKeyPrice: string;
  subscriptionsEnabled: boolean;
  userConfirmed: boolean;
  twitterConfirmed: boolean;
  flag: number;
  ixHandle: string;
  handle: null | string;
  stats: Stats;
  badges: Badge[];
  following:
    | {
        id: number;
        createdOn: string;
        modifiedOn: string;
        followerId: string;
        followingId: string;
        followingActionTs: string | null;
        confirmed: string | null;
      }
    | boolean
    | null;
}

export interface TradesUsersTrendingResponse {
  users: User[];
}
