import { ImageSourcePropType, StyleSheet, TouchableOpacity, View } from "react-native";
import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import Avatar, { AvatarFallBack, LocalAvatar } from "@/components/ui/avatar";
import { ThemedText } from "@/components/ThemedText";
import { Colors } from "@/constants/Colors";
import { Href, router } from "expo-router";
import { Nullable, Undefined } from "@/types/common";
import { useUserByIdQuery } from "@/queries";
import { UserFlaggedEnum } from "@/types";
import { PinOutlineIcon, SuspendedUserChatPreviewOutlineIcon } from "@/components/icons";
import { cleanMessage, formatTimeDistance, getBadgeImage } from "@/utils";
import { useUser } from "@/stores";
import { IGroupInfo, useMessaging } from "../_contexts/messaging-context";
import { paddingHorizontal } from "../(top-tabs)/_layout";

const AVATAR_SIZE = 42;

interface ListItemProps {
  groupId: string;
  groupName: string;
  groupImageUrl: string;
  isDirectMessage: boolean;
  lastMessageByName: Nullable<string>;
  lastMessageOnDate: Nullable<number>;
  lastMessage: Nullable<string>;
  lastUserId: Nullable<string>;
  lastSeen: Undefined<number>;
  ownerUserId: string;
  isPinned: boolean;
  setSeen: (groupId: string, date: number) => void;
  selectedGroupId: string;
  setSelectedGroup: (group: Nullable<IGroupInfo>) => void;
  chatMateId: Nullable<string>;
  chatmateName: Nullable<string>;
}

const ListItem = memo(({
  groupId,
  groupName,
  isDirectMessage,
  groupImageUrl,
  lastMessageByName,
  lastMessageOnDate,
  lastMessage,
  lastUserId,
  lastSeen,
  ownerUserId,
  isPinned,
  setSeen,
  selectedGroupId,
  setSelectedGroup,
  chatMateId,
  chatmateName
}: ListItemProps) => {
  const owner = useUserByIdQuery(ownerUserId);
  const { user } = useUser();
  const [messageItemHeight, setMessageItemHeight] = useState<number>(0);
  const chatMate = useUserByIdQuery(chatMateId || "");
  const { setIsSelectedDirectMessage } = useMessaging();

  const [time, setTime] = useState<string>('');
  let interval = useRef<any>(null);

  const isSuspended = useMemo(() => {
    if (isDirectMessage && ownerUserId === user?.id)
      return chatMate?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
    else
      return owner?.data?.user?.flag === UserFlaggedEnum.SUSPENDED;
  }, [owner?.data?.user?.flag, chatMate?.data?.user?.flag, ownerUserId, user?.id, isDirectMessage])

  const typingUser = '';

  const isLastMessageMine = useMemo(() => {
    return lastUserId === user?.id;
  }, [lastUserId, user])

  const lastMessagePreview = useMemo(() => {
    return lastMessage ? cleanMessage(lastMessage || '') : ''
  }, [lastMessage])

  const lastMessageUserName = useMemo(() => {
    return isLastMessageMine ? "You" : lastMessageByName;
  }, [isLastMessageMine, lastMessageByName])

  const directMessagePreview = useMemo(() => {
    //if its 1:1 chat, show the last message preview
    return isLastMessageMine && lastMessagePreview ? `You: ${lastMessagePreview}` : `${lastMessagePreview}`;
  }, [isLastMessageMine, lastMessagePreview])

  const messagePreview = useMemo(() => {
    return isSuspended ? 'Account suspended.' :
      typingUser ? `${typingUser} is typing...` :
        isDirectMessage ? directMessagePreview :
          lastMessageUserName && lastMessagePreview ? `${lastMessageUserName}: ${lastMessagePreview}` : ''
  }, [isSuspended, typingUser, lastMessageUserName, lastMessagePreview, isDirectMessage, directMessagePreview])

  const name = useMemo(() => {
    if (isDirectMessage) {
      if (isSuspended) return 'Suspended Account';
      console.log(ownerUserId, user?.id)
      if (ownerUserId === user?.id) return chatmateName;
      return groupName;
    } else {
      return groupName;
    }
  }, [isDirectMessage, isSuspended, ownerUserId, user?.id, chatmateName, groupName])

  const profilePicture = useMemo(() => {
    if (isDirectMessage && ownerUserId === user?.id) {
      return chatMate?.data?.user?.twitterPicture || "";
    } else {
      return groupImageUrl;
    }
  }, [isDirectMessage, ownerUserId, user?.id, chatMate?.data?.user?.twitterPicture, groupImageUrl])

  const [isSelected, setIsSelected] = useState<boolean>(false);

  useEffect(() => {
    if (selectedGroupId === groupId) {
      setIsSelected(true);
    } else {
      setIsSelected(false);
    }
  }, [selectedGroupId, groupId])

  const lastMessageDate = useMemo(() => {
    return lastMessageOnDate
      ? new Date(+lastMessageOnDate)
      : null;
  }, [lastMessageOnDate])

  const lastSeenDate = useMemo(() => {
    return lastSeen
      ? new Date(+lastSeen)
      : null;
  }, [lastSeen]);

  const isNotSeen = useMemo(() => {
    return isLastMessageMine
      ? false
      : lastMessageDate && lastSeenDate
        ? lastMessageDate > lastSeenDate
        : false;
  }, [isLastMessageMine, lastMessageDate, lastSeenDate]);

  useEffect(() => {
    if (lastMessageOnDate === null) {
      return;
    }
    //clear prev interval and then start new interval
    if (interval.current !== null) {
      clearInterval(interval.current);
      interval.current = null;
    }
    interval.current = setInterval(() => {
      setTime(formatTimeDistance(lastMessageOnDate));
    }, 1000);
    return () => {
      if (interval.current !== null) {
        clearInterval(interval.current);
        interval.current = null;
      }
    };
  }, [lastMessageOnDate]);

  const handlePress = () => {
    if (isSelected) {
      setSelectedGroup(null); 
      setIsSelected(false);
    } else {
      setSeen(groupId, Date.now());
      router.push(`/chat/${groupId}` as Href<string>);
    }
  }

  const handleLongPress = () => {
      setIsSelected(true);
      setSelectedGroup({ id: groupId, isPinned: isPinned });
      if(isDirectMessage) {
        setIsSelectedDirectMessage(true);
      } else {
        setIsSelectedDirectMessage(false);
      }
  }

  return (
    <TouchableOpacity
      onPress={handlePress}
      activeOpacity={0.7}
      onLayout={(event) => {
        setMessageItemHeight(event.nativeEvent.layout.height);
      }}
      onLongPress={handleLongPress}
      style={[
        styles.messageItemBox,
        isSelected && { backgroundColor: "#212121" },
      ]}
    >
      {isNotSeen ? <View style={[styles.notSeenIndicator, { top: messageItemHeight / 2 }]} /> : null}
      <View style={styles.leftSection}>
        {isSuspended ? (
          <SuspendedUserChatPreviewOutlineIcon height={AVATAR_SIZE} width={AVATAR_SIZE} />
        ) : getBadgeImage(groupName) ? (
          <LocalAvatar avatarSize={AVATAR_SIZE} src={getBadgeImage(groupName) as ImageSourcePropType} />
        ) : profilePicture ? (
          <Avatar size={AVATAR_SIZE} src={profilePicture} />
        ) : <AvatarFallBack avatarSize={AVATAR_SIZE} />
        }
      </View>
      <View style={styles.middleSection}>
        <ThemedText numberOfLines={1} style={styles.groupName}>{name}</ThemedText>
        {
          messagePreview ? (
            <ThemedText
              ellipsizeMode="tail"
              numberOfLines={1}
              type="defaultGrey"
            >
              {messagePreview}
            </ThemedText>
          ) : null
        }
      </View>
      <View style={styles.rightSection}>
        {
          time ? (
            <ThemedText style={styles.messageTime}>{time}</ThemedText>
          ) : null
        }
        {
          isPinned ? (
            <PinOutlineIcon color={Colors.dark.lightGreyText} height={18} width={18} />
          ) : null
        }
      </View>
    </TouchableOpacity>
  );
}, (prevProps, nextProps) => {
  return (
    //don't re-render if the props are the same
    prevProps.isPinned === nextProps.isPinned &&
    prevProps.lastMessageOnDate === nextProps.lastMessageOnDate &&
    prevProps.lastSeen === nextProps.lastSeen &&
    prevProps.lastMessage === nextProps.lastMessage &&
    prevProps.lastUserId === nextProps.lastUserId &&
    prevProps.ownerUserId === nextProps.ownerUserId &&
    prevProps.groupId === nextProps.groupId &&
    prevProps.groupName === nextProps.groupName &&
    prevProps.isDirectMessage === nextProps.isDirectMessage &&
    prevProps.groupImageUrl === nextProps.groupImageUrl &&
    prevProps.lastMessageByName === nextProps.lastMessageByName &&
    prevProps.chatMateId === nextProps.chatMateId &&
    prevProps.chatmateName === nextProps.chatmateName
  );
});

export default ListItem;

const styles = StyleSheet.create({
  messageItemBox: {
    flexDirection: "row",
    paddingVertical: 15,
    width: '100%',
    paddingHorizontal: paddingHorizontal,
  },
  leftSection: {
    width: '15%',
  },
  middleSection: {
    gap: 3,
    width: '70%',
    justifyContent: 'center',
  },
  groupName: {
    fontSize: 14,
    color: Colors.dark.offWhite,
    fontWeight: "700",
  },
  groupMessage: {
    fontSize: 14,
    color: Colors.dark.lightGreyText,
  },
  messageTime: {
    color: Colors.dark.lightGreyText,
    fontSize: 12,
  },
  notSeenIndicator: {
    width: 4,
    height: 4,
    borderRadius: 4,
    backgroundColor: Colors.dark.brandOrange,
    position: "absolute",
    left: paddingHorizontal / 2,
  },
  rightSection: {
    gap: 3,
    justifyContent: 'center',
    width: '15%',
    alignItems: 'flex-end',
  }
});
