import { ActivityIndicator, FlatList, RefreshControl, StyleSheet, View } from "react-native";
import React, { memo, useCallback, useState } from "react";
import { Colors } from "@/constants/Colors";
import { ListSkeleton } from "./skeleton";
import { ThemedText } from "@/components/ThemedText";
import { globalStyles } from "@/app/globalStyles";
import { Nullable, Undefined } from "@/types/common";
import { Group, GroupsResponse } from "@/queries/types";
import { FetchNextPageOptions, InfiniteData, InfiniteQueryObserverResult, QueryObserverResult, RefetchOptions } from "@tanstack/react-query";
import ListItem from "./list-item";
import { IGroupInfo } from "../_contexts/messaging-context";

interface GroupListProps {
    isLoading: boolean;
    data: Undefined<Group[]>;
    refetch: (options?: RefetchOptions) => Promise<QueryObserverResult<InfiniteData<GroupsResponse, unknown>, Error>>,
    hasNextPage: boolean;
    fetchNextPage: (options?: FetchNextPageOptions) => Promise<InfiniteQueryObserverResult<InfiniteData<GroupsResponse, unknown>, Error>>,
    isFetchingNextPage: boolean;
    setSeen: (groupId: string, date: number) => void
    emptyListText: string;
    selectedGroupId: string;
    setSelectedGroup: (group: Nullable<IGroupInfo>) => void
}

const GroupList = memo(({
    isLoading,
    data,
    refetch,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    setSeen,
    emptyListText,
    selectedGroupId,
    setSelectedGroup
}: GroupListProps) => {
    const [refreshing, setRefreshing] = useState(false);

    const handleRefresh = async () => {
        setRefreshing(true);
        try {
            await refetch();
        } catch (error) {
        } finally {
            setRefreshing(false);
        }
    };

    const onEndReached = () => {
        if (!isFetchingNextPage) {
            fetchNextPage();
        }
    }

    const renderItem = useCallback(({ item }: { item: Group }) => (
        <ListItem
            groupId={item.id}
            groupName={item.name}
            isDirectMessage={item.isDirect || false}
            groupImageUrl={item.profilePictureUrl}
            lastMessageByName={item.lastName}
            lastMessageOnDate={item.lastMessageOn}
            lastMessage={item.lastMessage}
            lastUserId={item.lastUserId}
            lastSeen={item.memberLink?.lastSeen}
            ownerUserId={item.ownerUserId}
            isPinned={item.memberLink?.isPinned || false}
            chatMateId={item.chatMateId || null}
            chatmateName={item.chatMateName || null}
            setSeen={setSeen}
            selectedGroupId={selectedGroupId}
            setSelectedGroup={setSelectedGroup}
        />
    ), [setSeen, selectedGroupId, setSelectedGroup])

    return isLoading ? (
        <ListSkeleton />
    ) : data && data.length > 0 ? (
        <FlatList
            data={data}
            style={{ flex: 1 }}
            renderItem={renderItem}
            onEndReached={onEndReached}
            onEndReachedThreshold={0.7}
            keyExtractor={(item) => item.id}
            refreshControl={
                <RefreshControl
                    refreshing={refreshing}
                    onRefresh={handleRefresh}
                    colors={[Colors.dark.brandOrange]}
                />
            }
            ListFooterComponent={
                isFetchingNextPage || hasNextPage ? (
                    <ActivityIndicator size="large" color={Colors.dark.brandOrange} />
                ) : null
            }
        />
    ) : (
        <View style={styles.noDataContainer}>
            <ThemedText style={styles.defaultText}>
                {emptyListText}
            </ThemedText>
        </View>
    )
}, (prevProps, nextProps) => {
    return prevProps.isLoading === nextProps.isLoading
        && JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data)
        && prevProps.refetch === nextProps.refetch
        && prevProps.hasNextPage === nextProps.hasNextPage
        && prevProps.fetchNextPage === nextProps.fetchNextPage
        && prevProps.isFetchingNextPage === nextProps.isFetchingNextPage
        && prevProps.setSeen === nextProps.setSeen
        && prevProps.emptyListText === nextProps.emptyListText
        && prevProps.selectedGroupId === nextProps.selectedGroupId
        && prevProps.setSelectedGroup === nextProps.setSelectedGroup
});

export default GroupList;

const styles = StyleSheet.create({
    noDataContainer: {
        ...globalStyles.centerAligned,
        height: '100%',
    },
    defaultText: {
        fontSize: 15,
        marginHorizontal: "auto",
        marginVertical: 20,
        fontFamily: "InterSemiBold",
        fontWeight: "700",
    },
});
