import { globalStyles } from "@/app/globalStyles"
import { PinOutlineIcon, UnpinOutlineIcon } from "@/components/icons"
import BackButton from "@/components/navigation/BackButton"
import { Colors } from "@/constants/Colors"
import { View, StyleSheet, TouchableOpacity } from "react-native"
import { useMessaging } from "../_contexts/messaging-context"
import { usePinConversation } from "@/queries/chat-mutations"
import { InfiniteData } from "@tanstack/react-query"
import { useQueryClient } from "@tanstack/react-query"
import { GroupsResponse } from "@/queries/types/chats"

export const GroupSelectionHeader = () => {
    const { setSelectedGroup, selectedGroup, searchValue, isSelectedDirectMessage } = useMessaging();
    const queryClient = useQueryClient();

    const conversationQueryKey: string = isSelectedDirectMessage ? "direct-messages" : "conversations";
    const searchConversationQueryKey: string = isSelectedDirectMessage ? "search-dm-conversations" : "search-conversations";

    const { mutateAsync: pinConversation } = usePinConversation({
        onMutate: async ({ groupId, isPinned }) => {
          const previousData = queryClient.getQueryData(["chat", conversationQueryKey]);
          const previousSearchData = queryClient.getQueryData([
            "chat",
            searchConversationQueryKey,
            searchValue,
          ]);
    
          queryClient.setQueryData(
            ["chat", searchConversationQueryKey, searchValue],
            (oldData: InfiniteData<GroupsResponse>) => {
              if (!oldData) return oldData;
    
              return {
                ...oldData,
                pages: oldData.pages.map((page) => {
                  return {
                    ...page,
                    groups: page.groups.map((g) => {
                      if (g.id === groupId) {
                        return {
                          ...g,
                          memberLink: {
                            ...g.memberLink,
                            isPinned,
                          },
                        };
                      }
                      return g;
                    }),
                  };
                }),
              };
            },
          );
          queryClient.setQueryData(
            ["chat", conversationQueryKey],
            (oldData: InfiniteData<GroupsResponse>) => {
              if (!oldData) return oldData;
    
              return {
                ...oldData,
                pages: oldData.pages.map((page) => {
                  return {
                    ...page,
                    groups: page.groups.map((g) => {
                      if (g.id === groupId) {
                        return {
                          ...g,
                          memberLink: {
                            ...g.memberLink,
                            isPinned,
                          },
                        };
                      }
                      return g;
                    }),
                  };
                }),
              };
            },
          );
          setSelectedGroup({
            id: groupId,
            isPinned: isPinned,
          });
          return { previousData, previousSearchData };
        },
        onError: (err, variables, context) => {
          queryClient.setQueryData(["chat", conversationQueryKey], context.previousData);
          queryClient.setQueryData(
            ["chat", searchConversationQueryKey, searchValue],
            context.previousSearchData,
          );
        },
      });

    const handlePinPress = async () => {
        if (!selectedGroup?.id){
          setSelectedGroup(null);
          return;
        }
        await pinConversation({
            groupId: selectedGroup?.id,
            isPinned: !selectedGroup?.isPinned,
        });
        setSelectedGroup(null);
    }

    return (
        <View style={styles.headerWrapper}>
            <BackButton onPress={() => {
                setSelectedGroup(null);
            }}/>
            <TouchableOpacity style={styles.pinIconContainer} onPress={handlePinPress}>
                {
                    selectedGroup?.isPinned ?
                        <UnpinOutlineIcon color={Colors.dark.white} width={20} height={20} />
                        :
                        <PinOutlineIcon color={Colors.dark.white} width={20} height={20} />
                }
            </TouchableOpacity>
        </View>
    )
}

const styles = StyleSheet.create({
    headerWrapper: {
        ...globalStyles.rowSpaceBetween,
        flexDirection: 'row',
    },
    pinIconContainer: {
        paddingHorizontal: 10,
        paddingVertical: 10,
    }
})