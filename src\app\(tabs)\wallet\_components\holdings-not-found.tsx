import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Button } from "@/components/ui/button";
import { Colors } from "@/constants/Colors";
import { Link, router } from "expo-router";
import { StyleSheet, Dimensions } from "react-native";

const { height, width } = Dimensions.get("window");

export const HoldingsNotFound = () => {
  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.font} className="text-sm font-semibold">
        You don't own any tickets yet!
      </ThemedText>
      <ThemedText type="greyText" style={styles.alignCenter}>
        Explore the top and trending users and begin your journey at The Arena!
      </ThemedText>
        <Button onPress={() => router.push("/explore")} style={styles.button} >Explore</Button>
    </ThemedView>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    height: height / 3,
  },
  alignCenter: {
    textAlign: "center",
    width: width / 1.5,
  },
  button: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: Colors.dark.brandOrange,
    paddingHorizontal: 25,
    paddingVertical: 10,
    marginTop: 13
  },
  font: {
    fontFamily: "InterSemibold",
  },
});
